#!/usr/bin/env python3
"""
Script pour ajouter des données supplémentaires au système de courrier
Modifiez ce script pour ajouter vos propres données
"""

import requests
import json
from datetime import datetime, date

BASE_URL = "http://127.0.0.1:3000"

def ajouter_courriers_arrives():
    """Ajouter des courriers arrivés supplémentaires"""
    print("📥 Ajout de courriers arrivés supplémentaires...")
    
    # Vous pouvez modifier ces données selon vos besoins
    courriers = [
        {
            "id": "1461",
            "urgence": "routine",
            "nature": "message",
            "date_arrivee": "2024-01-21",
            "date_signature": "2024-01-20",
            "numero_ecrit": "14559",
            "expediteur": "4° Bureau",
            "objet": "Demande de formation sur le nouveau système",
            "classification": "public",
            "annotation": "Formation prévue",
            "divisions_action": ["instruction"],
            "divisions_info": ["rh", "technique"]
        },
        {
            "id": "1462",
            "urgence": "urgent",
            "nature": "note_royale",
            "date_arrivee": "2024-01-22",
            "date_signature": "2024-01-21",
            "numero_ecrit": "14560",
            "expediteur": "5° Bureau",
            "objet": "Directive sur l'utilisation des nouvelles technologies",
            "classification": "restreint",
            "annotation": "Application immédiate",
            "divisions_action": ["technique", "informatique"],
            "divisions_info": ["planification"]
        }
    ]
    
    for courrier in courriers:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/arrives",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def ajouter_courriers_envoyes():
    """Ajouter des courriers envoyés supplémentaires"""
    print("\n📤 Ajout de courriers envoyés supplémentaires...")
    
    # Vous pouvez modifier ces données selon vos besoins
    courriers = [
        {
            "id": "CE-1461",
            "numero_ecrit": "ENV-14559",
            "division_emettrice": "instruction",
            "date_depart": "2024-01-23",
            "nature": "message",
            "objet": "Programme de formation établi",
            "destinataire": "4° Bureau",
            "observations": "Formation programmée pour la semaine prochaine"
        },
        {
            "id": "CE-1462",
            "numero_ecrit": "ENV-14560",
            "division_emettrice": "technique",
            "date_depart": "2024-01-24",
            "nature": "note_royale",
            "objet": "Accusé de réception de la directive",
            "destinataire": "5° Bureau",
            "observations": "Directive reçue et mise en application"
        }
    ]
    
    for courrier in courriers:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/envoyes",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("➕ AJOUT DE DONNÉES SUPPLÉMENTAIRES")
    print("=" * 50)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Ajouter les données
    ajouter_courriers_arrives()
    ajouter_courriers_envoyes()
    
    print("\n" + "=" * 50)
    print("✅ AJOUT TERMINÉ")
    print("=" * 50)
    
    print("\n🌐 Vérifiez dans l'interface web:")
    print(f"   • Courriers arrivés: {BASE_URL}/courrier/arrives")
    print(f"   • Courriers envoyés: {BASE_URL}/courrier/envoyes")

if __name__ == "__main__":
    main()
