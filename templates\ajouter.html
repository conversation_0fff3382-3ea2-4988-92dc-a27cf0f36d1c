{% extends "base.html" %}

{% block title %}Enregistrer une Panne{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-tools me-2"></i>Enregistrer une Nouvelle Panne
                </h4>

            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <!-- Champs cachés pour stocker les informations du véhicule -->
                    <input type="hidden" id="vehicule_found" name="vehicule_found" value="false">
                    <input type="hidden" id="vehicule_type" name="vehicule_type" value="">
                    <input type="hidden" id="vehicule_marque" name="vehicule_marque" value="">
                    <input type="hidden" id="vehicule_unite" name="vehicule_unite" value="">


                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type_vehicule" class="form-label">
                                    <i class="fas fa-truck me-1"></i>Type de Véhicule*
                                </label>
                                <select class="form-select" id="type_vehicule" name="type_vehicule" required>
                                    <option value="">Sélectionnez un type</option>
                                    {% for type in types_vehicules.keys() %}
                                    <option value="{{ type }}">{{ type }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un type de véhicule
                                </div>
                            </div>
                        </div>

                        <!-- ✅ Nouveau champ unité avec valeurs dynamiques -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unite" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Unité du Véhicule*
                                </label>
                                <select class="form-select" id="unite" name="unite" required>
                                    <option value="">Sélectionnez une unité</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une unité
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="marque" class="form-label">
                                    <i class="fas fa-building me-1"></i>Marque*
                                </label>
                                <select class="form-select" id="marque" name="marque" required disabled>
                                    <option value="">Sélectionnez d'abord un type</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une marque
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="modele" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>Matricule* <span class="badge bg-info">Peut être un matricule existant</span>
                                </label>
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" id="modele" name="modele" required placeholder="Entrez le matricule...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchMatriculeBtn">
                                        <i class="fas fa-search"></i> Vérifier
                                    </button>
                                </div>

                                <div class="invalid-feedback">
                                    Veuillez entrer le matricule du véhicule
                                </div>
                                <!-- Le résultat sera affiché dans une modal -->
                                <!-- Bouton caché qui sera cliqué par JavaScript pour ouvrir la modal -->
                                <button type="button" id="openModalBtn" class="d-none" data-bs-toggle="modal" data-bs-target="#vehiculeInfoModal"></button>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type_panne" class="form-label">
                                    <i class="fas fa-wrench me-1"></i>Type de Panne*
                                </label>
                                <select class="form-select" id="type_panne" name="type_panne" required disabled onchange="toggleCustomPanneInput()">
                                    <option value="">Sélectionnez d'abord un type de véhicule</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner ou spécifier un type de panne
                                </div>
                                <div id="customPanneContainer" class="mt-2" style="display: none;">
                                    <label for="custom_panne" class="form-label">
                                        <i class="fas fa-edit me-1"></i>Précisez le type de panne*
                                    </label>
                                    <input type="text" class="form-control" id="custom_panne" name="custom_panne" disabled>
                                    <div class="invalid-feedback">
                                        Veuillez saisir le type de panne
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_panne" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de la Panne*
                                </label>
                                <input type="date" class="form-control" id="date_panne" name="date_panne" required>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner la date de la panne
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="statut" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Statut*
                                </label>
                                <select class="form-select" id="statut" name="statut" required>
                                    <option value="En panne" selected>En panne</option>
                                    <option value="Indisponible">Indisponible</option>
                                    <option value="En réparation">En réparation</option>
                                    <option value="Réparé">Réparé</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un statut
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            // Vérifier si "Autre (précisez)" est sélectionné
            const typePanneSelect = document.getElementById('type_panne');
            const customPanneInput = document.getElementById('custom_panne');
            
            if (typePanneSelect && typePanneSelect.value === 'autre' && customPanneInput) {
                // Si "Autre (précisez)" est sélectionné, remplacer la valeur par celle du champ personnalisé
                if (customPanneInput.value.trim() === '') {
                    // Si le champ personnalisé est vide, empêcher la soumission
                    event.preventDefault();
                    event.stopPropagation();
                    customPanneInput.classList.add('is-invalid');
                    return;
                }
                // Créer un champ caché avec la valeur personnalisée
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'type_panne';
                hiddenInput.value = customPanneInput.value.trim();
                form.appendChild(hiddenInput);
                
                // Désactiver le select original pour qu'il ne soit pas soumis
                typePanneSelect.disabled = true;
            }

            // Réactiver temporairement les champs désactivés pour qu'ils soient inclus dans la soumission
            const disabledFields = form.querySelectorAll('select:disabled, input:disabled');
            disabledFields.forEach(field => {
                // Stocker l'état désactivé pour le restaurer après la validation
                field.setAttribute('data-was-disabled', 'true');
                field.disabled = false;
            });

            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();

                // Restaurer l'état désactivé des champs
                disabledFields.forEach(field => {
                    if (field.getAttribute('data-was-disabled') === 'true') {
                        field.disabled = true;
                    }
                });
            }

            form.classList.add('was-validated');
        }, false)
    })
})()

// Fonction pour basculer l'affichage du champ de saisie personnalisé
toggleCustomPanneInput = function() {
    const typePanneSelect = document.getElementById('type_panne');
    const customPanneContainer = document.getElementById('customPanneContainer');
    const customPanneInput = document.getElementById('custom_panne');
    
    if (typePanneSelect.value === 'autre') {
        customPanneContainer.style.display = 'block';
        customPanneInput.required = true;
        customPanneInput.disabled = false;
    } else {
        customPanneContainer.style.display = 'none';
        customPanneInput.required = false;
        customPanneInput.disabled = true;
    }
}

// Fonction simplifiée pour charger les marques et les types de pannes
function loadOptionsForType(selectedType, callback) {
    const marqueSelect = document.getElementById('marque');
    const typePanneSelect = document.getElementById('type_panne');
    const customPanneContainer = document.getElementById('customPanneContainer');
    const customPanneInput = document.getElementById('custom_panne');

    console.log("Chargement des options pour le type:", selectedType);

    // Gestion des marques
    marqueSelect.disabled = !selectedType;
    marqueSelect.innerHTML = '<option value="">Sélectionnez une marque</option>';

    // Gestion des types de pannes
    typePanneSelect.disabled = !selectedType;
    typePanneSelect.innerHTML = '<option value="">Sélectionnez un type de panne</option>';
    
    // Masquer le champ de saisie personnalisé
    customPanneContainer.style.display = 'none';
    customPanneInput.required = false;
    customPanneInput.disabled = true;

    if (!selectedType) {
        if (callback) callback();
        return;
    }

    // Charger les marques
    fetch(`/get_marques/${selectedType}`)
        .then(response => response.json())
        .then(marques => {
            console.log("Marques chargées:", marques);
            marques.forEach(marque => {
                const option = document.createElement('option');
                option.value = marque;
                option.textContent = marque;
                marqueSelect.appendChild(option);
            });

            // Si on a une marque stockée dans les champs cachés, la sélectionner
            const savedMarque = document.getElementById('vehicule_marque').value;
            if (savedMarque) {
                marqueSelect.value = savedMarque;
            }

            // Charger les types de pannes
            return fetch(`/get_pannes/${selectedType}`);
        })
        .then(response => response.json())
        .then(pannes => {
            console.log("Types de pannes chargés:", pannes);
            pannes.forEach(panne => {
                const option = document.createElement('option');
                option.value = panne;
                option.textContent = panne;
                typePanneSelect.appendChild(option);
            });

            // Ajouter l'option "Autre (précisez)" à la fin de la liste
            const autreOption = document.createElement('option');
            autreOption.value = 'autre';
            autreOption.textContent = 'Autre (précisez)';
            typePanneSelect.appendChild(autreOption);

            if (callback) callback();
        })
        .catch(error => {
            console.error("Erreur lors du chargement des options:", error);
            if (callback) callback();
        });
}

// Gestion dynamique des marques et des types de pannes
document.getElementById('type_vehicule').addEventListener('change', function() {
    loadOptionsForType(this.value);
});

// Variables globales pour stocker les informations du véhicule
let vehiculeInfo = {
    found: false,
    typeVehicule: '',
    marque: '',
    unite: '',
    pannes: []
};

// Fonction pour vérifier si un matricule existe déjà
document.getElementById('searchMatriculeBtn').addEventListener('click', function() {
    const matricule = document.getElementById('modele').value.trim();
    const modalBody = document.getElementById('vehiculeInfoModalBody');

    if (!matricule) {
        alert('Veuillez entrer un matricule à vérifier');
        return;
    }

    // Afficher un indicateur de chargement dans la modal
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Recherche du matricule ${matricule}...</p>
        </div>
    `;

    // Ouvrir la modal
    document.getElementById('openModalBtn').click();

    // Effectuer une recherche AJAX pour vérifier le matricule
    fetch(`/verifier_vehicule?matricule=${encodeURIComponent(matricule)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Erreur réseau');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.vehicule) {
                // Matricule trouvé, préparer les informations
                vehiculeInfo = {
                    found: true,
                    typeVehicule: data.vehicule.type_vehicule,
                    marque: data.vehicule.marque,
                    unite: data.vehicule.unite || '',
                    pannes: data.pannes || []
                };

                // Stocker les informations dans les champs cachés
                document.getElementById('vehicule_found').value = 'true';
                document.getElementById('vehicule_type').value = vehiculeInfo.typeVehicule;
                document.getElementById('vehicule_marque').value = vehiculeInfo.marque;
                document.getElementById('vehicule_unite').value = vehiculeInfo.unite;

                // Afficher les informations dans la modal
                let modalContent = `
                    <div class="alert alert-success">
                        <strong>Matricule trouvé !</strong> Ce véhicule existe dans la liste des véhicules GAR.
                    </div>
                    <div class="card border-0">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>Type:</strong> ${data.vehicule.type_vehicule}</p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Marque:</strong> ${data.vehicule.marque}</p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Unité:</strong> ${data.vehicule.unite || 'Non assigné'}</p>
                                </div>
                            </div>`;

                if (vehiculeInfo.pannes.length > 0) {
                    modalContent += `
                        <hr>
                        <h5 class="mb-3">Pannes existantes:</h5>
                        <ul class="list-group">`;

                    vehiculeInfo.pannes.forEach(panne => {
                        const statusClass = panne.statut === 'Réparé' ? 'success' :
                                          (panne.statut === 'En réparation' ? 'info' :
                                          (panne.statut === 'Indisponible' ? 'warning' : 'danger'));
                        modalContent += `
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ${panne.type_panne}
                                <span class="badge bg-${statusClass}">${panne.statut}</span>
                            </li>`;
                    });

                    modalContent += `</ul>`;
                }

                modalContent += `
                        </div>
                    </div>`;

                modalBody.innerHTML = modalContent;

                // Remplir automatiquement les champs du formulaire
                document.getElementById('type_vehicule').value = vehiculeInfo.typeVehicule;
                document.getElementById('unite').value = vehiculeInfo.unite;

                // Charger les marques et les types de pannes
                loadOptionsForType(vehiculeInfo.typeVehicule, function() {
                    const marqueSelect = document.getElementById('marque');
                    marqueSelect.value = vehiculeInfo.marque;
                    
                    // Désactiver les champs remplis
                    document.getElementById('type_vehicule').disabled = true;
                    marqueSelect.disabled = true;
                    document.getElementById('unite').disabled = true;

                    // Ajouter une indication visuelle
                    document.getElementById('type_vehicule').classList.add('bg-light');
                    marqueSelect.classList.add('bg-light');
                    document.getElementById('unite').classList.add('bg-light');

                    // Mettre le focus sur le champ de type de panne
                    document.getElementById('type_panne').focus();
                });
            } else {
                // Matricule non trouvé
                modalBody.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Matricule non trouvé</strong><br>
                        ${data.message || 'Ce matricule n\'existe pas dans la liste des véhicules GAR.'}
                    </div>`;
                
                // Réinitialiser vehiculeInfo et les champs cachés
                vehiculeInfo = {
                    found: false,
                    typeVehicule: '',
                    marque: '',
                    unite: '',
                    pannes: []
                };
                document.getElementById('vehicule_found').value = 'false';
                document.getElementById('vehicule_type').value = '';
                document.getElementById('vehicule_marque').value = '';
                document.getElementById('vehicule_unite').value = '';
                
                // Désactiver le bouton Confirmer
                document.getElementById('useVehiculeInfoBtn').disabled = true;
            }
        })
        .catch(error => {
            console.error('Erreur lors de la vérification du matricule:', error);
            modalBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Erreur</strong><br>
                    Une erreur est survenue lors de la vérification du matricule. Veuillez réessayer.
                </div>`;
            
            // Réinitialiser vehiculeInfo et les champs cachés
            vehiculeInfo = {
                found: false,
                typeVehicule: '',
                marque: '',
                unite: '',
                pannes: []
            };
            document.getElementById('vehicule_found').value = 'false';
            document.getElementById('vehicule_type').value = '';
            document.getElementById('vehicule_marque').value = '';
            document.getElementById('vehicule_unite').value = '';
            
            // Désactiver le bouton Confirmer
            document.getElementById('useVehiculeInfoBtn').disabled = true;
        });
});

// Fonction ultra-simplifiée pour remplir les champs avec les informations du véhicule
function fillVehiculeInfo() {
    console.log("Remplissage des champs avec les informations du véhicule:", vehiculeInfo);

    if (!vehiculeInfo.found) {
        console.log("Aucune information de véhicule trouvée");
        return;
    }

    try {
        // Fermer la modal manuellement
        var modal = bootstrap.Modal.getInstance(document.getElementById('vehiculeInfoModal'));
        if (modal) {
            modal.hide();
        }

        // Remplir directement les champs sans passer par les événements
        document.getElementById('type_vehicule').value = vehiculeInfo.typeVehicule;
        document.getElementById('unite').value = vehiculeInfo.unite;

        // Forcer le chargement des marques
        fetch(`/get_marques/${vehiculeInfo.typeVehicule}`)
            .then(response => response.json())
            .then(marques => {
                // Remplir les options de marque
                const marqueSelect = document.getElementById('marque');
                marqueSelect.innerHTML = '<option value="">Sélectionnez une marque</option>';
                marques.forEach(marque => {
                    const option = document.createElement('option');
                    option.value = marque;
                    option.textContent = marque;
                    marqueSelect.appendChild(option);
                });

                // Définir la marque
                marqueSelect.value = vehiculeInfo.marque;
                marqueSelect.disabled = false;

                // Désactiver les champs
                document.getElementById('type_vehicule').disabled = true;
                marqueSelect.disabled = true;
                document.getElementById('unite').disabled = true;

                // Ajouter une indication visuelle
                document.getElementById('type_vehicule').classList.add('bg-light');
                marqueSelect.classList.add('bg-light');
                document.getElementById('unite').classList.add('bg-light');



                // Forcer le chargement des types de pannes
                return fetch(`/get_pannes/${vehiculeInfo.typeVehicule}`);
            })
            .then(response => response.json())
            .then(pannes => {
                // Remplir les options de type de panne
                const typePanneSelect = document.getElementById('type_panne');
                typePanneSelect.innerHTML = '<option value="">Sélectionnez un type de panne</option>';
                pannes.forEach(panne => {
                    const option = document.createElement('option');
                    option.value = panne;
                    option.textContent = panne;
                    typePanneSelect.appendChild(option);
                });

                // Ajouter l'option "Autre (précisez)" à la fin de la liste
                const autreOption = document.createElement('option');
                autreOption.value = 'autre';
                autreOption.textContent = 'Autre (précisez)';
                typePanneSelect.appendChild(autreOption);

                // Activer le champ de type de panne
                typePanneSelect.disabled = false;

                // Mettre le focus sur le champ de type de panne
                typePanneSelect.focus();
            })
            .catch(error => {
                console.error("Erreur lors du remplissage des champs:", error);
                alert("Une erreur est survenue lors du remplissage des champs. Veuillez réessayer.");
            });
    } catch (error) {
        console.error("Erreur lors du remplissage des champs:", error);
        alert("Une erreur est survenue lors du remplissage des champs. Veuillez réessayer.");
    }
}

// Animation du formulaire
document.addEventListener('DOMContentLoaded', function() {
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        setTimeout(() => {
            group.style.transition = 'all 0.3s ease';
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// Charger les unités au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Charger les unités depuis la base de données
    fetch('/get_unites')
        .then(response => response.json())
        .then(unites => {
            const uniteSelect = document.getElementById('unite');
            unites.forEach(unite => {
                const option = document.createElement('option');
                option.value = unite;
                option.textContent = unite;
                uniteSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des unités:', error);
        });
});
</script>
<!-- Modal pour afficher les informations du véhicule -->
<div class="modal fade" id="vehiculeInfoModal" tabindex="-1" aria-labelledby="vehiculeInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="vehiculeInfoModalLabel">Informations du véhicule</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="vehiculeInfoModalBody">
                <!-- Le contenu sera injecté par JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                <button type="button" class="btn btn-primary" id="useVehiculeInfoBtn" onclick="fillVehiculeInfo(); return false;">Confirmer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}