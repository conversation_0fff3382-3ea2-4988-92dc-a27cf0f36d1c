"""Initial migration after reset

Revision ID: 365cef44f1ac
Revises: 
Create Date: 2025-06-30 16:34:13.733475

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '365cef44f1ac'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Drop existing tables if they exist to ensure a clean slate
    op.drop_table('inscription', if_exists=True)
    op.drop_table('document', if_exists=True)
    op.drop_table('stage', if_exists=True)
    op.drop_table('rapport', if_exists=True)
    op.drop_table('prise_arme', if_exists=True)
    op.drop_table('notification', if_exists=True)
    op.drop_table('action', if_exists=True)
    op.drop_table('utilisateur', if_exists=True)
    op.drop_table('type_stage', if_exists=True)
    op.drop_table('stagiaire', if_exists=True)
    op.drop_table('promotion', if_exists=True)
    
    op.create_table('promotion',
    sa.Column('id_promotion', sa.Integer(), nullable=False),
    sa.Column('nom', sa.String(length=150), nullable=False),
    sa.Column('annee', sa.Integer(), nullable=False),
    sa.Column('filiere', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id_promotion')
    )
    op.create_table('stagiaire',
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.Column('nom', sa.String(length=100), nullable=False),
    sa.Column('prenom', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('date_naissance', sa.Date(), nullable=False),
    sa.Column('photo', sa.String(length=255), nullable=True),
    sa.Column('adresse', sa.Text(), nullable=True),
    sa.Column('telephone', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id_stagiaire'),
    sa.UniqueConstraint('email')
    )
    op.create_table('type_stage',
    sa.Column('id_type_stage', sa.Integer(), nullable=False),
    sa.Column('libelle', sa.String(length=100), nullable=False),
    sa.Column('conditions_admission', sa.Text(), nullable=True),
    sa.Column('documents_requis', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id_type_stage')
    )
    op.create_table('utilisateur',
    sa.Column('id_user', sa.Integer(), nullable=False),
    sa.Column('nom_user', sa.String(length=100), nullable=False),
    sa.Column('role', sa.Enum('admin', 'rh', 'responsable', name='role_enum'), nullable=False),
    sa.PrimaryKeyConstraint('id_user'),
    sa.UniqueConstraint('nom_user')
    )
    op.create_table('action',
    sa.Column('id_action', sa.Integer(), nullable=False),
    sa.Column('type_action', sa.String(length=255), nullable=False),
    sa.Column('date_action', sa.DateTime(), nullable=False),
    sa.Column('id_user', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_user'], ['utilisateur.id_user'], ),
    sa.PrimaryKeyConstraint('id_action')
    )
    op.create_table('notification',
    sa.Column('id_notif', sa.Integer(), nullable=False),
    sa.Column('contenu', sa.Text(), nullable=False),
    sa.Column('date_envoi', sa.DateTime(), nullable=False),
    sa.Column('lue', sa.Boolean(), nullable=True),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id_stagiaire'], ),
    sa.PrimaryKeyConstraint('id_notif')
    )
    op.create_table('prise_arme',
    sa.Column('id_prise', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('presence', sa.Boolean(), nullable=True),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id_stagiaire'], ),
    sa.PrimaryKeyConstraint('id_prise')
    )
    op.create_table('rapport',
    sa.Column('id_rapport', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=False),
    sa.Column('date_creation', sa.DateTime(), nullable=False),
    sa.Column('contenu', sa.Text(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id_stagiaire'], ),
    sa.PrimaryKeyConstraint('id_rapport')
    )
    op.create_table('stage',
    sa.Column('id_stage', sa.Integer(), nullable=False),
    sa.Column('date_debut', sa.Date(), nullable=False),
    sa.Column('date_fin', sa.Date(), nullable=False),
    sa.Column('statut', sa.Enum('en_attente', 'valide', 'termine', 'proroge', name='statut_enum'), nullable=False),
    sa.Column('id_type_stage', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_type_stage'], ['type_stage.id_type_stage'], ),
    sa.PrimaryKeyConstraint('id_stage')
    )
    op.create_table('document',
    sa.Column('id_document', sa.Integer(), nullable=False),
    sa.Column('type_doc', sa.String(length=100), nullable=False),
    sa.Column('chemin_fichier', sa.String(length=255), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.Column('id_stage', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['id_stage'], ['stage.id_stage'], ),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id_stagiaire'], ),
    sa.PrimaryKeyConstraint('id_document')
    )
    op.create_table('inscription',
    sa.Column('id_inscription', sa.Integer(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.Column('id_stage', sa.Integer(), nullable=False),
    sa.Column('id_promotion', sa.Integer(), nullable=False),
    sa.Column('date_inscription', sa.Date(), nullable=False),
    sa.ForeignKeyConstraint(['id_promotion'], ['promotion.id_promotion'], ),
    sa.ForeignKeyConstraint(['id_stage'], ['stage.id_stage'], ),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id_stagiaire'], ),
    sa.PrimaryKeyConstraint('id_inscription')
    )
    # SUPPRESSION des anciennes tables de gestion de courrier (si elles existent)
    op.drop_table('courrier_division_action', if_exists=True)
    op.drop_table('courrier_division_info', if_exists=True)
    op.drop_table('courrier_arrive', if_exists=True)
    op.drop_table('courrier_envoye', if_exists=True)

    # CREATION de la nouvelle structure de gestion de courrier
    op.create_table(
        'courrier',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('reference', sa.String(100), nullable=False, unique=True),
        sa.Column('type_courrier', sa.Enum('arrive', 'depart', name='type_courrier_enum'), nullable=False),
        sa.Column('nature', sa.Enum('message', 'nds', 'note_royale', 'decision', name='nature_enum'), nullable=False),
        sa.Column('urgence', sa.Enum('extreme', 'extreme_urgent', 'urgent', 'routine', name='urgence_enum'), nullable=False),
        sa.Column('date_courrier', sa.Date(), nullable=False),
        sa.Column('date_signature', sa.Date(), nullable=True),
        sa.Column('expediteur', sa.String(200), nullable=True),
        sa.Column('destinataire_externe', sa.String(200), nullable=True),
        sa.Column('objet', sa.Text(), nullable=False),
        sa.Column('classification', sa.Enum('public', 'restreint', 'confidentiel', 'secret', 'tres_secret', name='classification_enum'), nullable=True),
        sa.Column('annotation', sa.Text(), nullable=True),
        sa.Column('observations', sa.Text(), nullable=True),
        sa.Column('statut', sa.String(30), nullable=True),
        sa.Column('date_creation', sa.DateTime(), nullable=False, server_default=sa.func.now()),
    )

    op.create_table(
        'courrier_division',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('courrier_id', sa.Integer(), sa.ForeignKey('courrier.id', ondelete='CASCADE'), nullable=False),
        sa.Column('division', sa.Enum('courrier', 'instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa', name='division_enum'), nullable=False),
        sa.Column('role', sa.Enum('action', 'info', name='role_division_enum'), nullable=False),
    )

    op.create_table(
        'courrier_piece_jointe',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('courrier_id', sa.Integer(), sa.ForeignKey('courrier.id', ondelete='CASCADE'), nullable=False),
        sa.Column('nom_fichier', sa.String(255), nullable=False),
        sa.Column('chemin_fichier', sa.String(255), nullable=False),
        sa.Column('division_visible', sa.Enum('courrier', 'instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa', name='division_enum'), nullable=False),
        sa.Column('date_ajout', sa.DateTime(), nullable=False, server_default=sa.func.now()),
    )

    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')

    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=mysql.VARCHAR(length=10),
               type_=sa.String(length=20),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=sa.String(length=20),
               type_=mysql.VARCHAR(length=10),
               nullable=False)

    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')

    op.drop_table('inscription')
    op.drop_table('document')
    op.drop_table('stage')
    op.drop_table('rapport')
    op.drop_table('prise_arme')
    op.drop_table('notification')
    op.drop_table('action')
    op.drop_table('utilisateur')
    op.drop_table('type_stage')
    op.drop_table('stagiaire')
    op.drop_table('promotion')
    # ### end Alembic commands ###
