{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Assistant IA</h3>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-robot me-2"></i>Assistant virtuel
                        </h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" id="clearChat">
                            <i class="fas fa-eraser me-2"></i>Effacer la conversation
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Zone de suggestions -->
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Suggestions</h6>
                            </div>
                            <div class="card-body">
                                <div class="list-group">
                                    <button class="list-group-item list-group-item-action" data-query="Comment créer une nouvelle convention de stage ?">
                                        <i class="fas fa-file-contract me-2"></i>Créer une convention
                                    </button>
                                    <button class="list-group-item list-group-item-action" data-query="Comment ajouter un nouveau stagiaire ?">
                                        <i class="fas fa-user-plus me-2"></i>Ajouter un stagiaire
                                    </button>
                                    <button class="list-group-item list-group-item-action" data-query="Comment générer une attestation de stage ?">
                                        <i class="fas fa-certificate me-2"></i>Générer une attestation
                                    </button>
                                    <button class="list-group-item list-group-item-action" data-query="Comment configurer les alertes de fin de stage ?">
                                        <i class="fas fa-bell me-2"></i>Configurer les alertes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Zone de chat -->
                    <div class="col-md-9">
                        <div class="chat-container bg-light p-3 rounded" style="height: 400px; overflow-y: auto;">
                            <div id="chatMessages">
                                <!-- Message de bienvenue -->
                                <div class="chat-message assistant mb-3">
                                    <div class="d-flex">
                                        <div class="flex-shrink-0">
                                            <div class="avatar avatar-sm">
                                                <i class="fas fa-robot fa-2x text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="bg-white rounded p-3 shadow-sm">
                                                <p class="mb-0">
                                                    Bonjour ! Je suis votre assistant virtuel pour la gestion des stages. 
                                                    Comment puis-je vous aider aujourd'hui ?
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Zone de saisie -->
                        <div class="mt-3">
                            <form id="chatForm" class="d-flex">
                                <input type="text" class="form-control me-2" id="userInput" 
                                    placeholder="Posez votre question ici..." autocomplete="off">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatMessages = document.getElementById('chatMessages');
    const chatForm = document.getElementById('chatForm');
    const userInput = document.getElementById('userInput');
    const clearChat = document.getElementById('clearChat');
    const suggestionButtons = document.querySelectorAll('.list-group-item-action');

    // Fonction pour ajouter un message au chat
    function addMessage(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${isUser ? 'user' : 'assistant'} mb-3`;
        
        const messageContent = `
            <div class="d-flex ${isUser ? 'justify-content-end' : ''}">
                ${!isUser ? `
                <div class="flex-shrink-0">
                    <div class="avatar avatar-sm">
                        <i class="fas fa-robot fa-2x text-primary"></i>
                    </div>
                </div>
                ` : ''}
                <div class="flex-grow-1 ${isUser ? 'me-3' : 'ms-3'}">
                    <div class="bg-${isUser ? 'primary text-white' : 'white'} rounded p-3 shadow-sm">
                        <p class="mb-0">${message}</p>
                    </div>
                </div>
                ${isUser ? `
                <div class="flex-shrink-0">
                    <div class="avatar avatar-sm">
                        <i class="fas fa-user fa-2x text-primary"></i>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
        
        messageDiv.innerHTML = messageContent;
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Simulation de réponse de l'IA
    function simulateResponse(query) {
        const responses = {
            "Comment créer une nouvelle convention de stage ?": 
                "Pour créer une nouvelle convention de stage, suivez ces étapes :\n" +
                "1. Allez dans la section 'Documents'\n" +
                "2. Cliquez sur 'Générer un document'\n" +
                "3. Sélectionnez 'Convention de stage'\n" +
                "4. Remplissez les informations requises\n" +
                "5. Cliquez sur 'Générer'",
            
            "Comment ajouter un nouveau stagiaire ?":
                "Pour ajouter un nouveau stagiaire :\n" +
                "1. Accédez à la section 'Stagiaires'\n" +
                "2. Cliquez sur 'Nouveau Stagiaire'\n" +
                "3. Remplissez le formulaire avec les informations du stagiaire\n" +
                "4. Cliquez sur 'Enregistrer'",
            
            "Comment générer une attestation de stage ?":
                "Pour générer une attestation de stage :\n" +
                "1. Allez dans 'Documents'\n" +
                "2. Sélectionnez 'Attestation de stage'\n" +
                "3. Choisissez le stagiaire concerné\n" +
                "4. Vérifiez les dates et informations\n" +
                "5. Cliquez sur 'Générer'",
            
            "Comment configurer les alertes de fin de stage ?":
                "Pour configurer les alertes :\n" +
                "1. Accédez à 'Suivi & Alertes'\n" +
                "2. Cliquez sur 'Configurer les alertes'\n" +
                "3. Définissez les délais de notification\n" +
                "4. Choisissez les types d'alertes\n" +
                "5. Enregistrez vos préférences"
        };

        setTimeout(() => {
            const response = responses[query] || "Je ne suis pas sûr de comprendre votre question. Pourriez-vous la reformuler ?";
            addMessage(response);
        }, 1000);
    }

    // Gestion du formulaire
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = userInput.value.trim();
        if (message) {
            addMessage(message, true);
            userInput.value = '';
            simulateResponse(message);
        }
    });

    // Gestion des suggestions
    suggestionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const query = this.dataset.query;
            userInput.value = query;
            chatForm.dispatchEvent(new Event('submit'));
        });
    });

    // Effacer la conversation
    clearChat.addEventListener('click', function() {
        if (confirm('Voulez-vous vraiment effacer toute la conversation ?')) {
            chatMessages.innerHTML = '';
            // Réafficher le message de bienvenue
            addMessage("Bonjour ! Je suis votre assistant virtuel pour la gestion des stages. Comment puis-je vous aider aujourd'hui ?");
        }
    });
});
</script>

<style>
.chat-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.chat-message {
    max-width: 80%;
}

.chat-message.user {
    margin-left: 20%;
}

.chat-message.assistant {
    margin-right: 20%;
}

.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f8f9fa;
}
</style>
{% endblock %} 