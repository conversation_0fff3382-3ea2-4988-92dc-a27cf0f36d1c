{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="card-header d-flex justify-content-between align-items-center text-white" style="background-color: #4b5320; font-size: 1.3rem; font-weight: bold;">
        <span>Gestion des Vidanges</span>
        <div class="d-flex align-items-center">
            <button class="btn btn-primary ms-2" id="btnNouvelleVidange">
                <i class="fas fa-plus"></i> Nouvelle Vidange
            </button>
            <div class="notification-container" style="position: relative;">
                <div class="military-alert-icon ms-3" id="notificationIcon" data-bs-toggle="tooltip" style="display: none;">
                    <div class="icon-wrapper">
                        <svg class="alert-icon" viewBox="0 0 24 24" width="28" height="28">
                            <defs>
                                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#A8C090"/>
                                    <stop offset="100%" style="stop-color:#7D8C65"/>
                                </linearGradient>
                            </defs>
                            <path class="icon-path" d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.64 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/>
                        </svg>
                        <div class="status-indicator"></div>
                        <div class="effect-layer"></div>
                        <span id="notificationBadge" style="display:none;position:absolute;top:2px;right:2px;background:#dc3545;color:#fff;font-size:0.8em;padding:2px 6px;border-radius:10px;z-index:10;"></span>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Barre de recherche -->
    <div class="card mb-4">
        <div class="card-body bg-light py-3">
            <form action="{{ url_for('entretiens') }}" method="get" class="d-flex">
                <div class="input-group">
                    <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher les entretiens par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                    {% if search_matricule %}
                    <a href="{{ url_for('entretiens') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Effacer
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Section de vérification du véhicule -->
    <div id="verificationSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Vérification du Véhicule</h5>
        </div>
        <div class="card-body">
            <form id="verificationForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="matricule">Matricule</label>
                            <input type="text" class="form-control" id="matricule" name="matricule" required>
                        </div>
                    </div>
                </div>
                <div id="vehiculeInfoDisplay" class="alert alert-info mt-3" style="display: none;">
                    <h6>Informations du Véhicule</h6>
                    <p id="vehiculeDetailsDisplay"></p>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="btnVerifierVehicule">Vérifier</button>
                    <button type="button" class="btn btn-success" id="btnConfirmerVehicule" style="display: none;">Confirmer et Ajouter Vidange</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerVerification">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Section d'ajout de vidange -->
    <div id="ajoutVidangeSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Nouvelle Vidange pour Véhicule <span id="ajoutVidangeMatricule"></span></h5>
        </div>
        <div class="card-body">
            <form id="ajoutVidangeForm" action="{{ url_for('ajouter_entretien') }}" method="POST">
                <input type="hidden" id="vehicule_id_ajout" name="vehicule_id">
                <input type="hidden" name="type_entretien" value="Vidange">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_entretien">Date de Vidange</label>
                            <input type="date" class="form-control" id="date_entretien" name="date_entretien" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage">Kilométrage Actuel</label>
                            <input type="number" class="form-control" id="kilometrage" name="kilometrage" required>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage_prochain">Prochain Kilométrage</label>
                            <input type="number" class="form-control" id="kilometrage_prochain" name="kilometrage_prochain" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Pièces Remplacées</label>
                            <div id="piecesContainer">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à huile" id="piece1">
                                    <label class="form-check-label" for="piece1">Filtre à huile</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à air" id="piece2">
                                    <label class="form-check-label" for="piece2">Filtre à air</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à carburant" id="piece3">
                                    <label class="form-check-label" for="piece3">Filtre à carburant</label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="text" class="form-control" id="autrePiece" placeholder="Autre pièce...">
                                <button type="button" class="btn btn-sm btn-secondary mt-2" id="btnAjouterPiece">Ajouter</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="description">Description/Commentaires (optionnel)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Ajoutez des commentaires sur cette vidange..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerAjout">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tableau des vidanges -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="vidangesTable">
                    <thead>
                        <tr>
                            <th>Matricule</th>
                            <th>Unité</th>
                            <th>Date</th>
                            <th>Kilométrage</th>
                            <th>Prochain Kilométrage</th>
                            <th>Pièces Remplacées</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entretien in entretiens %}
                        <tr>
                            <td>{{ entretien.vehicule.matricule }}</td>
                            <td>{{ entretien.vehicule.unite }}</td>
                            <td>{{ entretien.date_entretien.strftime('%d/%m/%Y') }}</td>
                            <td>{{ entretien.kilometrage }}</td>
                            <td>{{ entretien.kilometrage_prochain }}</td>
                            <td>{{ entretien.pieces_remplacees or 'Aucune' }}</td>
                            <td>
                                <button class="btn btn-sm btn-info btn-update-km" data-vehicule-id="{{ entretien.vehicule.id }}">
                                    <i class="fas fa-edit"></i> Mettre à jour km
                                </button>
                                <button class="btn btn-sm btn-danger btn-delete-entretien" data-entretien-id="{{ entretien.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Section de mise à jour du kilométrage -->
<div id="updateKilometrageSection" class="card mb-4" style="display: none;">
    <div class="card-header text-white" style="background-color: #4b5320;">
        <h5 class="mb-0">Mise à jour du kilométrage</h5>
    </div>
    <div class="card-body">
        <form id="formMiseAJourKilometrage" class="row g-3 align-items-end">
            <input type="hidden" id="vehicule_id_update" name="vehicule_id">
            <div class="col-md-6">
                <label for="nouveau_kilometrage" class="form-label">Nouveau kilométrage</label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control form-control-lg" 
                           id="nouveau_kilometrage" 
                           name="kilometrage" 
                           required 
                           min="0" 
                           step="1"
                           placeholder="Entrez le kilométrage actuel">
                    <span class="input-group-text">km</span>
                </div>
                <div class="form-text">Le kilométrage doit être supérieur au précédent</div>
                <div id="kilometrageFeedback" class="invalid-feedback">
                    Veuillez entrer un kilométrage valide
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary" id="btnSaveKilometrage">
                        <i class="fas fa-save me-1"></i> Enregistrer
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="btnCancelKilometrage">
                        <i class="fas fa-times me-1"></i> Annuler
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>



<!-- Modal d'alertes de vidange -->
<div class="modal fade" id="alertModal" tabindex="-1" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content military-modal">
            <div class="modal-header military-modal-header">
                <div class="d-flex align-items-center">
                    <div class="modal-icon-wrapper me-3">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h5 class="modal-title mb-0" id="alertModalLabel">Alertes de Vidange</h5>
                        <small class="text-muted">Véhicules nécessitant un entretien</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onclick="closeAlertModal()"></button>
            </div>
            <div class="modal-body military-modal-body" id="alertModalBody">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer military-modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeAlertModal()">
                    <i class="fas fa-times me-2"></i>Fermer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="toastVidanges" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Vidanges à venir</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastVidangesBody">
        </div>
    </div>
</div>

<style>
.military-alert-icon {
    position: relative;
    cursor: pointer;
    width: 44px;
    height: 44px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 14px;
    background: linear-gradient(145deg, #E8F0E0, #C8D4B8);
    box-shadow: 
        -4px -4px 8px rgba(255, 255, 255, 0.8),
        4px 4px 8px rgba(128, 144, 96, 0.3),
        inset 1px 1px 1px rgba(255, 255, 255, 0.4);
}

.icon-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
}

.alert-icon {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.icon-path {
    fill: url(#iconGradient);
    transition: all 0.3s ease;
}

.status-indicator {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(145deg, #98B475, #7D8C65);
    box-shadow: 
        0 0 6px rgba(125, 140, 101, 0.5),
        inset -1px -1px 2px rgba(0,0,0,0.1);
    transform: scale(0);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.effect-layer {
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        rgba(255,255,255,0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* États actifs et animations */
.military-alert-icon.active {
    animation: alertAppear 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.military-alert-icon.active .status-indicator {
    transform: scale(1);
    animation: statusPulse 2s infinite;
}

.military-alert-icon.urgent {
    background: linear-gradient(145deg, #FFE5D9, #FFD0B5);
}

.military-alert-icon.urgent .icon-path {
    fill: #D17451;
}

.military-alert-icon.urgent .status-indicator {
    background: linear-gradient(145deg, #FF6B6B, #D63E3E);
    box-shadow: 0 0 8px rgba(255, 107, 107, 0.6);
}

.military-alert-icon:hover {
    transform: translateY(-2px);
    box-shadow: 
        -6px -6px 12px rgba(255, 255, 255, 0.8),
        6px 6px 12px rgba(128, 144, 96, 0.4),
        inset 1px 1px 1px rgba(255, 255, 255, 0.4);
}

.military-alert-icon:hover .effect-layer {
    opacity: 1;
}

/* Animations */
@keyframes alertAppear {
    0% {
        transform: scale(0.9) translateY(10px);
        opacity: 0;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes statusPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 6px rgba(125, 140, 101, 0.5);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 12px rgba(125, 140, 101, 0.7);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 6px rgba(125, 140, 101, 0.5);
    }
}

/* Effet de brillance */
.military-alert-icon::before {
    content: '';
    position: absolute;
    inset: 1px;
    border-radius: inherit;
    background: linear-gradient(
        135deg,
        rgba(255,255,255,0.5) 0%,
        rgba(255,255,255,0.2) 50%,
        transparent 100%
    );
    opacity: 0.7;
    pointer-events: none;
}

/* Effet de surbrillance au hover */
.military-alert-icon:hover::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        transparent 0%,
        rgba(255,255,255,0.2) 50%,
        transparent 100%
    );
    animation: lightSweep 2s infinite;
    border-radius: inherit;
}

@keyframes lightSweep {
    0% {
        background-position: -100% -100%;
    }
    100% {
        background-position: 200% 200%;
    }
}

/* Animation pulse pour les nouvelles alertes */
.military-alert-icon.pulse {
    animation: alertPulse 1s ease-in-out;
}

@keyframes alertPulse {
    0% {
        transform: scale(1);
        box-shadow:
            -4px -4px 8px rgba(255, 255, 255, 0.8),
            4px 4px 8px rgba(128, 144, 96, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.9),
            6px 6px 12px rgba(128, 144, 96, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow:
            -4px -4px 8px rgba(255, 255, 255, 0.8),
            4px 4px 8px rgba(128, 144, 96, 0.3);
    }
}

/* Modal militaire moderne */
.military-modal .modal-content {
    border: none;
    border-radius: 16px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.military-modal-header {
    background: linear-gradient(145deg, #E8F0E0, #D4E4C8);
    border-bottom: 1px solid rgba(125, 140, 101, 0.2);
    padding: 20px 24px;
}

.modal-icon-wrapper {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(145deg, #A8C090, #7D8C65);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4em;
    box-shadow:
        0 4px 8px rgba(125, 140, 101, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.military-modal-header .modal-title {
    color: #4A5D23;
    font-weight: 700;
    font-size: 1.2em;
}

.military-modal-body {
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.military-modal-footer {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-top: 1px solid rgba(125, 140, 101, 0.1);
    padding: 16px 24px;
}



/* Éléments d'alerte dans le modal */
.vehicle-alert-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.vehicle-alert-card:last-child {
    border-bottom: none;
}

.vehicle-alert-card:hover {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    transform: translateX(4px);
}

.vehicle-alert-card::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    border-radius: 0 4px 4px 0;
    transition: all 0.3s ease;
}

.vehicle-alert-card.normal::before {
    background: linear-gradient(145deg, #A8C090, #7D8C65);
}

.vehicle-alert-card.urgent::before {
    background: linear-gradient(145deg, #FF8A65, #D17451);
}

.vehicle-alert-card.overdue::before {
    background: linear-gradient(145deg, #FF6B6B, #D63E3E);
}

.vehicle-icon {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.6em;
    flex-shrink: 0;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.vehicle-icon.normal {
    background: linear-gradient(145deg, #E8F0E0, #D4E4C8);
    color: #7D8C65;
}

.vehicle-icon.urgent {
    background: linear-gradient(145deg, #FFE5D9, #FFD0B5);
    color: #D17451;
}

.vehicle-icon.overdue {
    background: linear-gradient(145deg, #ffebee, #ffcdd2);
    color: #c62828;
}

.vehicle-info {
    flex: 1;
}

.vehicle-matricule {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1em;
    margin-bottom: 4px;
}

.vehicle-unite {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 8px;
}

.vehicle-km-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85em;
    color: #495057;
}

.vehicle-km-info i {
    color: #7D8C65;
}

.vehicle-status {
    text-align: center;
    min-width: 100px;
}

.status-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-badge.normal {
    background: linear-gradient(145deg, #E8F0E0, #D4E4C8);
    color: #4A5D23;
}

.status-badge.urgent {
    background: linear-gradient(145deg, #FFE5D9, #FFD0B5);
    color: #D17451;
}

.status-badge.overdue {
    background: linear-gradient(145deg, #ffebee, #ffcdd2);
    color: #c62828;
}

.km-remaining {
    font-size: 0.75em;
    margin-top: 4px;
    opacity: 0.8;
}

/* Style pour le surlignage des lignes du tableau */
#vidangesTable tbody tr.table-warning {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-left: 4px solid #ffc107;
    transition: all 0.3s ease;
}

/* Scrollbar personnalisée pour le modal */
.military-modal-body::-webkit-scrollbar {
    width: 6px;
}

.military-modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.military-modal-body::-webkit-scrollbar-thumb {
    background: rgba(125, 140, 101, 0.3);
    border-radius: 3px;
}

.military-modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(125, 140, 101, 0.5);
}

/* Animation du modal */
.military-modal .modal-dialog {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.military-modal.fade .modal-dialog {
    transform: scale(0.9) translateY(-20px);
}

.military-modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}

/* État vide du modal */
.no-alerts {
    text-align: center;
    padding: 40px 24px;
    color: #6c757d;
}

.no-alerts i {
    font-size: 3em;
    color: #A8C090;
    margin-bottom: 16px;
}

.no-alerts h6 {
    color: #4A5D23;
    margin-bottom: 8px;
}

/* Animation pulse pour les nouvelles alertes */
.military-alert-icon.pulse {
    animation: alertPulse 1s ease-in-out;
}

@keyframes alertPulse {
    0% {
        transform: scale(1);
        box-shadow:
            -4px -4px 8px rgba(255, 255, 255, 0.8),
            4px 4px 8px rgba(128, 144, 96, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow:
            -6px -6px 12px rgba(255, 255, 255, 0.9),
            6px 6px 12px rgba(128, 144, 96, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow:
            -4px -4px 8px rgba(255, 255, 255, 0.8),
            4px 4px 8px rgba(128, 144, 96, 0.3);
    }
}
</style>

<!-- Ajout du son de notification -->
<audio id="notificationSound" preload="auto">
    <source src="{{ url_for('static', filename='sounds/alert.mp3') }}" type="audio/mp3">
</audio>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/entretiens.js') }}"></script>
{% endblock %}
{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed. Initializing entretien page scripts.');

    // Vérification de l'existence des éléments
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');
    const vidangesTableBody = document.querySelector('#vidangesTable tbody');
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    const btnAnnulerAjout = document.getElementById('btnAnnulerAjout');
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');

    // Gestionnaire pour le bouton Nouvelle Vidange
    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nouvelle Vidange button clicked');
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Annuler de la vérification
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('verificationForm').reset();
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Confirmer Véhicule
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            const matricule = document.getElementById('matricule').value;
            fetch(`/verifier_vehicule?matricule=${encodeURIComponent(matricule)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.vehicule) {
                        document.getElementById('vehicule_id_ajout').value = data.vehicule.id;
                        document.getElementById('ajoutVidangeMatricule').textContent = data.vehicule.matricule;
                        verificationSection.style.display = 'none';
                        ajoutVidangeSection.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
        });
    }

    // Gestionnaire pour le bouton Annuler de l'ajout
    if (btnAnnulerAjout) {
        btnAnnulerAjout.addEventListener('click', function() {
            ajoutVidangeSection.style.display = 'none';
            document.getElementById('ajoutVidangeForm').reset();
        });
    }

    // Gestionnaire pour l'ajout de pièces
    if (btnAjouterPiece) {
        btnAjouterPiece.addEventListener('click', function() {
            const autrePiece = document.getElementById('autrePiece').value.trim();
            if (autrePiece) {
                const piecesContainer = document.getElementById('piecesContainer');
                const newPiece = document.createElement('div');
                newPiece.className = 'form-check';
                newPiece.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="${autrePiece}" id="piece${piecesContainer.children.length + 1}" checked>
                    <label class="form-check-label" for="piece${piecesContainer.children.length + 1}">${autrePiece}</label>
                `;
                piecesContainer.appendChild(newPiece);
                document.getElementById('autrePiece').value = '';
            }
        });
    }

    // Gestionnaire pour le formulaire de mise à jour du kilométrage
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const nouveauKilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch('/mise_a_jour_kilometrage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicule_id: vehiculeId,
                    kilometrage: nouveauKilometrage
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || 'Erreur lors de la mise à jour du kilométrage');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        });
    }

    // Gestionnaire pour le bouton Annuler de la mise à jour du kilométrage
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
            document.getElementById('formMiseAJourKilometrage').reset();
        });
    }

    // Gestionnaire pour les boutons de mise à jour et suppression
    if (vidangesTableBody) {
        vidangesTableBody.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            
            if (target) {
                if (target.classList.contains('btn-update-km')) {
                    const vehiculeId = target.getAttribute('data-vehicule-id');
                    const row = target.closest('tr');
                    const currentKm = row.cells[3].textContent.trim();
                    mettreAJourKilometrage(vehiculeId, currentKm);
                } else if (target.classList.contains('btn-delete-entretien')) {
                    const entretienId = target.getAttribute('data-entretien-id');
                    supprimerEntretien(entretienId);
                }
            }
        });
    }

    // Les notifications sont maintenant gérées dans entretiens.js
});

// Fonction pour mettre à jour le kilométrage
function mettreAJourKilometrage(vehiculeId, currentKm) {
    console.log('Updating kilometrage for vehicle:', vehiculeId, 'Current km:', currentKm);
    const kmInput = document.getElementById('nouveau_kilometrage');
    const vehiculeIdInput = document.getElementById('vehicule_id_update');
    
    if (kmInput && vehiculeIdInput) {
        vehiculeIdInput.value = vehiculeId;
        kmInput.value = currentKm;
        document.getElementById('updateKilometrageSection').style.display = 'block';
    }
}

// Fonction pour supprimer un entretien
function supprimerEntretien(entretienId) {
    console.log('Deleting entretien:', entretienId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette vidange ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        });
    }
}


</script>
{% endblock %}
