{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Journalisation</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> Exporter Excel
        </button>
        <button class="btn btn-danger">
            <i class="fas fa-file-pdf"></i> Exporter PDF
        </button>
    </div>
</div>

<div class="row">
    <!-- Filtres -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Filtres</h5>
            </div>
            <div class="card-body">
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="dateDebut" class="form-label">Date début</label>
                        <input type="date" class="form-control" id="dateDebut">
                    </div>
                    <div class="col-md-3">
                        <label for="dateFin" class="form-label">Date fin</label>
                        <input type="date" class="form-control" id="dateFin">
                    </div>
                    <div class="col-md-3">
                        <label for="typeAction" class="form-label">Type d'action</label>
                        <select class="form-select" id="typeAction">
                            <option value="">Toutes les actions</option>
                            <option value="creation">Création</option>
                            <option value="modification">Modification</option>
                            <option value="suppression">Suppression</option>
                            <option value="connexion">Connexion</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="utilisateur" class="form-label">Utilisateur</label>
                        <select class="form-select" id="utilisateur">
                            <option value="">Tous les utilisateurs</option>
                            <option value="1">Jean DUPONT</option>
                            <option value="2">Sophie MARTIN</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Filtrer
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Réinitialiser
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Journal des événements -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Journal des événements</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Rechercher...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <!-- Aujourd'hui -->
                    <h6 class="mb-3">Aujourd'hui</h6>
                    <div class="timeline-item">
                        <div class="row">
                            <div class="col-auto text-center flex-column d-none d-sm-flex">
                                <div class="row h-50">
                                    <div class="col">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                                <h5 class="m-2">
                                    <span class="badge rounded-pill bg-primary">&nbsp;</span>
                                </h5>
                                <div class="row h-50">
                                    <div class="col border-end">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                            </div>
                            <div class="col py-2">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="float-end text-muted">14:30</div>
                                        <h4 class="card-title">Connexion utilisateur</h4>
                                        <p class="card-text">Jean DUPONT s'est connecté au système</p>
                                        <button class="btn btn-sm btn-light">
                                            <i class="fas fa-info-circle me-1"></i>Détails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="row">
                            <div class="col-auto text-center flex-column d-none d-sm-flex">
                                <div class="row h-50">
                                    <div class="col border-end">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                                <h5 class="m-2">
                                    <span class="badge rounded-pill bg-warning">&nbsp;</span>
                                </h5>
                                <div class="row h-50">
                                    <div class="col border-end">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                            </div>
                            <div class="col py-2">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="float-end text-muted">12:15</div>
                                        <h4 class="card-title">Modification stagiaire</h4>
                                        <p class="card-text">Sophie MARTIN a modifié les informations du stagiaire DUPONT Jean</p>
                                        <button class="btn btn-sm btn-light">
                                            <i class="fas fa-info-circle me-1"></i>Détails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hier -->
                    <h6 class="mb-3 mt-4">Hier</h6>
                    <div class="timeline-item">
                        <div class="row">
                            <div class="col-auto text-center flex-column d-none d-sm-flex">
                                <div class="row h-50">
                                    <div class="col">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                                <h5 class="m-2">
                                    <span class="badge rounded-pill bg-success">&nbsp;</span>
                                </h5>
                                <div class="row h-50">
                                    <div class="col border-end">&nbsp;</div>
                                    <div class="col">&nbsp;</div>
                                </div>
                            </div>
                            <div class="col py-2">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="float-end text-muted">16:45</div>
                                        <h4 class="card-title">Création document</h4>
                                        <p class="card-text">Génération d'une nouvelle convention de stage</p>
                                        <button class="btn btn-sm btn-light">
                                            <i class="fas fa-info-circle me-1"></i>Détails
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion du formulaire de filtres
    const filterForm = document.getElementById('filterForm');
    filterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Récupération des valeurs des filtres
        const filters = {
            dateDebut: document.getElementById('dateDebut').value,
            dateFin: document.getElementById('dateFin').value,
            typeAction: document.getElementById('typeAction').value,
            utilisateur: document.getElementById('utilisateur').value
        };

        // Simulation de filtrage
        console.log('Filtres appliqués:', filters);
    });

    // Gestion de la recherche
    const searchInput = document.querySelector('input[type="text"]');
    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });

    // Validation des dates
    const dateDebut = document.getElementById('dateDebut');
    const dateFin = document.getElementById('dateFin');

    dateFin.addEventListener('change', function() {
        if (dateDebut.value && this.value) {
            if (new Date(this.value) <= new Date(dateDebut.value)) {
                alert('La date de fin doit être postérieure à la date de début');
                this.value = '';
            }
        }
    });
});
</script>

<style>
.timeline {
    position: relative;
    padding: 0;
    list-style: none;
}

.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child) {
    margin-bottom: 1rem;
}

.timeline .badge {
    width: 1rem;
    height: 1rem;
    display: inline-block;
    border-radius: 50%;
}

.border-end {
    border-right: 2px solid #dee2e6 !important;
}
</style>
{% endblock %} 