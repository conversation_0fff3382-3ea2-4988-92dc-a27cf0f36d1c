#!/usr/bin/env python3
"""
Test pour vérifier que la boucle infinie est corrigée
"""

import requests
import webbrowser
import time

BASE_URL = "http://127.0.0.1:3000"

def test_api_courriers_arrives():
    """Tester que l'API fonctionne toujours"""
    print("🔍 Test de l'API courriers arrivés...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API fonctionne: {len(data)} courriers")
            if data:
                print(f"   Premier courrier: {data[0]['id']} - {data[0]['objet'][:50]}...")
            return True
        else:
            print(f"❌ API erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur API: {e}")
        return False

def open_page_test():
    """Ouvrir la page pour test"""
    print("\n🌐 Ouverture de la page courriers arrivés...")
    webbrowser.open(f"{BASE_URL}/courrier/arrives")

def main():
    """Test principal"""
    print("🔧 TEST APRÈS CORRECTION DE LA BOUCLE INFINIE")
    print("=" * 50)
    
    # Vérifier l'application
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Tester l'API
    if not test_api_courriers_arrives():
        return
    
    # Ouvrir la page
    open_page_test()
    
    print("\n" + "=" * 50)
    print("🎯 VÉRIFICATION DANS LE NAVIGATEUR:")
    print("=" * 50)
    
    print("\n1. 📱 La page courriers arrivés est ouverte")
    print("2. 🔍 Ouvrez la console (F12)")
    print("3. 🔄 Rechargez la page (Ctrl+F5)")
    print("4. ✅ Vérifiez que vous voyez MAINTENANT:")
    print("   • 🚀 Initialisation de la page courriers arrivés...")
    print("   • ✅ Élément trouvé: tbody-courriers")
    print("   • 📡 Chargement des courriers depuis l'API...")
    print("   • ✅ Courriers chargés: X")
    print("   • 📊 Compteurs mis à jour: X total...")
    print("   • ✅ Tableau rendu avec X courriers affichés")
    
    print("\n5. 🚫 Vous ne devriez PLUS voir:")
    print("   • ❌ Élément manquant: count-total")
    print("   • ❌ Élément manquant: count-attente")
    print("   • ❌ Éléments DOM manquants, nouvelle tentative...")
    print("   • (Répétition en boucle)")
    
    print("\n6. 🎯 RÉSULTAT ATTENDU:")
    print("   • ✅ Les courriers s'affichent dans le tableau")
    print("   • ✅ Pas de boucle infinie dans la console")
    print("   • ✅ L'indicateur 'Initialisation en cours...' disparaît")
    
    print("\n🎉 Si vous voyez les courriers, le problème est RÉSOLU !")

if __name__ == "__main__":
    main()
