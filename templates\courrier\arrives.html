{% extends "base.html" %}
{% block title %}Courriers Arrivés - Gestion du Courrier{% endblock %}
{% block content %}
<div class="container-fluid mt-4">


    <!-- Actions et Tableau -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header modal-card-header d-flex justify-content-between align-items-center" style="background: #6B8E23; color: white;">
                    <h5 class="mb-0 text-white"><i class="fas fa-list me-2"></i>Liste des Courriers Arrivés</h5>
                    <button class="btn" style="background: rgba(255,255,255,0.15); color: white; border: 1px solid rgba(255,255,255,0.3);" data-bs-toggle="modal" data-bs-target="#modalAjouterCourrier">
                        <i class="fas fa-plus me-2"></i>Ajouter Courrier
                    </button>
                </div>
                <div class="card-body">
                    <!-- Filtres rapides -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="filter-urgence">
                                <option value="">Toutes les urgences</option>
                                <option value="extreme">Extrême</option>
                                <option value="extreme_urgent">Extrême Urgent</option>
                                <option value="urgent">Urgent</option>
                                <option value="routine">Routine</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filter-nature">
                                <option value="">Toutes les natures</option>
                                <option value="message">Message</option>
                                <option value="nds">NDS</option>
                                <option value="note_royale">Note Royale</option>
                                <option value="decision">Décision</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="filter-search" placeholder="Rechercher par ID, objet, expéditeur...">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Tableau -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="table-courriers">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Urgence</th>
                                    <th>Nature</th>
                                    <th>N° Écrit</th>
                                    <th>Expéditeur</th>
                                    <th>Objet</th>
                                    <th>Divisions</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="tbody-courriers">
                                <!-- Contenu dynamique -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajouter Courrier -->
<div class="modal fade" id="modalAjouterCourrier" tabindex="-1" aria-labelledby="modalAjouterCourrierLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header" style="background: #f8f9fa; color: #495057; border-bottom: 1px solid #dee2e6;">
                <h5 class="modal-title" id="modalAjouterCourrierLabel">
                    <i class="fas fa-plus me-2"></i>Ajouter un Courrier Arrivé
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-courrier-arrive">
                    <div class="row">
                        <!-- Colonne 1 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header modal-card-header" style="background: #6B8E23; color: white;">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">ID Courrier *</label>
                                        <input type="text" class="form-control" id="id_courrier" required>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Degré d'urgence *</label>
                                                <select class="form-select" id="urgence" required>
                                                    <option value="">Choisir...</option>
                                                    <option value="extreme">Extrême</option>
                                                    <option value="extreme_urgent">Extrême Urgent</option>
                                                    <option value="urgent">Urgent</option>
                                                    <option value="routine">Routine</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Nature du courrier *</label>
                                                <select class="form-select" id="nature" required>
                                                    <option value="">Choisir...</option>
                                                    <option value="message">Message</option>
                                                    <option value="nds">NDS</option>
                                                    <option value="note_royale">Note Royale</option>
                                                    <option value="decision">Décision</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Date d'arrivée *</label>
                                                <input type="date" class="form-control" id="date_arrivee" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Date de signature</label>
                                                <input type="date" class="form-control" id="date_signature">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Numéro de l'écrit *</label>
                                        <input type="text" class="form-control" id="numero_ecrit" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Expéditeur *</label>
                                        <input type="text" class="form-control" id="expediteur" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Objet *</label>
                                        <textarea class="form-control" id="objet" rows="3" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Colonne 2 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header modal-card-header" style="background: #6B8E23; color: white;">
                                    <h6 class="mb-0"><i class="fas fa-users me-2"></i>Distribution aux Divisions</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-4">
                                        <label class="form-label text-danger fw-bold">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Divisions pour ACTION
                                        </label>
                                        <div class="divisions-grid-compact">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="instruction" id="action_instruction">
                                                <label class="form-check-label" for="action_instruction">Division Instruction</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="technique" id="action_technique">
                                                <label class="form-check-label" for="action_technique">Division Technique</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="rh" id="action_rh">
                                                <label class="form-check-label" for="action_rh">Division RH</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="mcpo" id="action_mcpo">
                                                <label class="form-check-label" for="action_mcpo">Division MCPO</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="informatique" id="action_informatique">
                                                <label class="form-check-label" for="action_informatique">Division Informatique</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="planification" id="action_planification">
                                                <label class="form-check-label" for="action_planification">Division Planification</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="asa" id="action_asa">
                                                <label class="form-check-label" for="action_asa">Division ASA</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">
                                            <i class="fas fa-info-circle me-1"></i>Divisions pour INFORMATION
                                        </label>
                                        <div class="divisions-grid-compact">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="instruction" id="info_instruction">
                                                <label class="form-check-label" for="info_instruction">Division Instruction</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="technique" id="info_technique">
                                                <label class="form-check-label" for="info_technique">Division Technique</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="rh" id="info_rh">
                                                <label class="form-check-label" for="info_rh">Division RH</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="mcpo" id="info_mcpo">
                                                <label class="form-check-label" for="info_mcpo">Division MCPO</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="informatique" id="info_informatique">
                                                <label class="form-check-label" for="info_informatique">Division Informatique</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="planification" id="info_planification">
                                                <label class="form-check-label" for="info_planification">Division Planification</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="checkbox" value="asa" id="info_asa">
                                                <label class="form-check-label" for="info_asa">Division ASA</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Degré de classification</label>
                                        <select class="form-select" id="classification">
                                            <option value="">Choisir...</option>
                                            <option value="public">Public</option>
                                            <option value="restreint">Restreint</option>
                                            <option value="confidentiel">Confidentiel</option>
                                            <option value="secret">Secret</option>
                                            <option value="tres_secret">Très Secret</option>
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Annotation</label>
                                        <textarea class="form-control" id="annotation" rows="3" placeholder="Annotation de Monsieur l'Inspecteur ou du Chef de Corps"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="ajouterCourrier()">
                    <i class="fas fa-save me-2"></i>Valider le Courrier
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.divisions-grid-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.divisions-grid-compact .form-check {
    margin: 0;
    padding: 5px 10px;
    background-color: white;
    border-radius: 3px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s;
}

.divisions-grid-compact .form-check:hover {
    background-color: #e9ecef;
    border-color: #6B8E23;
}

.divisions-grid-compact .form-check-input:checked + .form-check-label {
    color: #6B8E23;
    font-weight: 500;
}

.divisions-grid-compact .form-check-label {
    cursor: pointer;
    font-size: 0.9em;
    margin-bottom: 0;
}

.card-header h6 {
    font-weight: 600;
}

.table th {
    font-weight: 600;
    font-size: 0.875em;
}
</style>

<script>
// Variables globales
let courriersArrives = [];



// Initialisation robuste
function initializePage() {
    console.log('🚀 Initialisation de la page courriers arrivés...');

    // Vérifier que tous les éléments DOM critiques existent
    const requiredElements = ['tbody-courriers'];
    let allElementsFound = true;

    for (const elementId of requiredElements) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`❌ Élément manquant: ${elementId}`);
            allElementsFound = false;
        } else {
            console.log(`✅ Élément trouvé: ${elementId}`);
        }
    }

    if (!allElementsFound) {
        console.error('❌ Éléments DOM manquants, nouvelle tentative dans 500ms...');
        setTimeout(initializePage, 500);
        return;
    }

    // Charger les données
    loadMockData().then(() => {
        console.log('✅ Données chargées, mise à jour de l\'affichage...');
        updateCounters();
        renderTable();
        setupEvents();

        // Date par défaut
        const dateInput = document.getElementById('date_arrivee');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }

        console.log('✅ Initialisation terminée, courriers chargés:', courriersArrives.length);
    }).catch(error => {
        console.error('❌ Erreur lors du chargement des données:', error);
    });
}

// Plusieurs points d'entrée pour l'initialisation
document.addEventListener('DOMContentLoaded', initializePage);
window.addEventListener('load', function() {
    console.log('🔄 Window load event - vérification de l\'initialisation...');
    if (courriersArrives.length === 0) {
        console.log('🔄 Réinitialisation depuis window.load...');
        initializePage();
    }
});

// Initialisation de secours après 2 secondes
setTimeout(function() {
    if (courriersArrives.length === 0) {
        console.log('🔄 Initialisation de secours après 2 secondes...');
        initializePage();
    }
}, 2000);

// Charger les données depuis l'API des courriers arrivés
async function loadMockData() {
    console.log('📡 Chargement des courriers depuis l\'API...');
    try {
        const response = await fetch('/api/courriers/arrives');
        if (response.ok) {
            courriersArrives = await response.json();
            console.log('✅ Courriers chargés:', courriersArrives.length);
        } else {
            console.error('❌ Erreur lors du chargement des courriers:', response.statusText);
            courriersArrives = [];
        }
    } catch (error) {
        console.error('❌ Erreur lors du chargement des courriers:', error);
        courriersArrives = [];
    }
}

// Mettre à jour les compteurs
function updateCounters() {
    const total = courriersArrives.length;
    const attente = courriersArrives.filter(c => c.divisions_action && c.divisions_action.length > 0).length;

    // Mise à jour sécurisée des compteurs
    const countTotalEl = document.getElementById('count-total');
    const countAttenteEl = document.getElementById('count-attente');

    if (countTotalEl) {
        countTotalEl.textContent = total;
    }

    if (countAttenteEl) {
        countAttenteEl.textContent = attente;
    }

    console.log(`📊 Compteurs mis à jour: ${total} total, ${attente} en attente`);
}

// Configurer les événements
function setupEvents() {
    // Filtres
    document.getElementById('filter-urgence').addEventListener('change', renderTable);
    document.getElementById('filter-nature').addEventListener('change', renderTable);
    document.getElementById('filter-search').addEventListener('input', renderTable);
}

// Rendre le tableau
function renderTable() {
    console.log('🎨 Rendu du tableau, nombre de courriers:', courriersArrives.length);

    const tbody = document.getElementById('tbody-courriers');
    if (!tbody) {
        console.error('❌ Élément tbody-courriers non trouvé !');
        return;
    }

    tbody.innerHTML = '';

    // Appliquer les filtres
    let courriers = [...courriersArrives];

    // Récupération sécurisée des filtres
    const filterUrgenceEl = document.getElementById('filter-urgence');
    const filterNatureEl = document.getElementById('filter-nature');
    const filterSearchEl = document.getElementById('filter-search');

    const filterUrgence = filterUrgenceEl ? filterUrgenceEl.value : '';
    const filterNature = filterNatureEl ? filterNatureEl.value : '';
    const filterSearch = filterSearchEl ? filterSearchEl.value.toLowerCase() : '';

    if (filterUrgence) {
        courriers = courriers.filter(c => c.urgence === filterUrgence);
    }

    if (filterNature) {
        courriers = courriers.filter(c => c.nature === filterNature);
    }

    if (filterSearch) {
        courriers = courriers.filter(c =>
            c.id.toLowerCase().includes(filterSearch) ||
            c.objet.toLowerCase().includes(filterSearch) ||
            c.expediteur.toLowerCase().includes(filterSearch) ||
            c.numero_ecrit.toLowerCase().includes(filterSearch)
        );
    }

    courriers.forEach((courrier, index) => {
        const urgenceBadge = getUrgenceBadge(courrier.urgence);
        const natureBadge = getNatureBadge(courrier.nature);
        const divisionsText = getDivisionsText(courrier);

        const row = `
            <tr>
                <td><strong>${courrier.id}</strong></td>
                <td>${formatDate(courrier.date_arrivee)}</td>
                <td>${urgenceBadge}</td>
                <td>${natureBadge}</td>
                <td><code>${courrier.numero_ecrit}</code></td>
                <td>${courrier.expediteur}</td>
                <td class="text-truncate" style="max-width: 250px;" title="${courrier.objet}">${courrier.objet}</td>
                <td>${divisionsText}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="voirDetails(${index})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="scannerCourrier(${index})" title="Scanner">
                        <i class="fas fa-scan"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });

    console.log('✅ Tableau rendu avec', courriers.length, 'courriers affichés');
}

// Ajouter un courrier
async function ajouterCourrier() {
    // Récupérer les données du formulaire
    const id = document.getElementById('id_courrier').value;
    const urgence = document.getElementById('urgence').value;
    const nature = document.getElementById('nature').value;
    const dateArrivee = document.getElementById('date_arrivee').value;
    const dateSignature = document.getElementById('date_signature').value;
    const numeroEcrit = document.getElementById('numero_ecrit').value;
    const expediteur = document.getElementById('expediteur').value;
    const objet = document.getElementById('objet').value;
    const classification = document.getElementById('classification').value;
    const annotation = document.getElementById('annotation').value;

    // Validation
    if (!id || !urgence || !nature || !dateArrivee || !numeroEcrit || !expediteur || !objet) {
        alert('Veuillez remplir tous les champs obligatoires !');
        return;
    }

    // Récupérer les divisions sélectionnées
    const divisionsAction = [];
    const divisionsInfo = [];

    document.querySelectorAll('input[id^="action_"]:checked').forEach(checkbox => {
        divisionsAction.push(checkbox.value);
    });

    document.querySelectorAll('input[id^="info_"]:checked').forEach(checkbox => {
        divisionsInfo.push(checkbox.value);
    });

    // Créer le nouveau courrier
    const nouveauCourrier = {
        id: id, // Utiliser l'ID saisi par l'utilisateur
        urgence,
        nature,
        date_arrivee: dateArrivee,
        date_signature: dateSignature || null,
        numero_ecrit: numeroEcrit,
        expediteur,
        objet,
        classification: classification || null,
        annotation: annotation || null,
        divisions_action: divisionsAction,
        divisions_info: divisionsInfo
    };

    try {
        // Envoyer à l'API des courriers arrivés
        const response = await fetch('/api/courriers/arrives', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(nouveauCourrier)
        });

        if (response.ok) {
            const courrierAjoute = await response.json();

            // Recharger les données depuis l'API
            await loadMockData();

            // Notifier les divisions
            notifierDivisions(courrierAjoute);

            // Réinitialiser et fermer
            document.getElementById('form-courrier-arrive').reset();
            document.getElementById('date_arrivee').value = new Date().toISOString().split('T')[0];

            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAjouterCourrier'));
            modal.hide();

            updateCounters();
            renderTable();

            alert('✅ Courrier arrivé ajouté avec succès!');
        } else {
            const error = await response.json();
            alert('❌ Erreur: ' + error.error);
        }
    } catch (error) {
        console.error('Erreur lors de l\'ajout du courrier:', error);
        alert('❌ Erreur lors de l\'ajout du courrier');
    }
}

// Fonctions utilitaires
function getUrgenceBadge(urgence) {
    const badges = {
        'extreme': '<span class="badge bg-danger">Extrême</span>',
        'extreme_urgent': '<span class="badge bg-danger">Extrême Urgent</span>',
        'urgent': '<span class="badge bg-warning">Urgent</span>',
        'routine': '<span class="badge bg-secondary">Routine</span>'
    };
    return badges[urgence] || '<span class="badge bg-light text-dark">-</span>';
}

function getNatureBadge(nature) {
    const badges = {
        'message': '<span class="badge bg-info">Message</span>',
        'nds': '<span class="badge bg-primary">NDS</span>',
        'note_royale': '<span class="badge bg-warning text-dark">Note Royale</span>',
        'decision': '<span class="badge bg-success">Décision</span>'
    };
    return badges[nature] || '<span class="badge bg-light text-dark">-</span>';
}

function getDivisionsText(courrier) {
    let text = '';
    if (courrier.divisions_action && courrier.divisions_action.length > 0) {
        text += '<small class="text-danger fw-bold">Action: ' + courrier.divisions_action.join(', ') + '</small>';
    }
    if (courrier.divisions_info && courrier.divisions_info.length > 0) {
        if (text) text += '<br>';
        text += '<small class="text-info">Info: ' + courrier.divisions_info.join(', ') + '</small>';
    }
    return text || '<small class="text-muted">Aucune</small>';
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}

function resetFilters() {
    document.getElementById('filter-urgence').value = '';
    document.getElementById('filter-nature').value = '';
    document.getElementById('filter-search').value = '';
    renderTable();
}

function voirDetails(index) {
    const courrier = courriersArrives[index];

    let details = `
        <div class="modal fade" id="modalDetails" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Détails du Courrier ${courrier.id}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6"><strong>ID:</strong> ${courrier.id}</div>
                            <div class="col-md-6"><strong>Urgence:</strong> ${getUrgenceBadge(courrier.urgence)}</div>
                            <div class="col-md-6"><strong>Date d'arrivée:</strong> ${formatDate(courrier.date_arrivee)}</div>
                            <div class="col-md-6"><strong>Nature:</strong> ${getNatureBadge(courrier.nature)}</div>
                            <div class="col-md-6"><strong>N° Écrit:</strong> <code>${courrier.numero_ecrit}</code></div>
                            <div class="col-md-6"><strong>Date signature:</strong> ${formatDate(courrier.date_signature)}</div>
                            <div class="col-12 mt-2"><strong>Expéditeur:</strong> ${courrier.expediteur}</div>
                            <div class="col-12 mt-2"><strong>Objet:</strong> ${courrier.objet}</div>
                            <div class="col-12 mt-2"><strong>Classification:</strong> ${courrier.classification || 'Non spécifiée'}</div>
                            <div class="col-12 mt-2"><strong>Divisions pour action:</strong> ${(courrier.divisions_action || []).join(', ') || 'Aucune'}</div>
                            <div class="col-12 mt-2"><strong>Divisions pour info:</strong> ${(courrier.divisions_info || []).join(', ') || 'Aucune'}</div>
                            <div class="col-12 mt-2"><strong>Annotation:</strong> ${courrier.annotation || 'Aucune'}</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprimer l'ancienne modale si elle existe
    const existingModal = document.getElementById('modalDetails');
    if (existingModal) {
        existingModal.remove();
    }

    // Ajouter la nouvelle modale
    document.body.insertAdjacentHTML('beforeend', details);

    // Afficher la modale
    const modal = new bootstrap.Modal(document.getElementById('modalDetails'));
    modal.show();
}

function scannerCourrier(index) {
    const courrier = courriersArrives[index];
    alert(`📄 Fonction de scan pour le courrier ${courrier.id}\n\nCette fonctionnalité permettra de:\n- Scanner le document physique\n- L'associer au courrier électronique\n- Le rendre accessible aux divisions concernées`);
}

function notifierDivisions(courrier) {
    const divisions = [...(courrier.divisions_action || []), ...(courrier.divisions_info || [])];
    console.log(`📧 Notification envoyée aux divisions: ${divisions.join(', ')} pour le courrier ${courrier.id}`);
}
</script>

<style>
/* Désactiver le pattern de camouflage pour les en-têtes des modals */
.modal-card-header::before {
    display: none !important;
}
</style>
{% endblock %}
