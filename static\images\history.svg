<?xml version="1.0" encoding="UTF-8"?>
<svg width="512px" height="512px" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>Icône Historique</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient">
            <stop stop-color="#198754" offset="0%"></stop>
            <stop stop-color="#20c997" offset="100%"></stop>
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="10" result="blur"></feGaussianBlur>
            <feOffset in="blur" dx="0" dy="0" result="offsetBlur"></feOffset>
            <feFlood flood-color="#198754" flood-opacity="0.3" result="shadowColor"></feFlood>
            <feComposite in="shadowColor" in2="offsetBlur" operator="in" result="shadowBlur"></feComposite>
            <feComposite in="SourceGraphic" in2="shadowBlur" operator="over"></feComposite>
        </filter>
    </defs>
    <g id="history-icon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" filter="url(#shadow)">
        <!-- Cercle extérieur -->
        <circle id="outer-circle" stroke="url(#linearGradient)" stroke-width="20" cx="256" cy="256" r="220"></circle>
        
        <!-- Cercle intérieur -->
        <circle id="inner-circle" fill="rgba(25, 135, 84, 0.1)" cx="256" cy="256" r="200"></circle>
        
        <!-- Flèche circulaire -->
        <path d="M256,96 C362.5,96 448,181.5 448,288 C448,394.5 362.5,480 256,480 C149.5,480 64,394.5 64,288" 
              id="circular-arrow" 
              stroke="url(#linearGradient)" 
              stroke-width="24" 
              stroke-linecap="round" 
              fill="none"></path>
        
        <!-- Pointe de flèche -->
        <polygon id="arrow-head" 
                 fill="url(#linearGradient)" 
                 transform="translate(64, 288) rotate(-90) translate(-64, -288)" 
                 points="64 268 94 308 34 308"></polygon>
        
        <!-- Aiguilles d'horloge -->
        <line id="hour-hand" 
              x1="256" y1="256" x2="256" y2="160" 
              stroke="url(#linearGradient)" 
              stroke-width="12" 
              stroke-linecap="round"></line>
        
        <line id="minute-hand" 
              x1="256" y1="256" x2="350" y2="256" 
              stroke="url(#linearGradient)" 
              stroke-width="8" 
              stroke-linecap="round"></line>
        
        <!-- Centre de l'horloge -->
        <circle id="clock-center" fill="url(#linearGradient)" cx="256" cy="256" r="15"></circle>
        
        <!-- Marques des heures -->
        <line x1="256" y1="96" x2="256" y2="116" stroke="url(#linearGradient)" stroke-width="8" stroke-linecap="round"></line>
        <line x1="416" y1="256" x2="396" y2="256" stroke="url(#linearGradient)" stroke-width="8" stroke-linecap="round"></line>
        <line x1="256" y1="416" x2="256" y2="396" stroke="url(#linearGradient)" stroke-width="8" stroke-linecap="round"></line>
        <line x1="96" y1="256" x2="116" y2="256" stroke="url(#linearGradient)" stroke-width="8" stroke-linecap="round"></line>
        
        <!-- Marques diagonales -->
        <line x1="336" y1="176" x2="322" y2="190" stroke="url(#linearGradient)" stroke-width="6" stroke-linecap="round"></line>
        <line x1="336" y1="336" x2="322" y2="322" stroke="url(#linearGradient)" stroke-width="6" stroke-linecap="round"></line>
        <line x1="176" y1="336" x2="190" y2="322" stroke="url(#linearGradient)" stroke-width="6" stroke-linecap="round"></line>
        <line x1="176" y1="176" x2="190" y2="190" stroke="url(#linearGradient)" stroke-width="6" stroke-linecap="round"></line>
    </g>
</svg>
