/* Conteneur principal */
.history-animation-container {
    display: block !important;
    position: relative !important;
    margin: 0 auto 1rem auto !important;
    text-align: center !important;
    width: 220px !important;
    height: 220px !important;
    filter: drop-shadow(0 10px 25px rgba(25, 135, 84, 0.5)) !important;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Animation de l'icône */
.history-icon-css {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex !important;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 10;
}

/* Cercle avec effet néon */
.history-icon-css .circle {
    width: 90%;
    height: 90%;
    border: 8px solid rgba(25, 135, 84, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(25, 135, 84, 0.8), 0 0 30px rgba(25, 135, 84, 0.5);
    animation: pulse 2s infinite ease-in-out;
}

/* Flèche améliorée */
.history-icon-css .arrow {
    position: absolute;
    width: 40%;
    height: 8px;
    background: linear-gradient(45deg, #198754, #2ad888);
    top: 50%;
    left: 50%;
    transform-origin: left center;
    transform: translate(0, -50%) rotate(45deg);
    animation: rotate 3s infinite linear;
    box-shadow: 0 0 10px rgba(25, 135, 84, 0.5);
}

/* Clignotement du point */
.history-icon-css .dot {
    position: absolute;
    width: 18px;
    height: 18px;
    background: linear-gradient(45deg, #198754, #2ad888);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: blink 1.2s infinite;
}

/* Animations mises à jour */
@keyframes pulse {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.15); opacity: 1; }
    100% { transform: scale(1); opacity: 0.8; }
}

@keyframes rotate {
    from { transform: translate(0, -50%) rotate(0deg); }
    to { transform: translate(0, -50%) rotate(360deg); }
}

@keyframes blink {
    0% { opacity: 0.3; }
    50% { opacity: 1; }
    100% { opacity: 0.3; }
}
