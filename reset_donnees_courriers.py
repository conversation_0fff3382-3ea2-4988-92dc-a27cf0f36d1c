#!/usr/bin/env python3
"""
Script pour effacer les données de test existantes et les remplacer par de nouvelles données
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime, date
import requests
import json

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion_vehicules'
}

BASE_URL = "http://127.0.0.1:3000"

def clear_existing_data():
    """Effacer toutes les données de test existantes"""
    print("🗑️ Effacement des données existantes...")
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Supprimer dans l'ordre pour respecter les contraintes de clés étrangères
        cursor.execute("DELETE FROM courrier_division_info")
        cursor.execute("DELETE FROM courrier_division_action")
        cursor.execute("DELETE FROM courrier_arrive")
        cursor.execute("DELETE FROM courrier_envoye")
        cursor.execute("DELETE FROM courrier")
        
        # Réinitialiser les auto-increment
        cursor.execute("ALTER TABLE courrier_arrive AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_envoye AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_division_action AUTO_INCREMENT = 1")
        cursor.execute("ALTER TABLE courrier_division_info AUTO_INCREMENT = 1")
        
        connection.commit()
        print("✅ Données existantes effacées")
        
    except Error as e:
        print(f"❌ Erreur lors de l'effacement: {e}")
        if connection:
            connection.rollback()
    
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

def insert_new_data():
    """Insérer les nouvelles données spécifiées"""
    print("\n📝 Insertion des nouvelles données...")
    
    # Données pour les courriers arrivés
    courriers_arrives = [
        {
            "id": "1458",
            "urgence": "urgent",
            "nature": "message",
            "date_arrivee": "2024-01-15",
            "date_signature": "2024-01-14",
            "numero_ecrit": "14556",
            "expediteur": "1° Bureau",
            "objet": "Application de gestion de courrier",
            "classification": "public",
            "annotation": "Traitement prioritaire",
            "divisions_action": ["technique", "instruction"],
            "divisions_info": ["rh"]
        },
        {
            "id": "1459",
            "urgence": "routine",
            "nature": "nds",
            "date_arrivee": "2024-01-16",
            "date_signature": "2024-01-15",
            "numero_ecrit": "14557",
            "expediteur": "2° Bureau",
            "objet": "Test de déploiement du système",
            "classification": "restreint",
            "annotation": "Phase de test",
            "divisions_action": ["informatique"],
            "divisions_info": ["technique", "planification"]
        },
        {
            "id": "1460",
            "urgence": "urgent",
            "nature": "decision",
            "date_arrivee": "2024-01-17",
            "date_signature": "2024-01-16",
            "numero_ecrit": "14558",
            "expediteur": "3° Bureau",
            "objet": "Projet de modernisation des systèmes",
            "classification": "confidentiel",
            "annotation": "Validation requise",
            "divisions_action": ["planification", "technique"],
            "divisions_info": ["instruction", "mcpo"]
        }
    ]
    
    # Données pour les courriers envoyés
    courriers_envoyes = [
        {
            "id": "1461",
            "numero_ecrit": "14561",
            "division_emettrice": "technique",
            "date_depart": "2024-01-18",
            "nature": "message",
            "objet": "Application de gestion de courrier - Réponse",
            "destinataire": "1° Bureau",
            "observations": "Application validée et déployée"
        },
        {
            "id": "1462",
            "numero_ecrit": "14562",
            "division_emettrice": "informatique",
            "date_depart": "2024-01-19",
            "nature": "nds",
            "objet": "Test de déploiement - Rapport final",
            "destinataire": "2° Bureau",
            "observations": "Tests concluants, système opérationnel"
        },
        {
            "id": "1463",
            "numero_ecrit": "14563",
            "division_emettrice": "planification",
            "date_depart": "2024-01-20",
            "nature": "decision",
            "objet": "Projet de modernisation - Validation",
            "destinataire": "3° Bureau",
            "observations": "Projet approuvé, mise en œuvre autorisée"
        },
        {
            "id": "1464",
            "numero_ecrit": "14564",
            "division_emettrice": "instruction",
            "date_depart": "2024-01-21",
            "nature": "message",
            "objet": "Formation du personnel - Programme établi",
            "destinataire": "4° Bureau",
            "observations": "Formation programmée pour février 2024"
        },
        {
            "id": "1465",
            "numero_ecrit": "14565",
            "division_emettrice": "rh",
            "date_depart": "2024-01-22",
            "nature": "nds",
            "objet": "Gestion des ressources humaines - Directive",
            "destinataire": "5° Bureau",
            "observations": "Nouvelle procédure de gestion RH"
        },
        {
            "id": "1466",
            "numero_ecrit": "14566",
            "division_emettrice": "mcpo",
            "date_depart": "2024-01-23",
            "nature": "note_royale",
            "objet": "Maintenance et contrôle - Rapport mensuel",
            "destinataire": "6° Bureau",
            "observations": "Rapport de maintenance janvier 2024"
        }
    ]
    
    # Insérer les courriers arrivés via API
    print("📥 Insertion des courriers arrivés...")
    for courrier in courriers_arrives:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/arrives",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Courrier arrivé ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur pour {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour {courrier['id']}: {e}")
    
    # Insérer les courriers envoyés via API
    print("\n📤 Insertion des courriers envoyés...")
    for courrier in courriers_envoyes:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/envoyes",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Courrier envoyé ajouté: {courrier['id']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur pour {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour {courrier['id']}: {e}")

def verify_new_data():
    """Vérifier que les nouvelles données ont été insérées"""
    print("\n🔍 Vérification des nouvelles données...")
    
    try:
        # Vérifier courriers arrivés
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers_arrives = response.json()
            print(f"📥 Courriers arrivés: {len(courriers_arrives)}")
            for courrier in courriers_arrives:
                print(f"   • {courrier['id']}: {courrier['objet']}")
        
        # Vérifier courriers envoyés
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        if response.status_code == 200:
            courriers_envoyes = response.json()
            print(f"📤 Courriers envoyés: {len(courriers_envoyes)}")
            for courrier in courriers_envoyes:
                print(f"   • {courrier['id']}: {courrier['objet']}")
                
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")

def main():
    """Fonction principale"""
    print("🔄 RÉINITIALISATION DES DONNÉES DE COURRIER")
    print("=" * 60)
    
    # Vérifier que l'application Flask fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ Application Flask non accessible sur {BASE_URL}")
            print("   Assurez-vous que Flask fonctionne avec: python app.py")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter à Flask: {e}")
        print("   Assurez-vous que Flask fonctionne avec: python app.py")
        return
    
    print(f"✅ Application Flask accessible")
    
    # Étape 1: Effacer les données existantes
    clear_existing_data()
    
    # Étape 2: Insérer les nouvelles données
    insert_new_data()
    
    # Étape 3: Vérifier les nouvelles données
    verify_new_data()
    
    print("\n" + "=" * 60)
    print("🎉 RÉINITIALISATION TERMINÉE")
    print("=" * 60)
    
    print("\n📋 RÉSUMÉ DES NOUVELLES DONNÉES:")
    print("📥 Courriers Arrivés:")
    print("   • ID 1458 - N° 14556 - 1° Bureau - Application de gestion")
    print("   • ID 1459 - N° 14557 - 2° Bureau - Test de déploiement")
    print("   • ID 1460 - N° 14558 - 3° Bureau - Projet de modernisation")
    
    print("\n📤 Courriers Envoyés:")
    print("   • ID 1461 - N° 14561 - Technique - Application de gestion")
    print("   • ID 1462 - N° 14562 - Informatique - Test de déploiement")
    print("   • ID 1463 - N° 14563 - Planification - Projet de modernisation")
    print("   • ID 1464 - N° 14564 - Instruction - Formation du personnel")
    print("   • ID 1465 - N° 14565 - RH - Gestion des ressources humaines")
    print("   • ID 1466 - N° 14566 - MCPO - Maintenance et contrôle")
    
    print("\n🌐 Vérifiez maintenant dans l'interface web:")
    print(f"   • Courriers arrivés: {BASE_URL}/courrier/arrives")
    print(f"   • Courriers envoyés: {BASE_URL}/courrier/envoyes")

if __name__ == "__main__":
    main()
