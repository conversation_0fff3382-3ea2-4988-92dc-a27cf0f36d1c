{% extends "rh/base_rh.html" %}

{% block title %}Recherche Personnel - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-search"></i>
                                Recherche et Consultation du Personnel
                            </h2>
                            <small style="color: var(--text-light);">Recherche multicritères et consultation des fiches personnelles</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                                <i class="fas fa-user-plus"></i> Nouveau Militaire
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Critères de Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <!-- Ligne 1 : Critères principaux -->
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Nom/Prénom</label>
                            <input type="text" name="search" class="form-control"
                                   placeholder="Rechercher par nom ou prénom..." value="{{ search_term or '' }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Matricule</label>
                            <input type="text" name="matricule" class="form-control"
                                   placeholder="Ex: 1236589..." value="{{ matricule }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">CIN</label>
                            <input type="text" name="cin" class="form-control"
                                   placeholder="Ex: D784517..." value="{{ cin or '' }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">GSM</label>
                            <input type="text" name="gsm" class="form-control"
                                   placeholder="Ex: 0612345678..." value="{{ gsm or '' }}">
                        </div>

                        <!-- Ligne 2 : Critères militaires -->
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Catégorie</label>
                            <select name="categorie_id" class="form-control">
                                <option value="">Toutes les catégories</option>
                                {% if categories %}
                                    {% for categorie in categories %}
                                    <option value="{{ categorie.id_categorie }}" {% if categorie_id and categorie.id_categorie == categorie_id %}selected{% endif %}>
                                        {{ categorie.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Grade</label>
                            <select name="grade_id" class="form-control">
                                <option value="">Tous les grades</option>
                                {% if grades %}
                                    {% for grade in grades %}
                                    <option value="{{ grade.id_grade }}" {% if grade_id and grade.id_grade == grade_id %}selected{% endif %}>
                                        {{ grade.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Service/Arme</label>
                            <select name="service_id" class="form-control">
                                <option value="">Tous les services</option>
                                {% if services %}
                                    {% for service in services %}
                                    <option value="{{ service.id_service }}" {% if service_id and service.id_service == service_id %}selected{% endif %}>
                                        {{ service.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Unité</label>
                            <select name="unite_id" class="form-control">
                                <option value="">Toutes les unités</option>
                                {% if unites %}
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}" {% if unite_id and unite.id_unite == unite_id %}selected{% endif %}>
                                        {{ unite.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>

                        <!-- Ligne 3 : Critères personnels -->
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Genre</label>
                            <select name="genre_id" class="form-control">
                                <option value="">Tous les genres</option>
                                {% if genres %}
                                    {% for genre in genres %}
                                    <option value="{{ genre.id_genre }}" {% if genre_id and genre.id_genre == genre_id %}selected{% endif %}>
                                        {{ genre.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">État Matrimonial</label>
                            <select name="etat_matrimonial_id" class="form-control">
                                <option value="">Tous les états</option>
                                {% if etats_matrimoniaux %}
                                    {% for etat in etats_matrimoniaux %}
                                    <option value="{{ etat.id_etat }}" {% if etat_matrimonial_id and etat.id_etat == etat_matrimonial_id %}selected{% endif %}>
                                        {{ etat.libelle }}
                                    </option>
                                    {% endfor %}
                                {% endif %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Aptitude Médicale</label>
                            <select name="aptitude" class="form-control">
                                <option value="">Toutes les aptitudes</option>
                                <option value="Apte" {% if aptitude == 'Apte' %}selected{% endif %}>Apte</option>
                                <option value="Inapte" {% if aptitude == 'Inapte' %}selected{% endif %}>Inapte</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">Lieu de Naissance</label>
                            <input type="text" name="lieu_naissance" class="form-control"
                                   placeholder="Ex: Rabat, Casablanca..." value="{{ lieu_naissance or '' }}">
                        </div>

                        <!-- CRITÈRES AVANCÉS SUPPLÉMENTAIRES (MASQUÉS PAR DÉFAUT) -->
                        <div class="col-md-3" id="critere-age" style="display: none;">
                            <label class="form-label fw-bold">Âge</label>
                            <select name="tranche_age" class="form-control">
                                <option value="">Tous les âges</option>
                                <option value="18-25">18-25 ans</option>
                                <option value="26-35">26-35 ans</option>
                                <option value="36-45">36-45 ans</option>
                                <option value="46-55">46-55 ans</option>
                                <option value="56+">56+ ans</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-anciennete" style="display: none;">
                            <label class="form-label fw-bold">Ancienneté</label>
                            <select name="anciennete" class="form-control">
                                <option value="">Toutes anciennetés</option>
                                <option value="0-5">0-5 ans</option>
                                <option value="6-10">6-10 ans</option>
                                <option value="11-20">11-20 ans</option>
                                <option value="21-30">21-30 ans</option>
                                <option value="30+">30+ ans</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-taille" style="display: none;">
                            <label class="form-label fw-bold">Taille</label>
                            <select name="tranche_taille" class="form-control">
                                <option value="">Toutes tailles</option>
                                <option value="150-160">150-160 cm</option>
                                <option value="161-170">161-170 cm</option>
                                <option value="171-180">171-180 cm</option>
                                <option value="181-190">181-190 cm</option>
                                <option value="191+">191+ cm</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-fonction" style="display: none;">
                            <label class="form-label fw-bold">Fonction</label>
                            <input type="text" name="fonction" class="form-control"
                                   placeholder="Ex: Commandant, Chef..." value="{{ fonction or '' }}">
                        </div>

                        <!-- CRITÈRES AVANCÉS DISCIPLINAIRES ET OPÉRATIONNELS -->
                        <div class="col-md-3" id="critere-sanctions" style="display: none;">
                            <label class="form-label fw-bold">Sanctions</label>
                            <select name="statut_sanctions" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="aucune">Aucune sanction</option>
                                <option value="en_cours">Sanction en cours</option>
                                <option value="historique">Historique de sanctions</option>
                                <option value="recidiviste">Récidiviste (2+ sanctions)</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-absences" style="display: none;">
                            <label class="form-label fw-bold">Absences</label>
                            <select name="statut_absences" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="en_permission">En permission actuellement</option>
                                <option value="en_ptc">En PTC actuellement</option>
                                <option value="absent_longue_duree">Absence longue durée (30+ jours)</option>
                                <option value="retour_recent">Retour récent (7 derniers jours)</option>
                                <option value="jamais_absent">Jamais d'absence</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-detachements" style="display: none;">
                            <label class="form-label fw-bold">Détachements</label>
                            <select name="statut_detachements" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="en_detachement">En détachement actuellement</option>
                                <option value="retour_detachement">Retour de détachement récent</option>
                                <option value="detachements_multiples">Détachements multiples (3+)</option>
                                <option value="jamais_detache">Jamais détaché</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-mutations" style="display: none;">
                            <label class="form-label fw-bold">Mutations</label>
                            <select name="statut_mutations" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="mutation_recente">Mutation récente (6 derniers mois)</option>
                                <option value="stable_unite">Stable dans l'unité (5+ ans)</option>
                                <option value="mutations_frequentes">Mutations fréquentes (3+ mutations)</option>
                                <option value="jamais_mute">Jamais muté</option>
                            </select>
                        </div>

                        <!-- CRITÈRES AVANCÉS MÉDICAUX ET FAMILIAUX -->
                        <div class="col-md-3" id="critere-medical" style="display: none;">
                            <label class="form-label fw-bold">Situation Médicale</label>
                            <select name="statut_medical" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="hospitalisation_recente">Hospitalisation récente (6 mois)</option>
                                <option value="vaccinations_a_jour">Vaccinations à jour</option>
                                <option value="visite_medicale_due">Visite médicale due</option>
                                <option value="inapte_temporaire">Inapte temporaire</option>
                                <option value="suivi_medical">Sous suivi médical</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-famille" style="display: none;">
                            <label class="form-label fw-bold">Situation Familiale</label>
                            <select name="statut_famille" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="marie_avec_enfants">Marié avec enfants</option>
                                <option value="celibataire_sans_enfants">Célibataire sans enfants</option>
                                <option value="famille_nombreuse">Famille nombreuse (4+ enfants)</option>
                                <option value="conjoint_militaire">Conjoint militaire</option>
                                <option value="situation_incomplete">Données familiales incomplètes</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-carriere" style="display: none;">
                            <label class="form-label fw-bold">Évolution Carrière</label>
                            <select name="statut_carriere" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="promotion_recente">Promotion récente (2 ans)</option>
                                <option value="stagnation">Stagnation (5+ ans même grade)</option>
                                <option value="progression_rapide">Progression rapide</option>
                                <option value="fin_carriere">Fin de carrière (55+ ans)</option>
                                <option value="debut_carriere">Début de carrière (moins 5 ans)</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-operationnel" style="display: none;">
                            <label class="form-label fw-bold">Expérience Opérationnelle</label>
                            <select name="statut_operationnel" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="sejour_ops_recent">Séjour opérationnel récent</option>
                                <option value="experience_ops_multiple">Expérience opérationnelle multiple</option>
                                <option value="jamais_ops">Jamais en opération</option>
                                <option value="veterran_ops">Vétéran opérationnel (5+ séjours)</option>
                            </select>
                        </div>

                        <!-- CRITÈRES AVANCÉS ADMINISTRATIFS ET ALERTES -->
                        <div class="col-md-3" id="critere-documents" style="display: none;">
                            <label class="form-label fw-bold">Documents</label>
                            <select name="statut_documents" class="form-control">
                                <option value="">Tous statuts</option>
                                <option value="documents_expires">Documents expirés</option>
                                <option value="documents_a_renouveler">À renouveler (30 jours)</option>
                                <option value="passeport_valide">Passeport valide</option>
                                <option value="documents_incomplets">Documents incomplets</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-alertes" style="display: none;">
                            <label class="form-label fw-bold">Alertes Système</label>
                            <select name="type_alerte" class="form-control">
                                <option value="">Aucune alerte</option>
                                <option value="desertion">Désertion en cours</option>
                                <option value="retard_permission">Retard de permission</option>
                                <option value="donnees_obsoletes">Données obsolètes</option>
                                <option value="verification_requise">Vérification requise</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="critere-performance" style="display: none;">
                            <label class="form-label fw-bold">Indicateurs Performance</label>
                            <select name="indicateur_performance" class="form-control">
                                <option value="">Tous indicateurs</option>
                                <option value="exemplaire">Dossier exemplaire</option>
                                <option value="problematique">Dossier problématique</option>
                                <option value="surveillance">Sous surveillance</option>
                                <option value="merite_promotion">Mérite promotion</option>
                                <option value="formation_requise">Formation requise</option>
                            </select>
                        </div>

                        <!-- Ligne 4 : Boutons d'action -->
                        <div class="col-md-6">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military btn-lg">
                                    <i class="fas fa-search"></i> Rechercher
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <div class="d-grid">
                                <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-eraser"></i> Effacer
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label fw-bold">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-info btn-lg" onclick="toggleAdvancedSearch()">
                                    <i class="fas fa-cog"></i> <span id="advanced-btn-text">Avancé</span>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats de la Recherche -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-users"></i>
                                Résultats de la Recherche
                                {% if personnel %}
                                <span class="badge bg-light text-dark ms-2">{{ personnel.total }} résultat(s)</span>
                                {% endif %}
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            {% if alerte == 'cin' %}
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-exclamation-triangle"></i> CIN à renouveler
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if personnel and personnel.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom Complet</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Service</th>
                                    <th>Fonction</th>
                                    <th>CIN</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for militaire in personnel.items %}
                                <tr class="table-row-hover">
                                    <td>
                                        <strong class="text-primary">{{ militaire.matricule }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ militaire.nom }} {{ militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ militaire.nom_ar }} {{ militaire.prenom_ar }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <small>{{ militaire.unite.code if militaire.unite else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ militaire.service.code_court if militaire.service else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ militaire.fonction[:30] }}{% if militaire.fonction and militaire.fonction|length > 30 %}...{% endif %}</small>
                                    </td>
                                    <td>
                                        {% if militaire.cin_date_expiration %}
                                        {% set jours_restants = (militaire.cin_date_expiration - date.today()).days %}
                                        {% if jours_restants <= 0 %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation"></i> Expirée
                                        </span>
                                        {% elif jours_restants <= 30 %}
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-clock"></i> {{ jours_restants }}j
                                        </span>
                                        {% else %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Valide
                                        </span>
                                        {% endif %}
                                        {% else %}
                                        <span class="badge bg-secondary">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" 
                                               class="btn btn-info btn-sm" title="Voir la fiche complète">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-warning btn-sm" 
                                                    onclick="modifierPersonnel('{{ militaire.matricule }}')" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-success btn-sm" 
                                                    onclick="ajouterInfo('{{ militaire.matricule }}')" title="Ajouter info">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if personnel.pages > 1 %}
                    <div class="card-footer">
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination justify-content-center mb-0">
                                {% if personnel.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=personnel.prev_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                        <i class="fas fa-chevron-left"></i> Précédent
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% for page_num in personnel.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != personnel.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=page_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                        {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if personnel.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('rh.recherche_personnel', page=personnel.next_num, search=search, matricule=matricule, grade_id=grade_id, unite_id=unite_id, service_id=service_id, alerte=alerte) }}">
                                        Suivant <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun résultat trouvé</h5>
                        <p class="text-muted">
                            {% if search or matricule or grade_id or unite_id or service_id %}
                            Aucun militaire ne correspond aux critères de recherche.
                            {% else %}
                            Utilisez les filtres ci-dessus pour rechercher du personnel.
                            {% endif %}
                        </p>
                        <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                            <i class="fas fa-user-plus"></i> Ajouter un Nouveau Militaire
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.table-row-hover {
    transition: all 0.3s ease;
}

.table-row-hover:hover {
    background-color: rgba(212, 175, 55, 0.1) !important;
    transform: scale(1.01);
}

.form-control:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

.badge {
    font-size: 0.75em;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function modifierPersonnel(matricule) {
    alert('Modification du personnel ' + matricule + ' - Fonctionnalité en développement');
}

function ajouterInfo(matricule) {
    alert('Ajout d\'informations pour ' + matricule + ' - Fonctionnalité en développement');
}

// NOUVELLE FONCTION : Toggle pour la recherche avancée
function toggleAdvancedSearch() {
    // Liste COMPLÈTE des critères avancés SUPPLÉMENTAIRES
    const criteresAvances = [
        // Critères de base avancés
        'critere-age', 'critere-anciennete', 'critere-taille', 'critere-fonction',
        // Critères disciplinaires et opérationnels
        'critere-sanctions', 'critere-absences', 'critere-detachements', 'critere-mutations',
        // Critères médicaux et familiaux
        'critere-medical', 'critere-famille', 'critere-carriere', 'critere-operationnel',
        // Critères administratifs et alertes
        'critere-documents', 'critere-alertes', 'critere-performance'
    ];

    const btnText = document.getElementById('advanced-btn-text');
    const premierCritere = document.getElementById('critere-age');

    if (premierCritere) {
        const isHidden = premierCritere.style.display === 'none';

        if (isHidden) {
            // Afficher tous les critères avancés SUPPLÉMENTAIRES - AFFICHAGE INSTANTANÉ
            criteresAvances.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = 'block';
                    element.style.opacity = '1';
                }
            });
            btnText.textContent = 'Simple';
        } else {
            // Masquer tous les critères avancés SUPPLÉMENTAIRES - MASQUAGE INSTANTANÉ
            criteresAvances.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                }
            });
            btnText.textContent = 'Avancé';
        }
    }
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    // Masquer initialement TOUS les critères avancés supplémentaires
    const criteresAvances = [
        // Critères de base avancés
        'critere-age', 'critere-anciennete', 'critere-taille', 'critere-fonction',
        // Critères disciplinaires et opérationnels
        'critere-sanctions', 'critere-absences', 'critere-detachements', 'critere-mutations',
        // Critères médicaux et familiaux
        'critere-medical', 'critere-famille', 'critere-carriere', 'critere-operationnel',
        // Critères administratifs et alertes
        'critere-documents', 'critere-alertes', 'critere-performance'
    ];

    criteresAvances.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Animation des lignes du tableau
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
