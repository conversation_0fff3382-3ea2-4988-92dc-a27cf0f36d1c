{% extends "base.html" %}

{% block title %}Recherche Personnel - RH{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search"></i>
                        Recherche Avancée du Personnel
                    </h3>
                </div>
                
                <div class="card-body">
                    <form id="formRechercheAvancee" method="GET" action="{{ url_for('rh.liste_personnel') }}">
                        <!-- Section Informations personnelles -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> Informations personnelles
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="matricule">Matricule</label>
                                    <input type="text" class="form-control" id="matricule" name="matricule" 
                                           placeholder="Ex: 1234567">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom">Nom</label>
                                    <input type="text" class="form-control" id="nom" name="nom" 
                                           placeholder="Nom de famille">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom">Prénom</label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" 
                                           placeholder="Prénom">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="cin">CIN</label>
                                    <input type="text" class="form-control" id="cin" name="cin" 
                                           placeholder="Ex: A123456">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Militaire -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-medal"></i> Informations militaires
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="categorie">Catégorie</label>
                                    <select class="form-control" id="categorie" name="categorie">
                                        <option value="">Toutes les catégories</option>
                                        {% for categorie in categories %}
                                        <option value="{{ categorie.id_categorie }}">{{ categorie.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="grade">Grade</label>
                                    <select class="form-control" id="grade" name="grade">
                                        <option value="">Tous les grades</option>
                                        {% for grade in grades %}
                                        <option value="{{ grade.id_grade }}">{{ grade.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="arme">Arme</label>
                                    <select class="form-control" id="arme" name="arme">
                                        <option value="">Toutes les armes</option>
                                        {% for arme in armes %}
                                        <option value="{{ arme.id_arme }}">{{ arme.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="unite">Unité</label>
                                    <select class="form-control" id="unite" name="unite">
                                        <option value="">Toutes les unités</option>
                                        {% for unite in unites %}
                                        <option value="{{ unite.id_unite }}">{{ unite.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Dates -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-calendar"></i> Critères de dates
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_naissance_debut">Date naissance (début)</label>
                                    <input type="date" class="form-control" id="date_naissance_debut" name="date_naissance_debut">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_naissance_fin">Date naissance (fin)</label>
                                    <input type="date" class="form-control" id="date_naissance_fin" name="date_naissance_fin">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_engagement_debut">Date engagement (début)</label>
                                    <input type="date" class="form-control" id="date_engagement_debut" name="date_engagement_debut">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_engagement_fin">Date engagement (fin)</label>
                                    <input type="date" class="form-control" id="date_engagement_fin" name="date_engagement_fin">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Autres critères -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-filter"></i> Autres critères
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="situation_familiale">Situation familiale</label>
                                    <select class="form-control" id="situation_familiale" name="situation_familiale">
                                        <option value="">Toutes</option>
                                        {% for situation in situations_familiales %}
                                        <option value="{{ situation.id_sitfam }}">{{ situation.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="groupe_sanguin">Groupe sanguin</label>
                                    <select class="form-control" id="groupe_sanguin" name="groupe_sanguin">
                                        <option value="">Tous</option>
                                        {% for groupe in groupes_sanguins %}
                                        <option value="{{ groupe.id_groupe }}">{{ groupe.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="lieu_naissance">Lieu de naissance</label>
                                    <input type="text" class="form-control" id="lieu_naissance" name="lieu_naissance" 
                                           placeholder="Ville de naissance">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="fonction">Fonction</label>
                                    <input type="text" class="form-control" id="fonction" name="fonction" 
                                           placeholder="Fonction actuelle">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Boutons d'action -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search"></i>
                                        Rechercher
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-lg ml-2" onclick="resetForm()">
                                        <i class="fas fa-eraser"></i>
                                        Effacer
                                    </button>
                                    <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-info btn-lg ml-2">
                                        <i class="fas fa-list"></i>
                                        Voir tout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Section des résultats (si recherche effectuée) -->
    {% if resultats_recherche %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-list-ul"></i>
                        Résultats de la recherche ({{ resultats_recherche|length }} trouvé(s))
                    </h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom et Prénom</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Fonction</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for personnel in resultats_recherche %}
                                <tr>
                                    <td><strong>{{ personnel.matricule }}</strong></td>
                                    <td>{{ personnel.nom }} {{ personnel.prenom }}</td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ personnel.grade_actuel.libelle if personnel.grade_actuel else 'N/A' }}
                                        </span>
                                    </td>
                                    <td>{{ personnel.unite.libelle if personnel.unite else 'N/A' }}</td>
                                    <td>{{ personnel.fonction or 'N/A' }}</td>
                                    <td>
                                        <a href="{{ url_for('rh.fiche_personnel', matricule=personnel.matricule) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function resetForm() {
    document.getElementById('formRechercheAvancee').reset();
}

$(document).ready(function() {
    // Validation des dates
    $('#date_naissance_debut, #date_engagement_debut').on('change', function() {
        var debut = $(this).val();
        var finId = $(this).attr('id').replace('_debut', '_fin');
        var fin = $('#' + finId).val();
        
        if (debut && fin && debut > fin) {
            alert('La date de début ne peut pas être postérieure à la date de fin');
            $(this).val('');
        }
    });
    
    $('#date_naissance_fin, #date_engagement_fin').on('change', function() {
        var fin = $(this).val();
        var debutId = $(this).attr('id').replace('_fin', '_debut');
        var debut = $('#' + debutId).val();
        
        if (debut && fin && debut > fin) {
            alert('La date de fin ne peut pas être antérieure à la date de début');
            $(this).val('');
        }
    });
});
</script>
{% endblock %}
