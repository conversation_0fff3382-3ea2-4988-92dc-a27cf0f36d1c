{% extends "RH/base_rh.html" %}

{% block title %}Recherche Personnel - Forces Armées Royales{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête de Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-search"></i>
                                Recherche Personnel Militaire
                            </h1>
                            <p class="mb-0 mt-2" style="color: var(--text-light);">
                                Rechercher et consulter les informations du personnel militaire
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                                <i class="fas fa-user-plus"></i> Nouveau Militaire
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Critères de Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form id="searchForm" method="POST">
                        <div class="row">
                            <!-- Recherche Simple -->
                            <div class="col-md-6 mb-3">
                                <label for="search_simple" class="form-label">
                                    <i class="fas fa-search me-1"></i>Recherche Rapide
                                </label>
                                <input type="text" class="form-control" id="search_simple" name="search_simple"
                                       placeholder="Nom, Prénom ou Matricule..." value="{{ request.form.search_simple or '' }}">
                                <small class="form-text text-muted">Recherche par nom, prénom ou matricule</small>
                            </div>

                            <!-- Matricule -->
                            <div class="col-md-6 mb-3">
                                <label for="matricule" class="form-label">
                                    <i class="fas fa-id-badge me-1"></i>Matricule
                                </label>
                                <input type="text" class="form-control" id="matricule" name="matricule"
                                       placeholder="Ex: 1234567" value="{{ request.form.matricule or '' }}">
                            </div>
                        </div>

                        <div class="row">
                            <!-- Catégorie -->
                            <div class="col-md-4 mb-3">
                                <label for="categorie_id" class="form-label">
                                    <i class="fas fa-layer-group me-1"></i>Catégorie
                                </label>
                                <select class="form-select" id="categorie_id" name="categorie_id">
                                    <option value="">Toutes les catégories</option>
                                    {% for categorie in categories %}
                                    <option value="{{ categorie.id_categorie }}"
                                            {{ 'selected' if request.form.categorie_id == categorie.id_categorie|string }}>
                                        {{ categorie.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Grade -->
                            <div class="col-md-4 mb-3">
                                <label for="grade_id" class="form-label">
                                    <i class="fas fa-star me-1"></i>Grade
                                </label>
                                <select class="form-select" id="grade_id" name="grade_id">
                                    <option value="">Tous les grades</option>
                                    {% for grade in grades %}
                                    <option value="{{ grade.id_grade }}"
                                            {{ 'selected' if request.form.grade_id == grade.id_grade|string }}>
                                        {{ grade.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Unité -->
                            <div class="col-md-4 mb-3">
                                <label for="unite_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>Unité
                                </label>
                                <select class="form-select" id="unite_id" name="unite_id">
                                    <option value="">Toutes les unités</option>
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}"
                                            {{ 'selected' if request.form.unite_id == unite.id_unite|string }}>
                                        {{ unite.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Arme -->
                            <div class="col-md-4 mb-3">
                                <label for="arme_id" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Arme/Service
                                </label>
                                <select class="form-select" id="arme_id" name="arme_id">
                                    <option value="">Toutes les armes</option>
                                    {% for arme in armes %}
                                    <option value="{{ arme.id_arme }}"
                                            {{ 'selected' if request.form.arme_id == arme.id_arme|string }}>
                                        {{ arme.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Situation Familiale -->
                            <div class="col-md-4 mb-3">
                                <label for="situation_fam_id" class="form-label">
                                    <i class="fas fa-heart me-1"></i>Situation Familiale
                                </label>
                                <select class="form-select" id="situation_fam_id" name="situation_fam_id">
                                    <option value="">Toutes les situations</option>
                                    {% for situation in situations_familiales %}
                                    <option value="{{ situation.id_sitfam }}"
                                            {{ 'selected' if request.form.situation_fam_id == situation.id_sitfam|string }}>
                                        {{ situation.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Aptitude Médicale -->
                            <div class="col-md-4 mb-3">
                                <label for="aptitude" class="form-label">
                                    <i class="fas fa-heartbeat me-1"></i>Aptitude Médicale
                                </label>
                                <select class="form-select" id="aptitude" name="aptitude">
                                    <option value="">Toutes les aptitudes</option>
                                    <option value="apte" {{ 'selected' if request.form.aptitude == 'apte' }}>Apte</option>
                                    <option value="inapte" {{ 'selected' if request.form.aptitude == 'inapte' }}>Inapte</option>
                                </select>
                            </div>
                        </div>

                        <!-- Recherche Avancée (Collapsible) -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-outline-secondary mb-3" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#advancedSearch" aria-expanded="false">
                                    <i class="fas fa-cog me-1"></i>Recherche Avancée
                                </button>

                                <div class="collapse" id="advancedSearch">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <!-- Âge -->
                                                <div class="col-md-3 mb-3">
                                                    <label for="age_min" class="form-label">Âge Min</label>
                                                    <input type="number" class="form-control" id="age_min" name="age_min"
                                                           min="18" max="65" value="{{ request.form.age_min or '' }}">
                                                </div>
                                                <div class="col-md-3 mb-3">
                                                    <label for="age_max" class="form-label">Âge Max</label>
                                                    <input type="number" class="form-control" id="age_max" name="age_max"
                                                           min="18" max="65" value="{{ request.form.age_max or '' }}">
                                                </div>

                                                <!-- Années de service -->
                                                <div class="col-md-3 mb-3">
                                                    <label for="service_min" class="form-label">Service Min (années)</label>
                                                    <input type="number" class="form-control" id="service_min" name="service_min"
                                                           min="0" max="40" value="{{ request.form.service_min or '' }}">
                                                </div>
                                                <div class="col-md-3 mb-3">
                                                    <label for="service_max" class="form-label">Service Max (années)</label>
                                                    <input type="number" class="form-control" id="service_max" name="service_max"
                                                           min="0" max="40" value="{{ request.form.service_max or '' }}">
                                                </div>
                                            </div>

                                            <div class="row">
                                                <!-- Zone géographique -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="zone" class="form-label">Zone</label>
                                                    <select class="form-select" id="zone" name="zone">
                                                        <option value="">Toutes les zones</option>
                                                        <option value="zone_est" {{ 'selected' if request.form.zone == 'zone_est' }}>Zone EST</option>
                                                        <option value="zone_sud" {{ 'selected' if request.form.zone == 'zone_sud' }}>Zone SUD</option>
                                                        <option value="zone_centre" {{ 'selected' if request.form.zone == 'zone_centre' }}>Zone CENTRE</option>
                                                    </select>
                                                </div>

                                                <!-- Avec enfants -->
                                                <div class="col-md-6 mb-3">
                                                    <label for="avec_enfants" class="form-label">Situation familiale</label>
                                                    <select class="form-select" id="avec_enfants" name="avec_enfants">
                                                        <option value="">Tous</option>
                                                        <option value="avec_enfants" {{ 'selected' if request.form.avec_enfants == 'avec_enfants' }}>Avec enfants</option>
                                                        <option value="sans_enfants" {{ 'selected' if request.form.avec_enfants == 'sans_enfants' }}>Sans enfants</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-military">
                                        <i class="fas fa-search me-1"></i>Rechercher
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo me-1"></i>Réinitialiser
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="exportResults()">
                                        <i class="fas fa-download me-1"></i>Exporter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats de Recherche -->
    {% if personnel_list %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Résultats de Recherche
                            </h5>
                            <small style="color: var(--text-light);">{{ personnel_list|length }} militaire(s) trouvé(s)</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <button class="btn btn-outline-light btn-sm" onclick="toggleView('table')">
                                    <i class="fas fa-table"></i> Tableau
                                </button>
                                <button class="btn btn-outline-light btn-sm" onclick="toggleView('cards')">
                                    <i class="fas fa-th-large"></i> Cartes
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Vue Tableau -->
                    <div id="tableView" class="table-responsive">
                        <table class="table table-hover" id="personnelTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom Complet</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Catégorie</th>
                                    <th>Situation</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for militaire in personnel_list %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ militaire.matricule }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ militaire.nom_complet }}</strong><br>
                                            <small class="text-muted">{{ militaire.nom_complet_arabe }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">{{ militaire.grade_actuel.libelle }}</span>
                                    </td>
                                    <td>{{ militaire.unite.libelle }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ militaire.categorie.libelle }}</span>
                                    </td>
                                    <td>
                                        {% if militaire.situation_medicale %}
                                            <span class="badge bg-{{ 'success' if militaire.situation_medicale.aptitude == 'apte' else 'danger' }}">
                                                {{ militaire.situation_medicale.aptitude|title }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="viewPersonnel('{{ militaire.matricule }}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editPersonnel('{{ militaire.matricule }}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Vue Cartes -->
                    <div id="cardsView" class="row" style="display: none;">
                        {% for militaire in personnel_list %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-header bg-light">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="badge bg-primary">{{ militaire.matricule }}</span>
                                        <span class="badge bg-warning text-dark">{{ militaire.grade_actuel.libelle }}</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title">{{ militaire.nom_complet }}</h6>
                                    <p class="card-text text-muted small">{{ militaire.nom_complet_arabe }}</p>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-building me-1"></i>{{ militaire.unite.libelle }}
                                        </small>
                                    </div>
                                    <div class="mb-2">
                                        <span class="badge bg-info">{{ militaire.categorie.libelle }}</span>
                                        {% if militaire.situation_medicale %}
                                            <span class="badge bg-{{ 'success' if militaire.situation_medicale.aptitude == 'apte' else 'danger' }}">
                                                {{ militaire.situation_medicale.aptitude|title }}
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100">
                                        <button class="btn btn-outline-primary btn-sm" onclick="viewPersonnel('{{ militaire.matricule }}')">
                                            <i class="fas fa-eye"></i> Voir
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm" onclick="editPersonnel('{{ militaire.matricule }}')">
                                            <i class="fas fa-edit"></i> Modifier
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Modal de Visualisation Détaillée du Personnel -->
    <div class="modal fade" id="personnelModal" tabindex="-1" aria-labelledby="personnelModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="personnelModalLabel">
                        <i class="fas fa-user-circle me-2"></i>Fiche Personnel Militaire
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="personnelModalBody">
                    <!-- Le contenu sera chargé dynamiquement -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Fermer
                    </button>
                    <button type="button" class="btn btn-warning" onclick="editCurrentPersonnel()">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </button>
                    <button type="button" class="btn btn-info" onclick="printPersonnel()">
                        <i class="fas fa-print me-1"></i>Imprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Modification -->
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editModalLabel">
                        <i class="fas fa-edit me-2"></i>Modifier Personnel
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="editModalBody">
                    <!-- Le contenu sera chargé dynamiquement -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-success" onclick="savePersonnel()">
                        <i class="fas fa-save me-1"></i>Sauvegarder
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
let currentMatricule = null;

// Fonction pour basculer entre les vues
function toggleView(viewType) {
    const tableView = document.getElementById('tableView');
    const cardsView = document.getElementById('cardsView');

    if (viewType === 'table') {
        tableView.style.display = 'block';
        cardsView.style.display = 'none';
    } else {
        tableView.style.display = 'none';
        cardsView.style.display = 'block';
    }
}

// Fonction pour réinitialiser le formulaire
function resetForm() {
    document.getElementById('searchForm').reset();
    window.location.href = "{{ url_for('rh.recherche_personnel') }}";
}

// Fonction pour exporter les résultats
function exportResults() {
    const formData = new FormData(document.getElementById('searchForm'));
    const params = new URLSearchParams(formData);
    window.open("{{ url_for('rh.export_personnel') }}?" + params.toString(), '_blank');
}

// Fonction pour visualiser un personnel
function viewPersonnel(matricule) {
    currentMatricule = matricule;
    const modal = new bootstrap.Modal(document.getElementById('personnelModal'));

    // Charger les données du personnel
    fetch(`/rh/personnel/${matricule}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('personnelModalBody').innerHTML = generatePersonnelHTML(data.personnel);
                modal.show();
            } else {
                alert('Erreur lors du chargement des données: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des données');
        });
}

// Fonction pour modifier un personnel
function editPersonnel(matricule) {
    currentMatricule = matricule;
    window.location.href = `/rh/personnel/${matricule}/modifier`;
}

// Fonction pour modifier le personnel actuel
function editCurrentPersonnel() {
    if (currentMatricule) {
        editPersonnel(currentMatricule);
    }
}

// Fonction pour imprimer la fiche personnel
function printPersonnel() {
    if (currentMatricule) {
        window.open(`/rh/personnel/${currentMatricule}/print`, '_blank');
    }
}

// Fonction pour générer le HTML de la fiche personnel
function generatePersonnelHTML(personnel) {
    return `
        <div class="container-fluid">
            <!-- Navigation par onglets -->
            <ul class="nav nav-tabs mb-4" id="personnelTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="principal-tab" data-bs-toggle="tab" data-bs-target="#principal" type="button" role="tab">
                        <i class="fas fa-user me-1"></i>Fiche Principale
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="medical-tab" data-bs-toggle="tab" data-bs-target="#medical" type="button" role="tab">
                        <i class="fas fa-heartbeat me-1"></i>Situation Médicale
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="absences-tab" data-bs-toggle="tab" data-bs-target="#absences" type="button" role="tab">
                        <i class="fas fa-calendar-times me-1"></i>Absences
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="mouvements-tab" data-bs-toggle="tab" data-bs-target="#mouvements" type="button" role="tab">
                        <i class="fas fa-exchange-alt me-1"></i>Mouvements
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sanctions-tab" data-bs-toggle="tab" data-bs-target="#sanctions" type="button" role="tab">
                        <i class="fas fa-gavel me-1"></i>Sanctions
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="avancements-tab" data-bs-toggle="tab" data-bs-target="#avancements" type="button" role="tab">
                        <i class="fas fa-star me-1"></i>Avancements
                    </button>
                </li>
            </ul>

            <!-- Contenu des onglets -->
            <div class="tab-content" id="personnelTabsContent">
                <!-- Onglet Fiche Principale -->
                <div class="tab-pane fade show active" id="principal" role="tabpanel">
                    ${generateFichePrincipale(personnel)}
                </div>

                <!-- Onglet Situation Médicale -->
                <div class="tab-pane fade" id="medical" role="tabpanel">
                    ${generateSituationMedicale(personnel)}
                </div>

                <!-- Onglet Absences -->
                <div class="tab-pane fade" id="absences" role="tabpanel">
                    ${generateAbsences(personnel)}
                </div>

                <!-- Onglet Mouvements -->
                <div class="tab-pane fade" id="mouvements" role="tabpanel">
                    ${generateMouvements(personnel)}
                </div>

                <!-- Onglet Sanctions -->
                <div class="tab-pane fade" id="sanctions" role="tabpanel">
                    ${generateSanctions(personnel)}
                </div>

                <!-- Onglet Avancements -->
                <div class="tab-pane fade" id="avancements" role="tabpanel">
                    ${generateAvancements(personnel)}
                </div>
            </div>
        </div>
    `;
}

// Fonctions pour générer les sections spécialisées
function generateFichePrincipale(personnel) {
    return `
        <div class="row">
            <!-- Informations Civiles -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-user me-1"></i>Informations Civiles</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Matricule:</strong></td><td>${personnel.matricule}</td></tr>
                            <tr><td><strong>Nom:</strong></td><td>${personnel.nom}</td></tr>
                            <tr><td><strong>Prénom:</strong></td><td>${personnel.prenom}</td></tr>
                            <tr><td><strong>Nom Arabe:</strong></td><td>${personnel.nom_arabe}</td></tr>
                            <tr><td><strong>Prénom Arabe:</strong></td><td>${personnel.prenom_arabe}</td></tr>
                            <tr><td><strong>Date Naissance:</strong></td><td>${personnel.date_naissance}</td></tr>
                            <tr><td><strong>Lieu Naissance:</strong></td><td>${personnel.lieu_naissance}</td></tr>
                            <tr><td><strong>Sexe:</strong></td><td>${personnel.sexe}</td></tr>
                            <tr><td><strong>CIN:</strong></td><td>${personnel.numero_cin}</td></tr>
                            <tr><td><strong>GSM:</strong></td><td>${personnel.gsm}</td></tr>
                            <tr><td><strong>Résidence:</strong></td><td>${personnel.lieu_residence}</td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Informations Militaires -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-shield-alt me-1"></i>Informations Militaires</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Catégorie:</strong></td><td>${personnel.categorie}</td></tr>
                            <tr><td><strong>Grade:</strong></td><td>${personnel.grade_actuel}</td></tr>
                            <tr><td><strong>Arme:</strong></td><td>${personnel.arme}</td></tr>
                            <tr><td><strong>Spécialité:</strong></td><td>${personnel.specialite || 'N/A'}</td></tr>
                            <tr><td><strong>Unité:</strong></td><td>${personnel.unite}</td></tr>
                            <tr><td><strong>Fonction:</strong></td><td>${personnel.fonction}</td></tr>
                            <tr><td><strong>Date Engagement:</strong></td><td>${personnel.date_engagement}</td></tr>
                            <tr><td><strong>Date Prise Fonction:</strong></td><td>${personnel.date_prise_fonction}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Situation Familiale -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-heart me-1"></i>Situation Familiale</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Situation:</strong> ${personnel.situation_familiale}<br>
                                <strong>Nombre d'enfants:</strong> ${personnel.nombre_enfants || 0}
                            </div>
                            <div class="col-md-4">
                                <strong>Père:</strong> ${personnel.nom_pere} ${personnel.prenom_pere}<br>
                                <strong>Mère:</strong> ${personnel.nom_mere} ${personnel.prenom_mere}
                            </div>
                            <div class="col-md-4">
                                <strong>Contact Urgence:</strong> ${personnel.gsm_urgence}<br>
                                <strong>Degré Parenté:</strong> ${personnel.degre_parente}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateSituationMedicale(personnel) {
    return `
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-heartbeat me-1"></i>Situation Médicale</h6>
                    </div>
                    <div class="card-body">
                        ${personnel.situation_medicale ? `
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Aptitude:</strong>
                                    <span class="badge bg-${personnel.situation_medicale.aptitude === 'apte' ? 'success' : 'danger'}">
                                        ${personnel.situation_medicale.aptitude}
                                    </span><br>
                                    <strong>Maladies:</strong> ${personnel.situation_medicale.maladies}<br>
                                    <strong>Date Hospitalisation:</strong> ${personnel.situation_medicale.date_hospitalisation}
                                </div>
                                <div class="col-md-6">
                                    <strong>Lieu Hospitalisation:</strong> ${personnel.situation_medicale.lieu_hospitalisation}<br>
                                    <strong>Observations:</strong> ${personnel.situation_medicale.observations || 'Aucune'}
                                </div>
                            </div>
                        ` : '<p class="text-muted">Aucune information médicale disponible</p>'}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateAbsences(personnel) {
    return `
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills mb-3" id="absencesTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#permissions" type="button">
                            <i class="fas fa-calendar-check me-1"></i>Permissions
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#detachements" type="button">
                            <i class="fas fa-plane me-1"></i>Détachements
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#desertions" type="button">
                            <i class="fas fa-exclamation-triangle me-1"></i>Désertions
                        </button>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane fade show active" id="permissions">
                        ${generatePermissions(personnel.permissions || [])}
                    </div>
                    <div class="tab-pane fade" id="detachements">
                        ${generateDetachements(personnel.detachements || [])}
                    </div>
                    <div class="tab-pane fade" id="desertions">
                        ${generateDesertions(personnel.desertions || [])}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateMouvements(personnel) {
    return `
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills mb-3" id="mouvementsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#mutations" type="button">
                            <i class="fas fa-exchange-alt me-1"></i>Mutations
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#sejours" type="button">
                            <i class="fas fa-map-marker-alt me-1"></i>Séjours OPS
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#liberations" type="button">
                            <i class="fas fa-door-open me-1"></i>Libérations
                        </button>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane fade show active" id="mutations">
                        ${generateMutations(personnel.mutations || [])}
                    </div>
                    <div class="tab-pane fade" id="sejours">
                        ${generateSejoursOps(personnel.sejours_ops || [])}
                    </div>
                    <div class="tab-pane fade" id="liberations">
                        ${generateLiberations(personnel.liberations || [])}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function generateSanctions(personnel) {
    return `
        <div class="row">
            <div class="col-12">
                ${personnel.sanctions && personnel.sanctions.length > 0 ? `
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Durée</th>
                                    <th>Motif</th>
                                    <th>Observation</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${personnel.sanctions.map(sanction => `
                                    <tr>
                                        <td>${sanction.date_sanction}</td>
                                        <td><span class="badge bg-warning">${sanction.type_sanction}</span></td>
                                        <td>${sanction.duree || 'N/A'} jours</td>
                                        <td>${sanction.motif}</td>
                                        <td>${sanction.observation || 'Aucune'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : '<p class="text-muted">Aucune sanction enregistrée</p>'}
            </div>
        </div>
    `;
}

function generateAvancements(personnel) {
    return `
        <div class="row">
            <div class="col-12">
                ${personnel.avancements && personnel.avancements.length > 0 ? `
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Grade Précédent</th>
                                    <th>Grade Suivant</th>
                                    <th>Conditions</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${personnel.avancements.map(avancement => `
                                    <tr>
                                        <td>${avancement.date_avancement}</td>
                                        <td><span class="badge bg-secondary">${avancement.grade_precedent}</span></td>
                                        <td><span class="badge bg-success">${avancement.grade_suivant}</span></td>
                                        <td>${avancement.conditions || 'Standard'}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                ` : '<p class="text-muted">Aucun avancement enregistré</p>'}
            </div>
        </div>
    `;
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Animation des cartes
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
{% endblock %}
