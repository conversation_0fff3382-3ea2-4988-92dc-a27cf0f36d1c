{% extends "base.html" %}

{% block title %}Liste du Personnel - RH{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="card-title">
                            <i class="fas fa-users"></i>
                            Liste du Personnel
                        </h3>
                        <div>
                            <a href="{{ url_for('rh.ajouter_personnel') }}" class="btn btn-primary">
                                <i class="fas fa-user-plus"></i>
                                Ajouter Personnel
                            </a>
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-info">
                                <i class="fas fa-search"></i>
                                Recherche avancée
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Filtres rapides -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-control" id="filtre_categorie">
                                <option value="">Toutes les catégories</option>
                                <option value="Officier">Officiers</option>
                                <option value="ODR">Officiers du Rang</option>
                                <option value="MDR">Militaires du Rang</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="filtre_unite">
                                <option value="">Toutes les unités</option>
                                {% for unite in unites %}
                                <option value="{{ unite.libelle }}">{{ unite.libelle }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="filtre_arme">
                                <option value="">Toutes les armes</option>
                                {% for arme in armes %}
                                <option value="{{ arme.libelle }}">{{ arme.libelle }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="recherche_rapide" 
                                   placeholder="Recherche rapide (nom, prénom, matricule)">
                        </div>
                    </div>
                    
                    <!-- Tableau du personnel -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="table_personnel">
                            <thead class="thead-dark">
                                <tr>
                                    <th>Matricule</th>
                                    <th>Nom et Prénom</th>
                                    <th>Grade</th>
                                    <th>Catégorie</th>
                                    <th>Unité</th>
                                    <th>Arme</th>
                                    <th>Fonction</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for personnel in liste_personnel %}
                                <tr>
                                    <td>
                                        <strong>{{ personnel.matricule }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ personnel.nom }} {{ personnel.prenom }}</strong>
                                        </div>
                                        <small class="text-muted">{{ personnel.nom_arabe }} {{ personnel.prenom_arabe }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">
                                            {{ personnel.grade_actuel.libelle if personnel.grade_actuel else 'N/A' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if personnel.categorie %}
                                            {% if personnel.categorie.libelle == 'Officier' %}
                                                <span class="badge badge-success">{{ personnel.categorie.libelle }}</span>
                                            {% elif personnel.categorie.libelle == 'ODR' %}
                                                <span class="badge badge-warning">{{ personnel.categorie.libelle }}</span>
                                            {% else %}
                                                <span class="badge badge-secondary">{{ personnel.categorie.libelle }}</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge badge-light">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ personnel.unite.libelle if personnel.unite else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ personnel.arme.libelle if personnel.arme else 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <small>{{ personnel.fonction or 'N/A' }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('rh.fiche_personnel', matricule=personnel.matricule) }}" 
                                               class="btn btn-outline-primary" title="Voir fiche">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('rh.modifier_personnel', matricule=personnel.matricule) }}" 
                                               class="btn btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmerSuppression('{{ personnel.matricule }}', '{{ personnel.nom }} {{ personnel.prenom }}')"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if pagination %}
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_personnel', page=pagination.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_personnel', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_personnel', page=pagination.next_num) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    
                    <!-- Statistiques -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Total :</strong> {{ total_personnel or 0 }} militaires
                                {% if pagination %}
                                    | Page {{ pagination.page }} sur {{ pagination.pages }}
                                    | Affichage de {{ pagination.per_page }} éléments par page
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="modalSuppression" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    Confirmer la suppression
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le personnel <strong id="nomPersonnel"></strong> ?</p>
                <p class="text-danger">
                    <i class="fas fa-warning"></i>
                    Cette action est irréversible et supprimera toutes les données associées.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" id="btnConfirmerSuppression">
                    <i class="fas fa-trash"></i>
                    Supprimer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialisation de DataTables pour la recherche et le tri
    $('#table_personnel').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": 7 }
        ]
    });
    
    // Filtres personnalisés
    $('#filtre_categorie, #filtre_unite, #filtre_arme').on('change', function() {
        // Implémentation des filtres personnalisés
        // Cette partie nécessiterait une implémentation côté serveur
        console.log('Filtre appliqué:', this.id, this.value);
    });
});

function confirmerSuppression(matricule, nom) {
    $('#nomPersonnel').text(nom);
    $('#btnConfirmerSuppression').data('matricule', matricule);
    $('#modalSuppression').modal('show');
}

$('#btnConfirmerSuppression').on('click', function() {
    var matricule = $(this).data('matricule');
    // Redirection vers la route de suppression
    window.location.href = "{{ url_for('rh.supprimer_personnel', matricule='MATRICULE') }}".replace('MATRICULE', matricule);
});
</script>
{% endblock %}
