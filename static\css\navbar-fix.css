/* Navbar Fix CSS - Correction du bouton hamburger */

/* <PERSON><PERSON>gles générales pour le bouton hamburger */
.navbar-toggler {
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.25rem 0.5rem;
    background-color: transparent;
    color: white;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    outline: none;
}

.navbar-toggler:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Icône hamburger personnalisée */
.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    width: 1.5em;
    height: 1.5em;
    display: inline-block;
}

/* Afficher le bouton hamburger sur mobile */
@media (max-width: 991.98px) {
    .navbar-toggler {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        z-index: 1000;
    }
    
    .navbar-toggler-icon {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* S'assurer que le menu se déploie correctement */
    .navbar-collapse {
        background: rgba(75, 83, 32, 0.95);
        border-radius: 8px;
        margin-top: 0.5rem;
        padding: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .nav-item {
        margin: 0.5rem 0;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 5px;
        margin: 0.25rem 0;
        transition: all 0.3s ease;
    }
    
    .nav-link:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: translateX(5px);
    }
}

/* Masquer le bouton hamburger sur desktop */
@media (min-width: 992px) {
    .navbar-toggler {
        display: none !important;
    }
    
    .navbar-collapse {
        display: flex !important;
    }
}

/* Règles spécifiques pour très petits écrans */
@media (max-width: 576px) {
    .navbar-toggler {
        padding: 0.2rem 0.4rem;
    }
    
    .navbar-toggler-icon {
        width: 1.2em;
        height: 1.2em;
    }
    
    .navbar-collapse {
        margin-top: 0.25rem;
        padding: 0.75rem;
    }
    
    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* Animation pour le menu déroulant */
.navbar-collapse.collapsing {
    transition: height 0.35s ease;
}

.navbar-collapse.show {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Améliorations pour l'accessibilité */
.navbar-toggler[aria-expanded="true"] {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e");
}

/* Règles pour éviter les conflits avec d'autres styles */
.navbar .navbar-toggler {
    order: 3;
    margin-left: auto;
}

.navbar .navbar-brand {
    order: 1;
}

.navbar .digital-clock {
    order: 2;
}

.navbar .navbar-collapse {
    order: 4;
    width: 100%;
}

/* Améliorations pour le conteneur de navigation */
.navbar .container-fluid {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

/* Règles pour les icônes dans la navigation */
.navbar .nav-link i {
    font-size: 1.1rem;
    margin-right: 0.5rem;
}

@media (max-width: 767.98px) {
    .navbar .nav-link i {
        font-size: 1.2rem;
        margin-right: 0.75rem;
    }
    
    .navbar .nav-link span {
        font-size: 0.9rem;
    }
}

/* Améliorations pour l'état actif des liens */
.navbar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    font-weight: 600;
}

/* Améliorations pour les liens de déconnexion */
.navbar .nav-link.text-danger {
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 5px;
    transition: all 0.3s ease;
}

.navbar .nav-link.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.6);
} 