{% extends "base.html" %}

{% block title %}Ajouter Personnel - RH{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus"></i>
                        Ajouter un nouveau personnel
                    </h3>
                </div>
                
                <form method="POST" action="{{ url_for('rh.ajouter_personnel') }}" class="needs-validation" novalidate>
                    <div class="card-body">
                        
                        <!-- Section Informations personnelles -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> Informations personnelles
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="matricule" class="required">Matricule</label>
                                    <input type="text" class="form-control" id="matricule" name="matricule" 
                                           pattern="[0-9]{7}" maxlength="7" required>
                                    <div class="invalid-feedback">Le matricule doit contenir exactement 7 chiffres</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom" class="required">Nom</label>
                                    <input type="text" class="form-control" id="nom" name="nom" required>
                                    <div class="invalid-feedback">Le nom est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom" class="required">Prénom</label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" required>
                                    <div class="invalid-feedback">Le prénom est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sexe_id" class="required">Sexe</label>
                                    <select class="form-control" id="sexe_id" name="sexe_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for genre in genres %}
                                        <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Le sexe est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_arabe" class="required">الاسم العائلي</label>
                                    <input type="text" class="form-control" id="nom_arabe" name="nom_arabe" required>
                                    <div class="invalid-feedback">الاسم العائلي مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_arabe" class="required">الاسم الشخصي</label>
                                    <input type="text" class="form-control" id="prenom_arabe" name="prenom_arabe" required>
                                    <div class="invalid-feedback">الاسم الشخصي مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_naissance" class="required">Date de naissance</label>
                                    <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                                    <div class="invalid-feedback">La date de naissance est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="lieu_naissance" class="required">Lieu de naissance</label>
                                    <input type="text" class="form-control" id="lieu_naissance" name="lieu_naissance" required>
                                    <div class="invalid-feedback">Le lieu de naissance est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Documents -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-id-card"></i> Documents d'identité
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="numero_cin" class="required">Numéro CIN</label>
                                    <input type="text" class="form-control" id="numero_cin" name="numero_cin" 
                                           pattern="[A-Z][0-9]{6}" maxlength="7" required>
                                    <div class="invalid-feedback">Format: 1 lettre + 6 chiffres (ex: A123456)</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_delivrance_cin" class="required">Date délivrance CIN</label>
                                    <input type="date" class="form-control" id="date_delivrance_cin" name="date_delivrance_cin" required>
                                    <div class="invalid-feedback">La date de délivrance est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_expiration_cin" class="required">Date expiration CIN</label>
                                    <input type="date" class="form-control" id="date_expiration_cin" name="date_expiration_cin" required>
                                    <div class="invalid-feedback">La date d'expiration est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="groupe_sanguin_id" class="required">Groupe sanguin</label>
                                    <select class="form-control" id="groupe_sanguin_id" name="groupe_sanguin_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for groupe in groupes_sanguins %}
                                        <option value="{{ groupe.id_groupe }}">{{ groupe.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Le groupe sanguin est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Contact -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-phone"></i> Contact et adresse
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="gsm" class="required">GSM</label>
                                    <input type="tel" class="form-control" id="gsm" name="gsm" 
                                           pattern="0[67][0-9]{8}" required>
                                    <div class="invalid-feedback">Format: 06XXXXXXXX ou 07XXXXXXXX</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="telephone_domicile">Téléphone domicile</label>
                                    <input type="tel" class="form-control" id="telephone_domicile" name="telephone_domicile">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="taille" class="required">Taille (cm)</label>
                                    <input type="number" class="form-control" id="taille" name="taille" 
                                           min="150" max="220" step="0.01" required>
                                    <div class="invalid-feedback">La taille est obligatoire (150-220 cm)</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="gsm_urgence" class="required">GSM urgence</label>
                                    <input type="tel" class="form-control" id="gsm_urgence" name="gsm_urgence" 
                                           pattern="0[67][0-9]{8}" required>
                                    <div class="invalid-feedback">Le GSM d'urgence est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lieu_residence" class="required">Lieu de résidence</label>
                                    <textarea class="form-control" id="lieu_residence" name="lieu_residence" rows="2" required></textarea>
                                    <div class="invalid-feedback">Le lieu de résidence est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="degre_parente_id" class="required">Degré de parenté (contact urgence)</label>
                                    <select class="form-control" id="degre_parente_id" name="degre_parente_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for degre in degres_parente %}
                                        <option value="{{ degre.id_degre }}">{{ degre.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Le degré de parenté est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Militaire -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-medal"></i> Informations militaires
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="categorie_id" class="required">Catégorie</label>
                                    <select class="form-control" id="categorie_id" name="categorie_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for categorie in categories %}
                                        <option value="{{ categorie.id_categorie }}">{{ categorie.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">La catégorie est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="arme_id" class="required">Arme</label>
                                    <select class="form-control" id="arme_id" name="arme_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for arme in armes %}
                                        <option value="{{ arme.id_arme }}">{{ arme.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">L'arme est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="specialite_id">Spécialité</label>
                                    <select class="form-control" id="specialite_id" name="specialite_id">
                                        <option value="">Sélectionner...</option>
                                        {% for specialite in specialites %}
                                        <option value="{{ specialite.id_specialite }}">{{ specialite.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="unite_id" class="required">Unité</label>
                                    <select class="form-control" id="unite_id" name="unite_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for unite in unites %}
                                        <option value="{{ unite.id_unite }}">{{ unite.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">L'unité est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="grade_actuel_id" class="required">Grade actuel</label>
                                    <select class="form-control" id="grade_actuel_id" name="grade_actuel_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for grade in grades %}
                                        <option value="{{ grade.id_grade }}">{{ grade.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Le grade est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="fonction" class="required">Fonction</label>
                                    <input type="text" class="form-control" id="fonction" name="fonction" required>
                                    <div class="invalid-feedback">La fonction est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_prise_fonction" class="required">Date prise fonction</label>
                                    <input type="date" class="form-control" id="date_prise_fonction" name="date_prise_fonction" required>
                                    <div class="invalid-feedback">La date de prise de fonction est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_engagement" class="required">Date d'engagement</label>
                                    <input type="date" class="form-control" id="date_engagement" name="date_engagement" required>
                                    <div class="invalid-feedback">La date d'engagement est obligatoire</div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Financière -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-money-bill"></i> Informations financières
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ccp" class="required">CCP</label>
                                    <input type="text" class="form-control" id="ccp" name="ccp" required>
                                    <div class="invalid-feedback">Le CCP est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="compte_bancaire">Compte bancaire</label>
                                    <input type="text" class="form-control" id="compte_bancaire" name="compte_bancaire">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="numero_somme" class="required">Numéro SOMME</label>
                                    <input type="text" class="form-control" id="numero_somme" name="numero_somme" required>
                                    <div class="invalid-feedback">Le numéro SOMME est obligatoire</div>
                                </div>
                            </div>
                        </div>

                        <!-- Section Famille -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-users"></i> Informations familiales
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_pere" class="required">Nom du père</label>
                                    <input type="text" class="form-control" id="nom_pere" name="nom_pere" required>
                                    <div class="invalid-feedback">Le nom du père est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_pere" class="required">Prénom du père</label>
                                    <input type="text" class="form-control" id="prenom_pere" name="prenom_pere" required>
                                    <div class="invalid-feedback">Le prénom du père est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_mere" class="required">Nom de la mère</label>
                                    <input type="text" class="form-control" id="nom_mere" name="nom_mere" required>
                                    <div class="invalid-feedback">Le nom de la mère est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_mere" class="required">Prénom de la mère</label>
                                    <input type="text" class="form-control" id="prenom_mere" name="prenom_mere" required>
                                    <div class="invalid-feedback">Le prénom de la mère est obligatoire</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adresse_parents" class="required">Adresse des parents</label>
                                    <textarea class="form-control" id="adresse_parents" name="adresse_parents" rows="2" required></textarea>
                                    <div class="invalid-feedback">L'adresse des parents est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="situation_fam_id" class="required">Situation familiale</label>
                                    <select class="form-control" id="situation_fam_id" name="situation_fam_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for situation in situations_familiales %}
                                        <option value="{{ situation.id_sitfam }}">{{ situation.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">La situation familiale est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nombre_enfants">Nombre d'enfants</label>
                                    <input type="number" class="form-control" id="nombre_enfants" name="nombre_enfants" min="0" max="20">
                                </div>
                            </div>
                        </div>

                        <!-- Section Passeport (optionnel) -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-secondary mb-3">
                                    <i class="fas fa-passport"></i> Passeport (optionnel)
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="numero_passport">Numéro passeport</label>
                                    <input type="text" class="form-control" id="numero_passport" name="numero_passport">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="date_delivrance_passport">Date délivrance passeport</label>
                                    <input type="date" class="form-control" id="date_delivrance_passport" name="date_delivrance_passport">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="date_expiration_passport">Date expiration passeport</label>
                                    <input type="date" class="form-control" id="date_expiration_passport" name="date_expiration_passport">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.required::after {
    content: " *";
    color: red;
}
</style>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
