/* Styles pour l'horloge numérique moderne */
.digital-clock {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    margin-right: auto;
    padding: 6px 12px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2),
                inset 0 0 5px rgba(255, 255, 255, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-family: 'Roboto Mono', monospace;
    z-index: 100;
    min-width: 120px;
}

.digital-clock::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
}

.digital-clock::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 255, 157, 0.7), transparent);
}

.digital-clock:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15),
                inset 0 0 8px rgba(255, 255, 255, 0.3);
}

.digital-clock-time {
    font-size: 1.1rem;
    font-weight: 600;
    color: #00ff9d;
    text-shadow: 0 0 8px rgba(0, 255, 157, 0.6);
    letter-spacing: 1px;
}

.digital-clock-seconds {
    font-size: 0.8rem;
    margin-left: 2px;
    color: rgba(0, 255, 157, 0.8);
    position: relative;
    top: -2px;
    text-shadow: 0 0 5px rgba(0, 255, 157, 0.4);
}

.digital-clock-ampm {
    font-size: 0.7rem;
    margin-left: 3px;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    font-weight: 700;
    position: relative;
    top: -2px;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.5);
}

.digital-clock-date {
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    letter-spacing: 0.5px;
    opacity: 0;
    transform: translateY(0);
    transition: all 0.3s ease;
    text-shadow: 0 0 3px rgba(0, 255, 157, 0.5);
}

.digital-clock:hover .digital-clock-date {
    opacity: 1;
    transform: translateY(-15px);
}

/* Animation de pulsation pour les deux-points */
@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.3;
    }
}

.digital-clock-colon {
    animation: blink 1s infinite;
    color: #00ff9d;
}

/* Animation de brillance */
@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(0, 255, 157, 0.5);
    }
    50% {
        text-shadow: 0 0 15px rgba(0, 255, 157, 0.8), 0 0 5px rgba(255, 255, 255, 0.5);
    }
}

.digital-clock-time {
    animation: glow 2s infinite;
}

/* Animation de bordure */
@keyframes borderPulse {
    0%, 100% {
        border-color: rgba(255, 255, 255, 0.2);
    }
    50% {
        border-color: rgba(0, 255, 157, 0.5);
    }
}

.digital-clock {
    animation: borderPulse 4s infinite;
}

/* Responsive */
@media (max-width: 992px) {
    .digital-clock {
        display: none;
    }
}
