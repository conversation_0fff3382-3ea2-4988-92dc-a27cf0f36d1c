:root {
    --primary-color: #4B5320;
    --secondary-color: #6B8E23;
    --accent-color: #8B4513;
    --background-color: #F5F5DC;
    --text-color: #2E2E2E;
    --success-color: #3B5323;
    --warning-color: #D2B48C;
    --camo-light: #7D8C65;
    --camo-mid: #4A5D23;
    --camo-dark: #2F3A17;
    --camo-tan: #A89976;
    --camo-brown: #5D4B35;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}

/* Wrapper */
.wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
    background-color: var(--background-color);
}

/* Sidebar */
#sidebar {
    width: var(--sidebar-width);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background: linear-gradient(135deg, var(--camo-dark), var(--primary-color));
    color: #fff;
    transition: all 0.3s;
    box-shadow: 4px 0 15px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
}

#sidebar.active {
    width: var(--sidebar-collapsed-width);
}

#sidebar .sidebar-header {
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar .sidebar-header .logo {
    width: 100px;
    height: auto;
    margin-bottom: 10px;
    transition: all 0.3s;
}

#sidebar.active .sidebar-header .logo {
    width: 40px;
}

#sidebar .sidebar-header h3 {
    font-size: 1.2em;
    margin: 0;
    transition: all 0.3s;
    white-space: nowrap;
    overflow: hidden;
}

#sidebar.active .sidebar-header h3 {
    display: none;
}

/* Sidebar Links */
#sidebar ul.components {
    padding: 20px 0;
    flex-grow: 1;
    overflow-y: auto;
}

#sidebar ul li {
    position: relative;
}

#sidebar ul li a {
    padding: 15px 25px;
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
    border-left: 4px solid transparent;
}

#sidebar ul li a i {
    width: 30px;
    font-size: 1.2em;
    margin-right: 10px;
    text-align: center;
}

#sidebar.active ul li a span {
    display: none;
}

#sidebar ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: var(--accent-color);
}

#sidebar ul li.active > a {
    background: rgba(255, 255, 255, 0.2);
    border-left-color: var(--accent-color);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    color: #fff;
    text-decoration: none;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.logout-btn i {
    margin-right: 10px;
}

#sidebar.active .logout-btn span {
    display: none;
}

/* Content */
#content {
    width: calc(100% - var(--sidebar-width));
    margin-left: var(--sidebar-width);
    transition: all 0.3s;
    min-height: 100vh;
    padding: 20px;
    position: relative;
}

#content.active {
    width: calc(100% - var(--sidebar-collapsed-width));
    margin-left: var(--sidebar-collapsed-width);
}

/* Navbar */
.navbar {
    background: var(--primary-color);
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

#sidebarCollapse {
    background: transparent;
    border: none;
    color: #fff;
    font-size: 1.5em;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

#sidebarCollapse:hover {
    color: var(--accent-color);
}

/* Digital Clock */
.digital-clock {
    background: rgba(0, 0, 0, 0.2);
    padding: 10px 20px;
    border-radius: 5px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #fff;
}

.user-name {
    font-weight: 500;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #fff;
}

/* Welcome Content */
.welcome-title {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-text {
    font-size: 1.2em;
    color: var(--text-color);
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    #sidebar {
        margin-left: calc(-1 * var(--sidebar-width));
    }
    
    #sidebar.active {
        margin-left: 0;
        width: var(--sidebar-width);
    }
    
    #content {
        width: 100%;
        margin-left: 0;
    }
    
    #content.active {
        width: calc(100% - var(--sidebar-width));
        margin-left: var(--sidebar-width);
    }
    
    #sidebar.active .sidebar-header h3,
    #sidebar.active ul li a span,
    #sidebar.active .logout-btn span {
        display: inline;
    }
    
    .digital-clock {
        display: none;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background-color: var(--success-color);
    color: #fff;
}

.alert-danger {
    background-color: #dc3545;
    color: #fff;
}

.alert-warning {
    background-color: var(--warning-color);
    color: var(--text-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
} 