{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Mouvements - {{ militaire.nom }} {{ militaire.prenom }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-route me-2"></i>
                            Gestion des Mouvements - {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'Grade' }} {{ militaire.nom }} {{ militaire.prenom }}
                        </h4>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i> Retour à la Fiche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire Nouvelle Mutation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus me-2"></i>Nouvelle Mutation</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="nouvelle_mutation">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Unité d'Origine</label>
                                <select name="unite_origine_id" class="form-control" required>
                                    <option value="">Sélectionner</option>
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}" 
                                            {% if militaire.unite_id == unite.id_unite %}selected{% endif %}>
                                        {{ unite.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Unité de Destination</label>
                                <select name="unite_destination_id" class="form-control" required>
                                    <option value="">Sélectionner</option>
                                    {% for unite in unites %}
                                    <option value="{{ unite.id_unite }}">{{ unite.libelle }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Date Mutation</label>
                                <input type="date" name="date_mutation" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Motif</label>
                                <input type="text" name="motif" class="form-control" placeholder="Motif de la mutation">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Observations</label>
                                <textarea name="observations" class="form-control" rows="2" placeholder="Observations..."></textarea>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-plus"></i> Ajouter Mutation
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des Mouvements -->
    <div class="row">
        <!-- Mutations -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h6><i class="fas fa-exchange-alt me-2"></i>Mutations ({{ mutations|length }})</h6>
                </div>
                <div class="card-body">
                    {% if mutations %}
                        {% for mut in mutations %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ mut.date_mutation.strftime('%d/%m/%Y') }}</strong>
                                    <span class="badge bg-primary ms-2">Mutation</span>
                                    <div class="text-muted">
                                        {{ mut.unite_origine.libelle if mut.unite_origine else 'Origine inconnue' }}
                                        → {{ mut.unite_destination.libelle if mut.unite_destination else 'Destination inconnue' }}
                                    </div>
                                    {% if mut.motif %}
                                    <small class="text-muted">{{ mut.motif }}</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="modifierMutation({{ mut.id_mutation }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune mutation enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Séjours Opérationnels -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-globe me-2"></i>Séjours Opérationnels ({{ sejours_ops|length }})</h6>
                </div>
                <div class="card-body">
                    {% if sejours_ops %}
                        {% for sejour in sejours_ops %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <strong>{{ sejour.pays_destination }}</strong>
                                <small class="text-muted">
                                    {% if sejour.date_fin %}
                                        {{ (sejour.date_fin - sejour.date_debut).days }} jours
                                    {% else %}
                                        En cours
                                    {% endif %}
                                </small>
                            </div>
                            <div class="text-muted">
                                {{ sejour.date_debut.strftime('%d/%m/%Y') }}
                                {% if sejour.date_fin %}
                                    → {{ sejour.date_fin.strftime('%d/%m/%Y') }}
                                {% else %}
                                    → En cours
                                {% endif %}
                            </div>
                            {% if sejour.mission %}
                            <small class="text-muted">{{ sejour.mission }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucun séjour opérationnel enregistré</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sanctions -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h6><i class="fas fa-gavel me-2"></i>Sanctions ({{ sanctions|length }})</h6>
                </div>
                <div class="card-body">
                    {% if sanctions %}
                        {% for sanction in sanctions %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <strong>{{ sanction.type_sanction }}</strong>
                                <small class="text-muted">{{ sanction.date_sanction.strftime('%d/%m/%Y') }}</small>
                            </div>
                            {% if sanction.duree_jours %}
                            <div class="text-muted">Durée: {{ sanction.duree_jours }} jours</div>
                            {% endif %}
                            {% if sanction.motif %}
                            <small class="text-muted">{{ sanction.motif }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune sanction enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Libérations -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6><i class="fas fa-door-open me-2"></i>Libérations ({{ liberations|length }})</h6>
                </div>
                <div class="card-body">
                    {% if liberations %}
                        {% for lib in liberations %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <strong>{{ lib.type_liberation }}</strong>
                                <small class="text-muted">{{ lib.date_liberation.strftime('%d/%m/%Y') }}</small>
                            </div>
                            {% if lib.motif %}
                            <small class="text-muted">{{ lib.motif }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune libération enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
