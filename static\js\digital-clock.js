// Script pour l'horloge numérique moderne
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour mettre à jour l'horloge numérique
    function updateDigitalClock() {
        const now = new Date();
        
        // Obtenir les heures, minutes et secondes
        let hours = now.getHours();
        const minutes = now.getMinutes().toString().padStart(2, '0');
        const seconds = now.getSeconds().toString().padStart(2, '0');
        
        // Format 12 heures avec AM/PM
        const ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // l'heure '0' doit être '12'
        const hoursStr = hours.toString().padStart(2, '0');
        
        // Obtenir la date
        const options = { weekday: 'long', day: 'numeric', month: 'long' };
        const dateStr = now.toLocaleDateString('fr-FR', options);
        
        // Mettre à jour l'affichage
        const timeElement = document.getElementById('digitalClockTime');
        const secondsElement = document.getElementById('digitalClockSeconds');
        const ampmElement = document.getElementById('digitalClockAMPM');
        const dateElement = document.getElementById('digitalClockDate');
        
        if (timeElement) {
            timeElement.innerHTML = `${hoursStr}<span class="digital-clock-colon">:</span>${minutes}`;
        }
        
        if (secondsElement) {
            secondsElement.textContent = seconds;
        }
        
        if (ampmElement) {
            ampmElement.textContent = ampm;
        }
        
        if (dateElement) {
            dateElement.textContent = dateStr;
        }
    }
    
    // Mettre à jour l'horloge chaque seconde
    updateDigitalClock(); // Mise à jour initiale
    setInterval(updateDigitalClock, 1000);
    
    // Ajouter un effet de pulsation au changement de minute
    function checkMinuteChange() {
        const now = new Date();
        const seconds = now.getSeconds();
        
        if (seconds === 0) {
            const clockElement = document.querySelector('.digital-clock');
            if (clockElement) {
                clockElement.style.boxShadow = '0 0 15px rgba(25, 135, 84, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.4)';
                
                setTimeout(() => {
                    clockElement.style.boxShadow = '';
                }, 1000);
            }
        }
    }
    
    setInterval(checkMinuteChange, 1000);
});
