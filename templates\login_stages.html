<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Gestion des Stages</title>
    <link href="https://fonts.googleapis.com/css2?family=Black+Ops+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login-mobile.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto Condensed', sans-serif;
        }
        body {
            min-height: 100vh;
            background: none;
            background-size: cover;
            background-position: center;
            display: flex;
            overflow: auto;
            position: relative;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(75, 83, 32, 0.3), rgba(59, 66, 25, 0.3));
            z-index: 1;
        }
        .background-slider {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 0;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .background-slider img {
            position: absolute;
            width: auto;
            height: auto;
            min-width: 100%;
            min-height: 100%;
            max-width: none;
            max-height: none;
            object-fit: contain;
            object-position: center;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        .background-slider img.active {
            opacity: 1;
        }
        .background-slider::before {
            display: none;
        }
        .ops {
            font-family: 'Black Ops One', cursive;
            color: #ffffff;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(0,0,0,0.8);
            letter-spacing: 3px;
            font-weight: 700;
            text-transform: uppercase;
            line-height: 1.2;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .ops .title-line {
            display: inline-block;
            position: relative;
            transition: all 0.3s ease;
            padding: 0 5px;
        }
        @keyframes militaryGlow {
            0%, 100% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 10px rgba(0,0,0,0.8); }
            50% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 30px rgba(139, 115, 85, 1); }
        }
        .ops span:last-child {
            animation: militaryGlow 3s infinite;
        }
        .ops::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0));
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
            z-index: -1;
        }
        .ops:hover::before {
            opacity: 1;
            background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0.2));
            animation: pulseGradient 2s infinite alternate;
        }
        @keyframes pulseGradient {
            0% { transform: scale(1); background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0.2)); }
            100% { transform: scale(1.1); background: radial-gradient(circle at center, transparent 60%, rgba(230, 199, 139, 0.3)); }
        }
        .ops .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #e6c78b;
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 0 5px rgba(230, 199, 139, 0.8);
        }
        .ops:hover .particle {
            opacity: 1;
            animation: floatParticle 3s infinite ease-in-out;
        }
        @keyframes floatParticle {
            0% { transform: translateY(0) translateX(0); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(-30px) translateX(10px); opacity: 0; }
        }
        .military-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(45deg,
                    transparent 0,
                    transparent 10px,
                    rgba(0,0,0,0.1) 10px,
                    rgba(0,0,0,0.1) 20px
                );
            pointer-events: none;
            z-index: 2;
        }
        .border-frame {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid #8b7355;
            pointer-events: none;
            z-index: 3;
        }
        .border-frame::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px dashed #4b5320;
            animation: borderPulse 2s infinite;
        }
        @keyframes borderPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        .container {
            display: flex;
            width: 100%;
            padding: 40px;
            position: relative;
            z-index: 5;
            min-height: 100vh;
            height: auto;
        }
        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #d0d0c0;
            padding: 40px;
            position: relative;
            z-index: 6;
        }
        .left-section h1 {
            font-family: 'Black Ops One', cursive;
            font-size: 3em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #ffffff;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(0,0,0,0.8);
        }
        .right-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 40px;
            z-index: 6;
            position: relative;
        }
        .login-box {
            background: rgba(75, 83, 32, 0.2);
            border: 4px solid #8b7355;
            padding: 30px;
            width: 100%;
            max-width: 480px;
            position: relative;
            box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 15px rgba(139, 115, 85, 0.5);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            animation: boxGlow 3s infinite alternate;
        }
        @keyframes boxGlow {
            0% { box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 15px rgba(139, 115, 85, 0.5); }
            100% { box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 25px rgba(139, 115, 85, 0.8); }
        }
        .login-box::before,
        .login-box::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            border: 3px solid #8b7355;
        }
        .login-box::before {
            top: -12px;
            left: -12px;
            border-right: none;
            border-bottom: none;
            box-shadow: -3px -3px 10px rgba(0,0,0,0.3);
        }
        .login-box::after {
            bottom: -12px;
            right: -12px;
            border-left: none;
            border-top: none;
            box-shadow: 3px 3px 10px rgba(0,0,0,0.3);
        }
        .login-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
        }
        .login-header img {
            width: 160px;
            height: 160px;
            margin-bottom: 15px;
            filter: drop-shadow(0 0 15px rgba(139, 115, 85, 0.7));
            transition: transform 0.3s ease;
        }
        .login-header img:hover {
            transform: scale(1.05);
        }
        .form-group {
            position: relative;
            margin-bottom: 25px;
        }
        .form-group input {
            width: 100%;
            padding: 12px 40px 12px 40px;
            border: 2px solid #8b7355;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            color: #fff;
            font-size: 16px;
            font-family: 'Roboto Condensed', sans-serif;
            outline: none;
            transition: border 0.3s, box-shadow 0.3s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .form-group input:focus {
            border-color: #e6c78b;
            box-shadow: 0 0 8px #e6c78b44;
        }
        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #e6c78b;
            font-size: 22px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5), 0 0 8px rgba(230, 199, 139, 0.6);
            transition: all 0.3s ease;
            z-index: 2;
        }
        .form-group:hover i {
            color: #f0d9a8;
            transform: translateY(-50%) scale(1.1);
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5), 0 0 15px rgba(240, 217, 168, 0.8);
        }
        .form-group i::after {
            content: '';
            position: absolute;
            width: 32px;
            height: 32px;
            background: rgba(139, 115, 85, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            box-shadow: 0 0 10px rgba(230, 199, 139, 0.4);
            transition: all 0.3s ease;
        }
        .form-group:hover i::after {
            background: rgba(139, 115, 85, 0.3);
            width: 36px;
            height: 36px;
            box-shadow: 0 0 15px rgba(230, 199, 139, 0.6);
        }
        .form-group input::placeholder {
            color: #ffe082;
            opacity: 1;
            font-weight: 600;
            letter-spacing: 1px;
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4b5320;
            border: 2px solid #8b7355;
            color: #ffffff;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            overflow: hidden;
            font-family: 'Black Ops One', cursive;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .login-btn::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, #8b7355, transparent, #8b7355);
            z-index: -1;
            animation: borderGlow 3s linear infinite;
            opacity: 0.7;
        }
        .login-btn:hover {
            background: #5a6427;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
            text-shadow: 1px 1px 3px rgba(0,0,0,0.6);
        }
        @keyframes borderGlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 2px solid #8b7355;
            color: #d0d0c0;
            text-align: center;
            position: relative;
            background: rgba(0,0,0,0.2);
        }
        .alert::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px dashed #8b7355;
            pointer-events: none;
        }
        .alert-danger {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.2);
        }
        .alert-success {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.2);
        }
        @media (max-width: 1024px) {
            .container {
                flex-direction: column;
                height: auto;
                max-height: none;
                padding: 20px;
            }
            .left-section {
                padding: 20px;
                text-align: center;
            }
            .left-section h1 {
                font-size: 2em;
            }
            .right-section {
                padding: 20px;
            }
        }
        body {
            min-height: 100vh;
        }
        .auth-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }
        .auth-btn {
            background: linear-gradient(45deg, #8b7355 60%, #4b5320 100%);
            color: #fff;
            border: none;
            padding: 12px 28px;
            border-radius: 10px;
            font-size: 1.08em;
            font-family: 'Black Ops One', cursive;
            letter-spacing: 1px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.18);
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: background 0.2s, transform 0.2s, box-shadow 0.2s;
            outline: none;
        }
        .auth-btn i {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .auth-btn.qr-btn {
            background: linear-gradient(45deg, #8b7355 70%, #e6c78b 100%);
        }
        .auth-btn.facial-btn {
            background: linear-gradient(45deg, #4b5320 70%, #8b7355 100%);
        }
        .auth-btn:hover, .auth-btn:focus {
            background: linear-gradient(45deg, #ffe082 60%, #8b7355 100%);
            color: #212529;
            transform: translateY(-2px) scale(1.04);
            box-shadow: 0 4px 16px rgba(230, 199, 139, 0.25);
        }
        .divider {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 18px 0 10px 0;
        }
        .divider span {
            display: block;
            color: #ffe082 !important;
            font-family: 'Black Ops One', cursive !important;
            font-size: 1.08em !important;
            letter-spacing: 2px;
            text-align: center;
            text-shadow: 1px 1px 6px rgba(0,0,0,0.25);
            background: none;
            padding: 0 12px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="background-slider">
        <img src="{{ url_for('static', filename='images/s1.jpeg') }}" alt="Background 1" class="active">
        <img src="{{ url_for('static', filename='images/s2.jpg') }}" alt="Background 2">
        <img src="{{ url_for('static', filename='images/s3.png') }}" alt="Background 3">
    </div>
    <div class="military-overlay"></div>
    <div class="border-frame"></div>
    <div class="container">
        <div class="left-section">
            <h1 class="ops" style="text-align: center;">
                <span class="title-line">SYSTÈME DE</span>
                <span class="title-line">GESTION DES STAGES</span>
                <span class="title-line" style="display: block; font-size: 1.2em; color: #ffe082; text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px #ffe08299;">DE L'ARTILLERIE</span>
            </h1>
        </div>
        <div class="right-section">
            <div class="login-box">
                <div class="login-header">
                    <div style="display: flex; justify-content: center; align-items: center; gap: 40px; margin-bottom: 15px;">
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" style="width: 140px; height: auto; filter: drop-shadow(0 0 10px rgba(139, 115, 85, 0.7));">
                        <img src="{{ url_for('static', filename='images/far.png') }}" alt="FAR" style="width: 180px; height: auto; filter: drop-shadow(0 0 10px rgba(139, 115, 85, 0.7));">
                    </div>
                </div>
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                <form method="POST" action="{{ url_for('login_stages') }}">
                    <div class="form-group">
                        <i class="fas fa-id-card-alt"></i>
                        <input type="text" id="username" name="username" placeholder="Nom d'utilisateur" required>
                    </div>
                    <div class="form-group">
                        <i class="fas fa-key"></i>
                        <input type="password" id="password" name="password" placeholder="Mot de passe" required>
                    </div>
                    <button type="submit" class="login-btn">Accéder aux stages</button>
                </form>
                <div class="auth-options">
                    <div class="divider">
                        <span>MÉTHODES ALTERNATIVES</span>
                    </div>
                    <div class="auth-buttons">
                        <button class="auth-btn qr-btn" type="button"><i class="fas fa-qrcode"></i>QR Code</button>
                        <button class="auth-btn facial-btn" type="button"><i class="fas fa-user-circle"></i>Reconnaissance Faciale</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // Copier ici les scripts de login.html pour le slider et les effets
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.background-slider img');
            let currentIndex = 0;
            function nextImage() {
                images[currentIndex].classList.remove('active');
                currentIndex = (currentIndex + 1) % images.length;
                images[currentIndex].classList.add('active');
            }
            setInterval(nextImage, 2000);
        });
        document.addEventListener('DOMContentLoaded', function() {
            const title = document.querySelector('.ops');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = (Math.random() * 2) + 's';
                title.appendChild(particle);
            }
        });
    </script>
</body>
</html> 