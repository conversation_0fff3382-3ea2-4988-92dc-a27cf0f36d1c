{% extends "stages/base_stages.html" %}

{% block title %}Ajouter un Document{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Ajouter un Document</h1>
    </div>
    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="mb-3">
                    <label for="type_doc" class="form-label">Type de document</label>
                    <select class="form-select" id="type_doc" name="type_doc" required>
                        <option value="">Sélectionner...</option>
                        <option value="attestation">Attestation de stage</option>
                        <option value="convention">Convention</option>
                        <option value="bilan">Bilan</option>
                        <option value="autre">Autre</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="fichier" class="form-label">Fichier</label>
                    <input type="file" class="form-control" id="fichier" name="fichier" required>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_documents', stagiaire_id=stagiaire.id_stagiaire) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 