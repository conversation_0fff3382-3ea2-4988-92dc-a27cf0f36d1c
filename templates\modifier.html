{% extends "base.html" %}

{% block title %}Modifier un Véhicule{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Modifier le Statut du Véhicule - Matricule: {{ vehicule.matricule }}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type_vehicule" class="form-label">
                                    <i class="fas fa-truck me-1"></i>Type de Véhicule*
                                </label>
                                <select class="form-select" id="type_vehicule" name="type_vehicule" required>
                                    <option value="">Sélectionnez un type</option>
                                    {% for type in types_vehicules.keys() %}
                                    <option value="{{ type }}" {% if vehicule.type_vehicule == type %}selected{% endif %}>{{ type }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un type de véhicule
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unite" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>Unité du Véhicule*
                                </label>
                                <select class="form-select" id="unite" name="unite" required>
                                    <option value="">Sélectionnez une unité</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une unité
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="marque" class="form-label">
                                    <i class="fas fa-building me-1"></i>Marque*
                                </label>
                                <select class="form-select" id="marque" name="marque" required>
                                    <option value="">Sélectionnez d'abord un type</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une marque
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="modele" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>Matricule* (non modifiable)
                                </label>
                                <input type="text" class="form-control bg-light" id="modele" name="modele" value="{{ vehicule.matricule }}" required readonly>
                                <small class="text-muted">Le matricule ne peut pas être modifié pour préserver l'intégrité de l'historique</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="type_panne" class="form-label">
                                    <i class="fas fa-wrench me-1"></i>Type de Panne*
                                </label>
                                <select class="form-select" id="type_panne" name="type_panne" required>
                                    <option value="{{ vehicule.type_panne }}">{{ vehicule.type_panne }}</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un type de panne
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_panne" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>Date de la Panne*
                                </label>
                                <input type="date" class="form-control" id="date_panne" name="date_panne" value="{{ vehicule.date_panne.strftime('%Y-%m-%d') }}" required>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner la date de la panne
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="statut" class="form-label">
                                    <i class="fas fa-info-circle me-1"></i>Statut* <span class="badge bg-primary">Seul champ modifiable</span>
                                </label>
                                <select class="form-select border border-primary" id="statut" name="statut" required>
                                    <option value="En panne" {% if vehicule.statut == "En panne" %}selected{% endif %}>En panne</option>
                                    <option value="Indisponible" {% if vehicule.statut == "Indisponible" %}selected{% endif %}>Indisponible</option>
                                    <option value="En réparation" {% if vehicule.statut == "En réparation" %}selected{% endif %}>En réparation</option>
                                    <option value="Réparé" {% if vehicule.statut == "Réparé" %}selected{% endif %}>Réparé</option>
                                </select>
                                <small class="text-primary">Chaque changement de statut sera enregistré dans l'historique du véhicule</small>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un statut
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="date_modification" class="form-label" id="date_modification_label">
                                    <i class="fas fa-calendar-alt me-1"></i>Date de modification du statut*
                                </label>
                                <input type="date" class="form-control border border-primary" id="date_modification" name="date_modification" value="{{ now.strftime('%Y-%m-%d') }}" required>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une date
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-comment me-1"></i>Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ vehicule.description }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Mettre à jour le statut
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function () {
    'use strict'
    var forms = document.querySelectorAll('.needs-validation')
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            // Vérifier la validité du formulaire
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            } else {
                // Si le formulaire est valide, réactiver temporairement les champs désactivés
                const disabledFields = form.querySelectorAll('select:disabled, input:disabled, textarea:disabled');
                disabledFields.forEach(field => {
                    field.disabled = false;
                });

                // Laisser le formulaire se soumettre normalement
                console.log("Formulaire valide, soumission en cours...");
            }

            form.classList.add('was-validated');
        }, false)
    })
})()

// Variables pour stocker les valeurs actuelles
const currentType = "{{ vehicule.type_vehicule }}";
const currentMarque = "{{ vehicule.marque }}";
const currentPanne = "{{ vehicule.type_panne }}";
const currentUnite = "{{ vehicule.unite }}";

// Gestion dynamique des marques et des types de pannes
document.getElementById('type_vehicule').addEventListener('change', function() {
    loadDependentData(this.value);
});

// Fonction pour charger les données dépendantes (marques et pannes)
function loadDependentData(selectedType) {
    const marqueSelect = document.getElementById('marque');
    const typePanneSelect = document.getElementById('type_panne');

    // Gestion des marques
    marqueSelect.disabled = !selectedType;
    marqueSelect.innerHTML = '<option value="">Sélectionnez une marque</option>';

    if (selectedType) {
        // Charger les marques
        fetch(`/get_marques/${selectedType}`)
            .then(response => response.json())
            .then(marques => {
                marques.forEach(marque => {
                    const option = document.createElement('option');
                    option.value = marque;
                    option.textContent = marque;
                    if (marque === currentMarque) {
                        option.selected = true;
                    }
                    marqueSelect.appendChild(option);
                });
            });
    }

    // Conserver le type de panne actuel
    typePanneSelect.disabled = true;
    typePanneSelect.innerHTML = `<option value="${currentPanne}">${currentPanne}</option>`;
}

// Fonction pour mettre à jour le libellé du champ de date en fonction du statut
function updateDateLabel(statut) {
    const dateLabel = document.getElementById('date_modification_label');

    switch(statut) {
        case 'Réparé':
            dateLabel.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>Date de réparation*';
            break;
        case 'Indisponible':
            dateLabel.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>Date d\'indisponibilité*';
            break;
        case 'En réparation':
            dateLabel.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>Date de mise en réparation*';
            break;
        case 'En panne':
            dateLabel.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>Date de panne*';
            break;
        default:
            dateLabel.innerHTML = '<i class="fas fa-calendar-alt me-1"></i>Date de modification du statut*';
    }
}

// Écouter les changements sur le champ statut
document.getElementById('statut').addEventListener('change', function() {
    updateDateLabel(this.value);
});

// Charger les unités au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Charger les unités depuis la base de données
    fetch('/get_unites')
        .then(response => response.json())
        .then(unites => {
            const uniteSelect = document.getElementById('unite');
            unites.forEach(unite => {
                const option = document.createElement('option');
                option.value = unite;
                option.textContent = unite;
                if (unite === currentUnite) {
                    option.selected = true;
                }
                uniteSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Erreur lors du chargement des unités:', error);
        });

    // Charger les marques et pannes pour le type de véhicule actuel
    if (currentType) {
        loadDependentData(currentType);
    }

    // Désactiver tous les champs sauf le statut et la date de modification
    document.getElementById('type_vehicule').disabled = true;
    document.getElementById('marque').disabled = true;
    document.getElementById('type_panne').disabled = true;
    document.getElementById('date_panne').disabled = true;
    document.getElementById('description').disabled = true;
    document.getElementById('unite').disabled = true;

    // Ajouter une classe pour indiquer visuellement que les champs sont désactivés
    const disabledFields = document.querySelectorAll('select:disabled, input:disabled, textarea:disabled');
    disabledFields.forEach(field => {
        field.classList.add('bg-light');
    });

    // Mettre à jour le libellé du champ de date en fonction du statut actuel
    updateDateLabel(document.getElementById('statut').value);

    // Animation du formulaire
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        setTimeout(() => {
            group.style.transition = 'all 0.3s ease';
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
