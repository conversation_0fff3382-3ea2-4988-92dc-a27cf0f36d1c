{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Absences - {{ militaire.nom }} {{ militaire.prenom }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-calendar-times me-2"></i>
                            Gestion des Absences - {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'Grade' }} {{ militaire.nom }} {{ militaire.prenom }}
                        </h4>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i> Retour à la Fiche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire Nouvelle Permission -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus me-2"></i>Nouvelle Permission</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="nouvelle_permission">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Type de Permission</label>
                                <select name="type_permission" class="form-control" required>
                                    <option value="">Sélectionner</option>
                                    <option value="Permission ordinaire">Permission ordinaire</option>
                                    <option value="Permission exceptionnelle">Permission exceptionnelle</option>
                                    <option value="Permission maladie">Permission maladie</option>
                                    <option value="Permission maternité">Permission maternité</option>
                                    <option value="Permission décès">Permission décès</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Date Début</label>
                                <input type="date" name="date_debut" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Date Fin</label>
                                <input type="date" name="date_fin" class="form-control" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Motif</label>
                                <input type="text" name="motif" class="form-control" placeholder="Motif de la permission">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Observations</label>
                                <textarea name="observations" class="form-control" rows="2" placeholder="Observations..."></textarea>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Ajouter Permission
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des Absences -->
    <div class="row">
        <!-- Permissions -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h6><i class="fas fa-calendar-check me-2"></i>Permissions ({{ permissions|length }})</h6>
                </div>
                <div class="card-body">
                    {% if permissions %}
                        {% for perm in permissions %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ perm.type_permission }}</strong>
                                    <small class="text-muted ms-2">{{ (perm.date_fin - perm.date_debut).days + 1 }} jours</small>
                                    <div class="text-muted">
                                        {{ perm.date_debut.strftime('%d/%m/%Y') }} → {{ perm.date_fin.strftime('%d/%m/%Y') }}
                                    </div>
                                    {% if perm.motif %}
                                    <small class="text-muted">{{ perm.motif }}</small>
                                    {% endif %}
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="modifierPermission({{ perm.id_permission }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune permission enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Détachements -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-warning text-dark">
                    <h6><i class="fas fa-exchange-alt me-2"></i>Détachements ({{ detachements|length }})</h6>
                </div>
                <div class="card-body">
                    {% if detachements %}
                        {% for det in detachements %}
                        <div class="border-bottom pb-2 mb-2">
                            <strong>{{ det.unite_destination }}</strong>
                            <div class="text-muted">
                                {{ det.date_debut.strftime('%d/%m/%Y') }}
                                {% if det.date_fin %}
                                    → {{ det.date_fin.strftime('%d/%m/%Y') }}
                                {% else %}
                                    → En cours
                                {% endif %}
                            </div>
                            {% if det.motif %}
                            <small class="text-muted">{{ det.motif }}</small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucun détachement enregistré</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Désertions -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-danger text-white">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Désertions ({{ desertions|length }})</h6>
                </div>
                <div class="card-body">
                    {% if desertions %}
                        {% for des in desertions %}
                        <div class="border-bottom pb-2 mb-2">
                            <strong>{{ des.date_absence.strftime('%d/%m/%Y') }}</strong>
                            {% if des.date_retour %}
                                <span class="badge bg-success">Retourné le {{ des.date_retour.strftime('%d/%m/%Y') }}</span>
                            {% else %}
                                <span class="badge bg-danger">En cours</span>
                            {% endif %}
                            {% if des.motif %}
                            <div><small class="text-muted">{{ des.motif }}</small></div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune désertion enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
