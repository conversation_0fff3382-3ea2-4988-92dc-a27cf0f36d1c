#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier l'état des tables dans la base de données
"""

import mysql.connector
from mysql.connector import Error

def check_tables():
    """Vérifier les tables existantes"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        if tables:
            print(f"📋 {len(tables)} tables trouvées :")

            # Filtrer les tables RH
            rh_tables = [table[0] for table in tables if 'referentiel_' in table[0] or table[0] in ['personnel', 'conjoint', 'enfant', 'situation_medicale', 'vaccination', 'ptc', 'absence_desertion', 'absence_detachement', 'absence_permission', 'mouvement_interbie', 'sejour_ops', 'liberation', 'personnel_langue', 'historique_grade']]

            print(f"\n🎯 Tables RH trouvées ({len(rh_tables)}/25):")
            for table in sorted(rh_tables):
                print(f"  ✓ {table}")

            if len(rh_tables) >= 24:
                print("\n✅ ÉTAPE 3 TERMINÉE AVEC SUCCÈS!")
                print("Les tables RH sont créées et prêtes.")
            else:
                print(f"\n⚠️ Seulement {len(rh_tables)} tables RH trouvées sur 25 attendues")
        else:
            print("✅ Aucune table trouvée - Base de données vide")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"❌ Erreur : {e}")

if __name__ == "__main__":
    check_tables()
