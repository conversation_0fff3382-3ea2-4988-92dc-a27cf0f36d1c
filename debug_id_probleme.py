#!/usr/bin/env python3
"""
Debug pour identifier pourquoi l'ID n'est pas affiché correctement
"""

import requests
import json
import webbrowser
from datetime import datetime

BASE_URL = "http://127.0.0.1:3000"

def test_via_interface_web():
    """Ouvrir l'interface pour test manuel"""
    print("🌐 Ouverture de l'interface pour test manuel...")
    webbrowser.open(f"{BASE_URL}/courrier/arrives")
    
    print("\n📋 INSTRUCTIONS DE TEST MANUEL:")
    print("1. ➕ Cliquez sur 'Ajouter Courrier'")
    print("2. 📝 Dans le champ 'ID Courrier', saisissez: 5555")
    print("3. 📝 Remplissez les autres champs obligatoires:")
    print("   • Urgence: Urgent")
    print("   • Nature: Message") 
    print("   • Date d'arrivée: Aujourd'hui")
    print("   • N° Écrit: TEST-5555")
    print("   • Expéditeur: Test Bureau")
    print("   • Objet: Test ID personnalisé")
    print("4. ✅ Cliquez sur 'Ajouter'")
    print("5. 👀 Vérifiez dans le tableau si l'ID affiché est '5555'")
    print("6. 🔍 Si ce n'est pas le cas, ouvrez la console (F12) pour voir les erreurs")

def test_api_directe():
    """Test direct via API pour vérifier le backend"""
    print("\n🔧 Test direct via API...")
    
    courrier_test = {
        "id": "7777",
        "urgence": "urgent",
        "nature": "message",
        "date_arrivee": datetime.now().strftime('%Y-%m-%d'),
        "date_signature": datetime.now().strftime('%Y-%m-%d'),
        "numero_ecrit": "API-7777",
        "expediteur": "Test API Direct",
        "objet": "Test API direct - ID 7777",
        "classification": "public",
        "annotation": "Test via API directe",
        "divisions_action": ["technique"],
        "divisions_info": ["rh"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_test)
        )
        
        if response.status_code == 201:
            courrier_ajoute = response.json()
            print(f"✅ API: Courrier ajouté")
            print(f"   ID envoyé: 7777")
            print(f"   ID retourné: {courrier_ajoute['id']}")
            
            if courrier_ajoute['id'] == "7777":
                print("✅ Backend: L'ID est correctement conservé")
            else:
                print("❌ Backend: L'ID est modifié")
                
        else:
            print(f"❌ Erreur API: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def verifier_template_javascript():
    """Vérifier le contenu du template JavaScript"""
    print("\n🔍 Vérification du template JavaScript...")
    
    try:
        response = requests.get(f"{BASE_URL}/courrier/arrives")
        if response.status_code == 200:
            content = response.text
            
            # Chercher la ligne problématique
            if "'CA-' + Date.now()" in content:
                print("❌ PROBLÈME TROUVÉ: Le template contient encore 'CA-' + Date.now()")
                print("   La correction n'a pas été appliquée ou le cache n'est pas vidé")
            elif "id: id," in content or "id," in content:
                print("✅ CORRECTION PRÉSENTE: Le template utilise l'ID saisi")
            else:
                print("⚠️  INCERTAIN: Impossible de déterminer le statut")
                
            # Chercher d'autres patterns problématiques
            if "id: 'CA-'" in content:
                print("❌ Pattern 'CA-' trouvé dans le template")
            
        else:
            print(f"❌ Erreur lors de la récupération du template: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def lister_courriers_actuels():
    """Lister tous les courriers actuels pour voir les IDs"""
    print("\n📋 Courriers actuels dans la base:")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers = response.json()
            print(f"📥 {len(courriers)} courriers trouvés:")
            
            for i, courrier in enumerate(courriers, 1):
                print(f"   {i}. ID: '{courrier['id']}' - Objet: {courrier['objet']}")
                
        else:
            print(f"❌ Erreur API: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Fonction principale de debug"""
    print("🐛 DEBUG - PROBLÈME D'AFFICHAGE DE L'ID")
    print("=" * 50)
    
    # Vérifier l'application
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Tests de diagnostic
    lister_courriers_actuels()
    test_api_directe()
    verifier_template_javascript()
    test_via_interface_web()
    
    print("\n" + "=" * 50)
    print("🎯 DIAGNOSTIC TERMINÉ")
    print("=" * 50)
    
    print("\n💡 SOLUTIONS POSSIBLES:")
    print("1. 🔄 Videz le cache du navigateur (Ctrl+F5)")
    print("2. 🔄 Redémarrez Flask et rechargez la page")
    print("3. 🔍 Vérifiez la console JavaScript (F12) pour les erreurs")
    print("4. 🧪 Testez en mode incognito")
    
    print("\n📞 Si le problème persiste:")
    print("   • Notez l'ID que vous saisissez")
    print("   • Notez l'ID qui s'affiche")
    print("   • Vérifiez les messages dans la console JavaScript")

if __name__ == "__main__":
    main()
