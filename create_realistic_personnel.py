#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Création de 200 militaires avec données réalistes
50 Officiers, 50 ODR, 100 MDR selon spécifications exactes
"""

import random
from datetime import date, datetime, timedelta
from rh_models import *
from app import app

# Données réalistes marocaines
NOMS_PRENOMS_MAROCAINS = [
    ("ALAMI", "MOHAMMED", "العلمي", "محمد"),
    ("BENALI", "AHMED", "بن علي", "أحمد"),
    ("CHERKAOUI", "HASSAN", "الشرقاوي", "حسن"),
    ("DOUIRI", "OMAR", "الدويري", "عمر"),
    ("EL FASSI", "YOUSSEF", "الفاسي", "يوسف"),
    ("FILALI", "KHALID", "الفيلالي", "خالد"),
    ("GHAZI", "ABDELKADER", "الغازي", "عبد القادر"),
    ("HAJJI", "SAID", "الحاجي", "سعيد"),
    ("IDRISSI", "MUSTAPHA", "الإدريسي", "مصطفى"),
    ("JAMAL", "ABDELLAH", "جمال", "عبد الله"),
    ("KABBAJ", "RACHID", "القباج", "رشيد"),
    ("LAHLOU", "KARIM", "اللحلو", "كريم"),
    ("MANSOURI", "NOUREDDINE", "المنصوري", "نور الدين"),
    ("NACIRI", "ABDERRAHIM", "الناصري", "عبد الرحيم"),
    ("OUALI", "DRISS", "الوالي", "إدريس"),
    ("QADIRI", "BRAHIM", "القادري", "إبراهيم"),
    ("RACHIDI", "HAMID", "الراشدي", "حميد"),
    ("SLAOUI", "JAMAL", "السلاوي", "جمال"),
    ("TAZI", "FOUAD", "التازي", "فؤاد"),
    ("WAHBI", "TARIK", "الوهبي", "طارق"),
    ("AMRANI", "AMINE", "العمراني", "أمين"),
    ("BERRADA", "ZAKARIA", "البرادة", "زكرياء"),
    ("CHRAIBI", "OTHMANE", "الشرايبي", "عثمان"),
    ("DRISSI", "MEHDI", "الإدريسي", "مهدي"),
    ("EL ALAOUI", "AYOUB", "العلوي", "أيوب"),
    ("FASSI", "ISMAIL", "الفاسي", "إسماعيل"),
    ("GUERRAOUI", "BILAL", "الكراوي", "بلال"),
    ("HAKIM", "ADIL", "الحكيم", "عادل"),
    ("ISMAILI", "HICHAM", "الإسماعيلي", "هشام"),
    ("JEBARI", "REDOUANE", "الجباري", "رضوان"),
    ("KETTANI", "AZIZ", "الكتاني", "عزيز"),
    ("LAMRANI", "SAMIR", "اللمراني", "سمير"),
    ("MEKOUAR", "YASSINE", "المكوار", "ياسين"),
    ("NEJJAR", "ABDESLAM", "النجار", "عبد السلام"),
    ("OUAZZANI", "MOUNIR", "الوزاني", "منير")
]

VILLES_MAROCAINES = [
    "Rabat", "Casablanca", "Fès", "Marrakech", "Agadir", "Tanger", 
    "Meknès", "Oujda", "Kenitra", "Tétouan", "Safi", "Mohammedia",
    "Khouribga", "Beni Mellal", "El Jadida", "Nador", "Settat",
    "Larache", "Ksar El Kebir", "Sale", "Berrechid", "Khemisset",
    "Inezgane", "Ouarzazate", "Tiznit", "Taroudant", "Guelmim",
    "Errachidia", "Taza", "Essaouira", "Dakhla", "Laâyoune"
]

QUARTIERS_ADRESSES = [
    "Hay Riad", "Agdal", "Souissi", "Hassan", "Yacoub El Mansour",
    "Maarif", "Gauthier", "Racine", "Bourgogne", "Palmier",
    "Atlas", "Saiss", "Zouagha", "Bensouda", "Narjiss",
    "Gueliz", "Hivernage", "Majorelle", "Semlalia", "Daoudiate",
    "Talborjt", "Hay Mohammadi", "Founty", "Charaf", "Dakhla",
    "Malabata", "Boukhalef", "Mesnana", "California", "Charf"
]

def generate_matricule():
    """Génère un matricule de 7 chiffres"""
    return f"{random.randint(1000000, 9999999)}"

def generate_cin():
    """Génère un CIN : 1 lettre + 6 chiffres"""
    lettre = random.choice(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'])
    chiffres = f"{random.randint(100000, 999999)}"
    return f"{lettre}{chiffres}"

def get_gar_unit():
    """Retourne une unité GAR (1°GAR au 26°GAR)"""
    numero = random.randint(1, 26)
    return f"{numero}°GAR"

def random_date_between(start_date, end_date):
    """Génère une date aléatoire entre deux dates"""
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def create_realistic_personnel():
    """Crée 200 militaires avec données réalistes"""
    
    with app.app_context():
        try:
            print("🚀 Création de 200 militaires avec données réalistes...")
            
            # Récupération des données de référence
            print("📋 Récupération des données de référence...")
            genres = ReferentielGenre.query.all()
            categories = ReferentielCategorie.query.all()
            groupes_sanguin = ReferentielGroupeSanguin.query.all()
            armes = ReferentielArme.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()
            situations_fam = ReferentielSituationFamiliale.query.all()
            degres_parente = ReferentielDegreParente.query.all()
            
            # Identifier les catégories
            officier_cat = next((c for c in categories if c.libelle == "Officier"), None)
            odr_cat = next((c for c in categories if c.libelle == "Officier du rang"), None)
            mdr_cat = next((c for c in categories if c.libelle == "Militaire du rang"), None)
            
            if not all([officier_cat, odr_cat, mdr_cat]):
                print("❌ Erreur: Catégories non trouvées")
                return
            
            # Identifier les unités GAR
            unites_gar = []
            autres_unites = []
            for unite in unites:
                if "GAR" in unite.libelle:
                    unites_gar.append(unite)
                else:
                    autres_unites.append(unite)
            
            print(f"✅ Données de référence chargées:")
            print(f"   - Unités GAR: {len(unites_gar)}")
            print(f"   - Autres unités: {len(autres_unites)}")
            
            matricules_used = set()
            cin_used = set()
            
            # Création des militaires par catégorie
            categories_config = [
                (officier_cat, 50, "Officiers"),
                (odr_cat, 50, "ODR"),
                (mdr_cat, 100, "MDR")
            ]
            
            total_created = 0
            
            for categorie, nombre, nom_cat in categories_config:
                print(f"\n👥 Création de {nombre} {nom_cat}...")
                
                for i in range(nombre):
                    # Génération matricule unique
                    while True:
                        matricule = generate_matricule()
                        if matricule not in matricules_used:
                            matricules_used.add(matricule)
                            break
                    
                    # Génération CIN unique
                    while True:
                        cin = generate_cin()
                        if cin not in cin_used:
                            cin_used.add(cin)
                            break
                    
                    # Sélection nom/prénom
                    nom, prenom, nom_arabe, prenom_arabe = random.choice(NOMS_PRENOMS_MAROCAINS)
                    
                    # Sélection unité (80% GAR, 20% autres)
                    if random.random() < 0.8 and unites_gar:
                        unite = random.choice(unites_gar)
                    else:
                        unite = random.choice(autres_unites) if autres_unites else random.choice(unites_gar)
                    
                    # Dates
                    date_naissance = random_date_between(date(1980, 1, 1), date(2005, 12, 31))
                    date_engagement = random_date_between(date(2000, 1, 1), date(2024, 12, 31))
                    date_delivrance_cin = random_date_between(date(2010, 1, 1), date(2023, 12, 31))
                    date_expiration_cin = random_date_between(date(2025, 1, 1), date(2035, 12, 31))
                    
                    # Ville et adresse réalistes
                    ville = random.choice(VILLES_MAROCAINES)
                    quartier = random.choice(QUARTIERS_ADRESSES)
                    numero_rue = random.randint(1, 999)
                    adresse = f"{numero_rue}, {quartier}, {ville}"
                    
                    # Création du personnel
                    personnel = Personnel(
                        matricule=matricule,
                        nom=nom,
                        prenom=prenom,
                        nom_arabe=nom_arabe,
                        prenom_arabe=prenom_arabe,
                        date_naissance=date_naissance,
                        lieu_naissance=random.choice(VILLES_MAROCAINES),
                        numero_cin=cin,
                        date_delivrance_cin=date_delivrance_cin,
                        date_expiration_cin=date_expiration_cin,
                        sexe_id=random.choice(genres).id_genre,
                        categorie_id=categorie.id_categorie,
                        groupe_sanguin_id=random.choice(groupes_sanguin).id_groupe,
                        gsm=f"06{random.randint(10000000, 99999999)}",
                        telephone_domicile=f"05{random.randint(10000000, 99999999)}" if random.choice([True, False]) else None,
                        taille=round(random.uniform(1.60, 1.90), 2),
                        lieu_residence=adresse,
                        arme_id=random.choice(armes).id_arme,
                        unite_id=unite.id_unite,
                        grade_actuel_id=random.choice(grades).id_grade,
                        fonction=random.choice(["Soldat", "Caporal", "Sergent", "Adjudant", "Chef d'équipe"]),
                        date_prise_fonction=date_engagement,
                        ccp=f"CCP{random.randint(100000, 999999)}",
                        numero_somme=f"SOMME{random.randint(1000, 9999)}",
                        date_engagement=date_engagement,
                        nom_pere=random.choice([n[1] for n in NOMS_PRENOMS_MAROCAINS]),
                        prenom_pere=random.choice([n[0] for n in NOMS_PRENOMS_MAROCAINS]),
                        nom_mere=random.choice([n[1] for n in NOMS_PRENOMS_MAROCAINS]),
                        prenom_mere=random.choice([n[0] for n in NOMS_PRENOMS_MAROCAINS]),
                        adresse_parents=f"{random.randint(1, 999)}, {random.choice(QUARTIERS_ADRESSES)}, {random.choice(VILLES_MAROCAINES)}",
                        situation_fam_id=random.choice(situations_fam).id_sitfam,
                        nombre_enfants=random.randint(0, 4),
                        gsm_urgence=f"06{random.randint(10000000, 99999999)}",
                        degre_parente_id=random.choice(degres_parente).id_degre,
                        # Passeport optionnel (50% des cas)
                        numero_passport=f"P{random.randint(1000000, 9999999)}" if random.choice([True, False]) else None,
                        date_delivrance_passport=random_date_between(date(2015, 1, 1), date(2023, 12, 31)) if random.choice([True, False]) else None,
                        date_expiration_passport=random_date_between(date(2025, 1, 1), date(2033, 12, 31)) if random.choice([True, False]) else None
                    )
                    
                    db.session.add(personnel)
                    total_created += 1
                    
                    if total_created % 20 == 0:
                        print(f"   Progression: {total_created}/200 militaires créés...")
            
            # Commit final
            db.session.commit()
            
            # Vérification finale
            count_final = Personnel.query.count()
            count_officiers = Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == "Officier").count()
            count_odr = Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == "Officier du rang").count()
            count_mdr = Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == "Militaire du rang").count()
            
            print(f"\n📊 CRÉATION TERMINÉE AVEC SUCCÈS!")
            print(f"   ✅ Total créé: {count_final} militaires")
            print(f"   👨‍✈️ Officiers: {count_officiers}")
            print(f"   🎖️ ODR: {count_odr}")
            print(f"   🪖 MDR: {count_mdr}")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de la création: {e}")
            return False

if __name__ == "__main__":
    success = create_realistic_personnel()
    if success:
        print("\n🎉 MISSION ACCOMPLIE!")
    else:
        print("\n❌ ÉCHEC DE LA MISSION")
