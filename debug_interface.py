#!/usr/bin/env python3
"""
Script de débogage pour vérifier l'interface web
"""

import requests
import json
from datetime import datetime, date

BASE_URL = "http://127.0.0.1:3000"

def debug_api_responses():
    """Déboguer les réponses de l'API"""
    print("🔍 Débogage des réponses API...")
    
    # Test API courriers arrivés
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        print(f"\n📥 API Courriers Arrivés:")
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Nombre d'éléments: {len(data)}")
            if data:
                print("Premier élément:")
                print(json.dumps(data[0], indent=2, ensure_ascii=False))
        else:
            print(f"Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test API courriers envoyés
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        print(f"\n📤 API Courriers Envoyés:")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Nombre d'éléments: {len(data)}")
            if data:
                print("Premier élément:")
                print(json.dumps(data[0], indent=2, ensure_ascii=False))
        else:
            print(f"Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_interface_pages():
    """Tester l'accessibilité des pages d'interface"""
    print("\n🌐 Test des pages d'interface...")
    
    pages = [
        "/courrier/arrives",
        "/courrier/envoyes",
        "/gestion_courrier"
    ]
    
    for page in pages:
        try:
            response = requests.get(f"{BASE_URL}{page}")
            print(f"{page}: Status {response.status_code}")
            
            if response.status_code == 200:
                # Vérifier si la page contient les éléments JavaScript nécessaires
                content = response.text
                if "loadMockData" in content:
                    print(f"  ✅ Fonction loadMockData trouvée")
                else:
                    print(f"  ❌ Fonction loadMockData manquante")
                
                if "renderTable" in content:
                    print(f"  ✅ Fonction renderTable trouvée")
                else:
                    print(f"  ❌ Fonction renderTable manquante")
                    
                if "/api/courriers" in content:
                    print(f"  ✅ Appels API trouvés")
                else:
                    print(f"  ❌ Appels API manquants")
            
        except Exception as e:
            print(f"{page}: ❌ Erreur - {e}")

def add_test_courrier_and_verify():
    """Ajouter un courrier de test et vérifier qu'il apparaît"""
    print("\n🧪 Test d'ajout et vérification...")
    
    # Ajouter un courrier de test
    test_courrier = {
        "id": f"CA-DEBUG-{int(datetime.now().timestamp())}",
        "urgence": "urgent",
        "nature": "message",
        "date_arrivee": date.today().strftime('%Y-%m-%d'),
        "date_signature": date.today().strftime('%Y-%m-%d'),
        "numero_ecrit": f"DEBUG-{int(datetime.now().timestamp())}",
        "expediteur": "Test Debug",
        "objet": "Courrier de débogage pour vérifier l'affichage",
        "classification": "public",
        "annotation": "Test de débogage",
        "divisions_action": ["technique"],
        "divisions_info": ["rh"]
    }
    
    try:
        # Ajouter le courrier
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_courrier)
        )
        
        if response.status_code == 201:
            courrier_ajoute = response.json()
            print(f"✅ Courrier ajouté: {courrier_ajoute['id']}")
            
            # Vérifier qu'il apparaît dans la liste
            response_list = requests.get(f"{BASE_URL}/api/courriers/arrives")
            if response_list.status_code == 200:
                courriers = response_list.json()
                courrier_trouve = next((c for c in courriers if c['id'] == courrier_ajoute['id']), None)
                
                if courrier_trouve:
                    print(f"✅ Courrier trouvé dans la liste API")
                    print(f"   ID: {courrier_trouve['id']}")
                    print(f"   Objet: {courrier_trouve['objet']}")
                    print(f"   Divisions action: {courrier_trouve.get('divisions_action', [])}")
                    print(f"   Divisions info: {courrier_trouve.get('divisions_info', [])}")
                else:
                    print(f"❌ Courrier non trouvé dans la liste API")
            else:
                print(f"❌ Erreur lors de la récupération de la liste")
        else:
            print(f"❌ Erreur lors de l'ajout: {response.text}")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")

def check_javascript_errors():
    """Vérifier les erreurs JavaScript potentielles"""
    print("\n🔧 Vérification des erreurs JavaScript potentielles...")
    
    # Vérifier la page des courriers arrivés
    try:
        response = requests.get(f"{BASE_URL}/courrier/arrives")
        if response.status_code == 200:
            content = response.text
            
            # Vérifier les éléments critiques
            critical_elements = [
                "courriersArrives",
                "loadMockData()",
                "renderTable()",
                "updateCounters()",
                "tbody",
                "count-total",
                "count-attente"
            ]
            
            print("Éléments critiques dans le HTML:")
            for element in critical_elements:
                if element in content:
                    print(f"  ✅ {element}")
                else:
                    print(f"  ❌ {element} manquant")
                    
            # Vérifier les appels d'API
            if "/api/courriers/arrives" in content:
                print(f"  ✅ Appel API courriers arrivés")
            else:
                print(f"  ❌ Appel API courriers arrivés manquant")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Fonction principale de débogage"""
    print("🐛 DÉBOGAGE DE L'INTERFACE DE GESTION DE COURRIER")
    print("=" * 60)
    
    debug_api_responses()
    test_interface_pages()
    add_test_courrier_and_verify()
    check_javascript_errors()
    
    print("\n" + "=" * 60)
    print("🏁 Débogage terminé")
    print("\n💡 CONSEILS:")
    print("1. Vérifiez la console JavaScript du navigateur (F12)")
    print("2. Assurez-vous que JavaScript est activé")
    print("3. Rechargez la page avec Ctrl+F5 (cache vidé)")
    print("4. Vérifiez les erreurs réseau dans l'onglet Network")

if __name__ == "__main__":
    main()
