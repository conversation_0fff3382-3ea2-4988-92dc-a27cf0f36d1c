<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}
        {% if request.path.startswith('/gestion_courrier') or request.path.startswith('/courrier') %}
            Gestion du Courrier
        {% elif request.path.startswith('/rh') %}
            Gestion RH
        {% else %}
            Gestion des Véhicules
        {% endif %}
    {% endblock %}</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/details-sidebar.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/digital-clock.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-responsive.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-mobile.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar-fix.css', v='1.0') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                {% if request.path.startswith('/rh') %}
                <i class="fas fa-user-shield me-2"></i>
                <span class="d-none d-sm-inline">Gestion RH</span>
                <span class="d-inline d-sm-none">RH</span>
                {% elif request.path.startswith('/gestion_courrier') or request.path.startswith('/courrier') %}
                <i class="fas fa-envelope me-2"></i>
                <span class="d-none d-sm-inline">Gestion Courrier</span>
                <span class="d-inline d-sm-none">GC</span>
                {% else %}
                <i class="fas fa-car-side me-2"></i>
                <span class="d-none d-sm-inline">Gestion Véhicules</span>
                <span class="d-inline d-sm-none">GV</span>
                {% endif %}
            </a>

            <!-- Horloge Numérique - Visible uniquement sur desktop -->
            <div class="digital-clock d-none d-lg-block">
                <span class="digital-clock-time" id="digitalClockTime">00:00</span>
                <span class="digital-clock-seconds" id="digitalClockSeconds">00</span>
                <span class="digital-clock-ampm" id="digitalClockAMPM">AM</span>
                <div class="digital-clock-date" id="digitalClockDate">Lundi 1 janvier</div>
            </div>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if request.path.startswith('/gestion_courrier') or request.path.startswith('/courrier') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('gestion_courrier') }}">
                            <i class="fas fa-home me-1"></i> <span>Accueil</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('courrier_arrives') }}">
                            <i class="fas fa-inbox me-1"></i> <span>Courriers Arrivés</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('courrier_envoyes') }}">
                            <i class="fas fa-paper-plane me-1"></i> <span>Courriers Envoyés</span>
                        </a>
                    </li>
                    {% elif not request.path.startswith('/rh') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('liste_vehicules') }}">
                            <i class="fas fa-home me-1"></i> <span>Accueil</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-chart-line me-1"></i> <span>Tableau de bord</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('historique') }}">
                            <i class="fas fa-history me-1"></i> <span>Historique</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('entretiens') }}">
                            <i class="fas fa-tools me-1"></i> <span>Entretiens</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('chatbot') }}">
                            <i class="fas fa-robot me-1"></i> <span>Assistant IA</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('courrier_vehicules') }}">
                            <i class="fas fa-envelope me-1"></i> <span>Gestion du Courrier</span>
                        </a>
                    </li>

                    {% endif %}
                    {% if request.path.startswith('/rh') %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('chatbot') }}">
                            <i class="fas fa-robot me-1"></i> <span>Assistant IA</span>
                        </a>
                    </li>
                    {% endif %}
                    {% if session.logged_in %}
                    <li class="nav-item">
                        <span class="nav-link">
                            <i class="fas fa-user me-1"></i> <span>{{ session.username }}</span>
                        </span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="{{ url_for('index') }}">
                            <i class="fas fa-sign-out-alt me-1"></i> <span>Déconnexion</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-3 mt-md-4">
        <div class="row">
            <div class="col-12">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>



    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/digital-clock.js', v='1.0') }}"></script>
    <script>
        // Animation des éléments au chargement
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.card, .stat-card, .chart-container');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                setTimeout(() => {
                    element.classList.add('fade-in');
                }, index * 100);
            });
        });

        // Fermeture automatique des alertes après 5 secondes
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                const closeButton = alert.querySelector('.btn-close');
                if (closeButton) {
                    closeButton.click();
                }
            }, 5000);
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>