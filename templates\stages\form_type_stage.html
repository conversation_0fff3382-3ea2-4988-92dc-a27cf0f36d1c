{% extends "stages/base_stages.html" %}

{% block title %}{{ action }} un Type de Stage{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{{ action }} un Type de Stage</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <label for="nom" class="form-label">Nom du Type de Stage</label>
                    <input type="text" class="form-control" id="nom" name="nom" value="{{ type_stage.nom if type_stage else '' }}" required>
                </div>
                <div class="mb-3">
                    <label for="conditions_admission" class="form-label">Conditions d'Admission</label>
                    <textarea class="form-control" id="conditions_admission" name="conditions_admission" rows="5">{{ type_stage.conditions_admission if type_stage else '' }}</textarea>
                </div>
                
                <hr>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_types_stage') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {{ action }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 