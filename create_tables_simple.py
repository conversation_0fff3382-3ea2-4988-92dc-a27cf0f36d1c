#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour créer les tables RH une par une
"""

import mysql.connector
from mysql.connector import Error

def create_tables():
    """Crée les tables RH une par une"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("Connexion à la base de données réussie")
        
        # Désactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0;")
        
        # 1. Tables de référence
        tables = [
            ("referentiel_genre", """
                CREATE TABLE referentiel_genre (
                    id_genre INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(20) NOT NULL UNIQUE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_groupe_sanguin", """
                CREATE TABLE referentiel_groupe_sanguin (
                    id_groupe INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(3) NOT NULL UNIQUE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_categorie", """
                CREATE TABLE referentiel_categorie (
                    id_categorie INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_service", """
                CREATE TABLE referentiel_service (
                    id_service INT PRIMARY KEY AUTO_INCREMENT,
                    code_court VARCHAR(10) NOT NULL UNIQUE,
                    libelle VARCHAR(100) NOT NULL,
                    description TEXT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_specialite", """
                CREATE TABLE referentiel_specialite (
                    id_specialite INT PRIMARY KEY AUTO_INCREMENT,
                    service_id INT NOT NULL,
                    code VARCHAR(20) NOT NULL,
                    libelle VARCHAR(100) NOT NULL,
                    FOREIGN KEY (service_id) REFERENCES referentiel_service(id_service) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_unite", """
                CREATE TABLE referentiel_unite (
                    id_unite INT PRIMARY KEY AUTO_INCREMENT,
                    code VARCHAR(20) NOT NULL UNIQUE,
                    libelle VARCHAR(150) NOT NULL,
                    type_unite ENUM('Régiment', 'Inspection', 'Bureau', 'Autre') NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_grade", """
                CREATE TABLE referentiel_grade (
                    id_grade INT PRIMARY KEY AUTO_INCREMENT,
                    code_grade VARCHAR(20) NOT NULL UNIQUE,
                    libelle VARCHAR(50) NOT NULL,
                    niveau INT NOT NULL,
                    description TEXT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_etat_matrimonial", """
                CREATE TABLE referentiel_etat_matrimonial (
                    id_etat INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(20) NOT NULL UNIQUE,
                    description TEXT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_langue", """
                CREATE TABLE referentiel_langue (
                    id_langue INT PRIMARY KEY AUTO_INCREMENT,
                    code_iso CHAR(2) NOT NULL UNIQUE,
                    libelle VARCHAR(50) NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_lien_parente", """
                CREATE TABLE referentiel_lien_parente (
                    id_lien INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(50) NOT NULL UNIQUE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """),
            
            ("referentiel_type_absence", """
                CREATE TABLE referentiel_type_absence (
                    id_type INT PRIMARY KEY AUTO_INCREMENT,
                    libelle VARCHAR(50) NOT NULL UNIQUE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """)
        ]
        
        # Créer les tables de référence
        for i, (table_name, sql) in enumerate(tables, 1):
            try:
                cursor.execute(sql)
                print(f"✓ Table {i}/25 créée : {table_name}")
            except Error as e:
                print(f"✗ Erreur table {table_name}: {e}")
        
        # Valider les changements
        connection.commit()
        print("✓ Tables de référence créées avec succès")
        
        # Réactiver les contraintes
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1;")
        
    except Error as e:
        print(f"Erreur de connexion : {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion fermée")
    
    return True

if __name__ == "__main__":
    print("=== CRÉATION DES TABLES RH (PARTIE 1/3) ===")
    print("Création des 11 tables de référence")
    print("-" * 50)
    
    success = create_tables()
    
    if success:
        print("\n🎉 Partie 1 terminée avec succès !")
        print("Tables de référence créées.")
    else:
        print("\n❌ Échec de la partie 1")
