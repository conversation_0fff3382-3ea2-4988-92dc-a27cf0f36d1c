{% extends "stages/base_stages.html" %}

{% block title %}Types de Stage{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Gestion des Types de Stage</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('ajouter_type_stage') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-plus-circle me-1"></i>
                Ajouter un Type
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Nom du Type de Stage</th>
                            <th scope="col">Conditions d'Admission</th>
                            <th scope="col" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in types %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>{{ type.libelle }}</td>
                            <td>{{ type.conditions_admission }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('modifier_type_stage', id=type.id_type_stage) }}" class="btn btn-warning btn-sm" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <!-- Le bouton de suppression peut nécessiter un formulaire ou un modal pour la sécurité -->
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="4" class="text-center">Aucun type de stage trouvé.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 