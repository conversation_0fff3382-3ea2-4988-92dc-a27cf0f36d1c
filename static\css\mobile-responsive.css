/* Mobile Responsive CSS - Améliorations pour téléphone portable */

/* Variables pour mobile */
:root {
    --mobile-padding: 0.5rem;
    --mobile-margin: 0.5rem;
    --mobile-font-size: 0.9rem;
    --mobile-header-height: 60px;
}

/* Masquer le bouton hamburger sur desktop */
@media (min-width: 992px) {
    .navbar-toggler {
        display: none !important;
    }
}

/* Règles générales pour mobile */
@media (max-width: 768px) {
    /* Body et container */
    body {
        font-size: var(--mobile-font-size);
        line-height: 1.4;
    }
    
    .container {
        padding: var(--mobile-padding);
        margin: var(--mobile-margin);
        max-width: 100%;
        border-radius: 8px;
    }
    
    /* Navigation mobile */
    .navbar {
        padding: 0.5rem 1rem;
        min-height: var(--mobile-header-height);
    }
    
    /* S'assurer que le bouton hamburger est toujours visible sur mobile */
    .navbar-toggler {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
        margin-right: 0;
    }
    
    .navbar-toggler {
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 0.25rem 0.5rem;
        background-color: transparent;
        color: white;
    }
    
    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
        outline: none;
    }
    
    .navbar-toggler:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.5);
    }
    
    /* S'assurer que l'icône hamburger est visible */
    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100%;
        width: 1.5em;
        height: 1.5em;
        display: inline-block;
    }
    
    .navbar-collapse {
        background: rgba(75, 83, 32, 0.95);
        border-radius: 8px;
        margin-top: 0.5rem;
        padding: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .nav-item {
        margin: 0.5rem 0;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        border-radius: 5px;
        margin: 0.25rem 0;
    }
    
    /* Horloge numérique mobile */
    .digital-clock {
        display: none; /* Masquer sur mobile pour économiser l'espace */
    }
    
    /* Cartes et conteneurs */
    .card {
        margin-bottom: 1rem;
        border-radius: 8px;
    }
    
    .card-header {
        padding: 0.75rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Statistiques */
    .stat-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
        margin: 0.5rem 0;
    }
    
    /* Tableaux responsifs */
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }
    
    /* Boutons mobile */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        margin: 0.25rem;
    }
    
    .btn-group .btn {
        margin: 0;
    }
    
    /* Formulaires mobile */
    .form-control {
        font-size: 1rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }
    
    /* Alertes mobile */
    .alert {
        padding: 0.75rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    /* Graphiques mobile */
    .chart-container {
        padding: 1rem;
        margin-bottom: 1rem;
        height: auto;
        min-height: 250px;
    }
    
    /* Footer mobile */
    .footer {
        padding: 1rem 0;
        font-size: 0.8rem;
    }
    
    /* Grilles responsives */
    .row {
        margin: 0;
    }
    
    .col-md-6,
    .col-lg-4,
    .col-xl-3 {
        padding: 0.25rem;
    }
    
    /* Espacement mobile */
    .mt-4 {
        margin-top: 1rem !important;
    }
    
    .mb-4 {
        margin-bottom: 1rem !important;
    }
    
    .py-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
}

/* Règles pour très petits écrans */
@media (max-width: 576px) {
    .container {
        padding: 0.25rem;
        margin: 0.25rem;
    }
    
    .navbar {
        padding: 0.25rem 0.5rem;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
    
    .card {
        margin-bottom: 0.5rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.25rem 0.1rem;
    }
    
    /* Masquer certaines colonnes sur très petits écrans */
    .table-responsive .table th:nth-child(4),
    .table-responsive .table td:nth-child(4),
    .table-responsive .table th:nth-child(5),
    .table-responsive .table td:nth-child(5) {
        display: none;
    }
}

/* Règles pour les écrans en mode paysage sur mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .navbar {
        min-height: 50px;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .container {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 0.75rem;
    }
}

/* Améliorations pour les tableaux de données */
@media (max-width: 768px) {
    .vehicle-card {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .vehicle-card strong {
        display: block;
        margin-top: 0.5rem;
        color: var(--primary-color);
    }
    
    /* Chatbot mobile */
    .chat-container {
        height: 60vh;
        max-height: 400px;
    }
    
    .chat-messages {
        height: calc(100% - 120px);
        overflow-y: auto;
    }
    
    .chat-input {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        border-top: 1px solid #ddd;
        z-index: 1000;
    }
    
    /* Courrier mobile */
    .search-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .search-section .row {
        margin: 0;
    }
    
    .search-section .col-md-4 {
        margin-bottom: 0.5rem;
    }
    
    /* Dashboard mobile */
    .dashboard-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-stats .stat-card {
        margin: 0;
    }
}

/* Améliorations pour les formulaires */
@media (max-width: 768px) {
    .form-row {
        margin: 0;
    }
    
    .form-row .col-md-6,
    .form-row .col-lg-4 {
        padding: 0.25rem;
    }
    
    .form-check {
        margin-bottom: 0.5rem;
    }
    
    .form-check-input {
        margin-right: 0.5rem;
    }
}

/* Améliorations pour les modales */
@media (max-width: 768px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-content {
        border-radius: 8px;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
    }
}

/* Améliorations pour les badges et étiquettes */
@media (max-width: 768px) {
    .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Améliorations pour les listes */
@media (max-width: 768px) {
    .list-group-item {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .list-group-item-action:hover {
        background-color: rgba(75, 83, 32, 0.1);
    }
}

/* Améliorations pour les tooltips et popovers */
@media (max-width: 768px) {
    .tooltip {
        font-size: 0.8rem;
    }
    
    .popover {
        max-width: 280px;
    }
}

/* Améliorations pour les paginations */
@media (max-width: 768px) {
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .page-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* Améliorations pour les breadcrumbs */
@media (max-width: 768px) {
    .breadcrumb {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
        padding: 0 0.25rem;
    }
}

/* Améliorations pour les progress bars */
@media (max-width: 768px) {
    .progress {
        height: 8px;
        margin: 0.5rem 0;
    }
    
    .progress-bar {
        font-size: 0.7rem;
        line-height: 8px;
    }
}

/* Améliorations pour les spinners */
@media (max-width: 768px) {
    .spinner-border {
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }
}

/* Améliorations pour les images */
@media (max-width: 768px) {
    .img-fluid {
        border-radius: 8px;
    }
    
    .rounded {
        border-radius: 8px !important;
    }
}

/* Améliorations pour les vidéos */
@media (max-width: 768px) {
    .embed-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
}

/* Améliorations pour les carousels */
@media (max-width: 768px) {
    .carousel {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .carousel-item {
        height: 200px;
    }
    
    .carousel-caption {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

/* Améliorations pour les accordéons */
@media (max-width: 768px) {
    .accordion-item {
        margin-bottom: 0.5rem;
        border-radius: 8px;
    }
    
    .accordion-button {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .accordion-body {
        padding: 0.75rem;
    }
}

/* Améliorations pour les onglets */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        margin: 0.1rem;
    }
    
    .tab-content {
        padding: 0.75rem;
    }
}

/* Améliorations pour les notifications */
@media (max-width: 768px) {
    .toast {
        margin: 0.5rem;
        border-radius: 8px;
    }
    
    .toast-header {
        padding: 0.5rem;
        font-size: 0.85rem;
    }
    
    .toast-body {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}

/* Améliorations pour les dropdowns */
@media (max-width: 768px) {
    .dropdown-menu {
        border-radius: 8px;
        margin-top: 0.25rem;
    }
    
    .dropdown-item {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Améliorations pour les inputs group */
@media (max-width: 768px) {
    .input-group {
        margin-bottom: 0.5rem;
    }
    
    .input-group-text {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .input-group .form-control {
        margin-bottom: 0;
    }
}

/* Améliorations pour les validations */
@media (max-width: 768px) {
    .valid-feedback,
    .invalid-feedback {
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }
}

/* Améliorations pour les utilitaires */
@media (max-width: 768px) {
    .text-center {
        text-align: center !important;
    }
    
    .text-start {
        text-align: left !important;
    }
    
    .text-end {
        text-align: right !important;
    }
    
    .d-none {
        display: none !important;
    }
    
    .d-block {
        display: block !important;
    }
    
    .d-flex {
        display: flex !important;
    }
    
    .justify-content-center {
        justify-content: center !important;
    }
    
    .align-items-center {
        align-items: center !important;
    }
} 