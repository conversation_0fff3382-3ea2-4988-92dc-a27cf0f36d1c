{% extends "base.html" %}

{% block title %}Assistant IA{% endblock %}

{% block extra_css %}
<style>
#ai-agent-container {
    position: relative;
    width: 300px;
    height: 400px;
    margin-bottom: 20px;
    background-color: transparent;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 200px);
    min-height: 500px;
    margin: 20px 0;
}

.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0; /* Empêche le débordement */
}

.agent-section {
    width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    border-left: 2px solid rgba(107, 142, 35, 0.1);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: rgba(248, 249, 250, 0.95);
    border-radius: 15px;
    padding: 20px;
    height: 100%;
    min-height: 0; /* Important pour le scroll */
    position: relative; /* Pour le positionnement absolu de chat-input */
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 80px; /* Espace pour la zone de saisie */
    height: 100%;
    min-height: 0; /* Important pour le scroll */
    scroll-behavior: smooth; /* Pour un défilement fluide */
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.message {
    margin-bottom: 1rem;
    max-width: 80%;
    animation: fadeIn 0.3s ease;
    width: 100%; /* Pour que les messages prennent toute la largeur disponible */
    align-self: flex-start; /* Pour aligner les messages au début */
}

.message.user {
    align-self: flex-end; /* Pour aligner les messages utilisateur à droite */
}

.message.bot {
    align-self: flex-start; /* Pour aligner les messages bot à gauche */
}

.message-content {
    padding: 1rem;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message.user .message-content {
    background: var(--secondary-color);
    color: white;
    border-radius: 15px 15px 0 15px;
}

.message.bot .message-content {
    background: white;
    border-radius: 15px 15px 15px 0;
}

.chat-input {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 10;
}

.chat-input input {
    flex: 1;
    min-width: 0; /* Empêche le débordement */
    border: 2px solid #ddd;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.chat-input input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(41, 128, 185, 0.1);
    outline: none;
}

.chat-input button {
    min-width: 50px;
    height: 50px;
    border-radius: 25px;
    background: var(--secondary-color);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    padding: 0;
}

.chat-input button:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(41, 128, 185, 0.3);
}

.chat-input button i {
    font-size: 1.2rem;
}

.suggestion-chips {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.suggestion-chip {
    padding: 0.5rem 1rem;
    background: white;
    border: 2px solid var(--secondary-color);
    border-radius: 20px;
    color: var(--secondary-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.suggestion-chip:hover {
    background: var(--secondary-color);
    color: white;
}

.typing-indicator {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border-radius: 15px;
    width: fit-content;
    margin-bottom: 1rem;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: var(--secondary-color);
    border-radius: 50%;
    animation: typing 1s infinite ease-in-out;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.vehicle-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Styles spécifiques pour mobile */
@media (max-width: 768px) {
    .chat-layout {
        flex-direction: column;
        height: auto;
        min-height: 400px;
        gap: 10px;
    }
    
    .agent-section {
        width: 100%;
        order: 2;
        padding: 10px;
        border-left: none;
        border-top: 2px solid rgba(107, 142, 35, 0.1);
    }
    
    #ai-agent-container {
        width: 150px;
        height: 200px;
        margin: 0 auto 10px auto;
    }
    
    .chat-section {
        order: 1;
        flex: 1;
        min-height: 300px;
    }
    
    .chat-container {
        min-height: 300px;
        padding: 15px;
    }
    
    .chat-messages {
        margin-bottom: 70px;
        min-height: 200px;
    }
    
    .suggestion-chips {
        margin-bottom: 0.5rem;
    }
    
    .suggestion-chip {
        padding: 0.3rem 0.7rem;
        font-size: 0.8rem;
    }
    
    .chat-input {
        bottom: 15px;
        left: 15px;
        right: 15px;
        padding: 0.8rem;
    }
    
    .chat-input input {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .chat-input button {
        min-width: 40px;
        height: 40px;
    }
    
    .chat-input button i {
        font-size: 1rem;
    }
    
    .message-content {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
}

/* Styles pour très petits écrans */
@media (max-width: 480px) {
    #ai-agent-container {
        width: 120px;
        height: 160px;
    }
    
    .chat-container {
        padding: 10px;
    }
    
    .chat-messages {
        margin-bottom: 60px;
        min-height: 180px;
    }
    
    .suggestion-chips {
        gap: 0.3rem;
    }
    
    .suggestion-chip {
        padding: 0.25rem 0.6rem;
        font-size: 0.75rem;
    }
    
    .chat-input {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 0.6rem;
    }
    
    .chat-input input {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .chat-input button {
        min-width: 35px;
        height: 35px;
    }
    
    .chat-input button i {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="card">
        <div class="card-header py-2" style="min-height: 60px; overflow: hidden;">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-robot me-2"></i>Assistant IA
                </h4>
                <div class="d-flex align-items-center gap-2 mx-2" style="height: 60px;">
                    <img src="{{ url_for('static', filename='images/atlas.png') }}" alt="Atlas" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                    <img src="{{ url_for('static', filename='images/m109.png') }}" alt="M109" style="height: 90px; width: auto; object-fit: contain; transform: scale(0.95);">
                    <img src="{{ url_for('static', filename='images/vvv.png') }}" alt="Véhicule" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                </div>
                <div style="width: 150px;"></div> <!-- Élément vide pour équilibrer la mise en page -->
            </div>
        </div>
        <div class="card-body p-4">
            <div class="chat-layout">
                <div class="chat-section">
                    <div class="chat-container">
                        <div class="suggestion-chips">
                            <div class="suggestion-chip" onclick="sendMessage('Liste tous les véhicules')">
                                <i class="fas fa-list me-1"></i>Liste des véhicules
                            </div>
                            <div class="suggestion-chip" onclick="sendMessage('Combien y a-t-il de véhicules en panne ?')">
                                <i class="fas fa-wrench me-1"></i>Véhicules en panne
                            </div>
                            <div class="suggestion-chip" onclick="sendMessage('Quelles sont les marques les plus courantes ?')">
                                <i class="fas fa-chart-pie me-1"></i>Marques populaires
                            </div>
                        </div>

                        <div class="chat-messages" id="chatMessages">
                            <div class="message bot">
                                <div class="message-content">
                                    Bonjour ! Je suis votre assistant IA pour la gestion des véhicules.
                                    Comment puis-je vous aider aujourd'hui ?
                                </div>
                            </div>
                        </div>

                        <div class="chat-input">
                            <input type="text" id="userInput" placeholder="Posez votre question..."
                                   onkeypress="handleKeyPress(event)">
                            <button onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="agent-section">
                    <div id="ai-agent-container"></div>
                    <div class="text-center mt-3">
                        <p class="text-muted small">Assistant IA Militaire</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>

<script>
let scene, camera, renderer, robot, particles, clock, mixer;
const ROTATION_SPEED = 0.008;

function initScene() {
    // Initialisation de l'horloge pour les animations
    clock = new THREE.Clock();

    // Création de la scène avec un léger brouillard pour la profondeur
    scene = new THREE.Scene();
    scene.background = null;
    scene.fog = new THREE.FogExp2(0x000000, 0.03);

    // Configuration de la caméra avec un angle plus dynamique
    camera = new THREE.PerspectiveCamera(45, 300/400, 0.1, 1000);
    camera.position.set(0.5, 0.8, 5.5);
    camera.lookAt(0, 0.5, 0);

    // Configuration du renderer avec des ombres et antialiasing amélioré
    const container = document.getElementById('ai-agent-container');
    renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        preserveDrawingBuffer: false
    });
    
    // Adapter la taille selon l'écran
    const isMobile = window.innerWidth <= 768;
    const containerWidth = isMobile ? 150 : 300;
    const containerHeight = isMobile ? 200 : 400;
    
    renderer.setSize(containerWidth, containerHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputEncoding = THREE.sRGBEncoding;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;

    container.appendChild(renderer.domElement);

    // Système d'éclairage avancé
    // Lumière ambiante douce
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // Lumière principale avec ombres
    const mainLight = new THREE.DirectionalLight(0xffffff, 1.2);
    mainLight.position.set(2, 4, 3);
    mainLight.castShadow = true;
    mainLight.shadow.mapSize.width = 1024;
    mainLight.shadow.mapSize.height = 1024;
    mainLight.shadow.camera.near = 0.5;
    mainLight.shadow.camera.far = 20;
    mainLight.shadow.bias = -0.001;
    scene.add(mainLight);

    // Lumière d'accentuation bleue pour effet high-tech
    const blueRimLight = new THREE.PointLight(0x3677e8, 0.8, 10);
    blueRimLight.position.set(-3, 2, -2);
    scene.add(blueRimLight);

    // Lumière d'accentuation verte pour les détails militaires
    const greenAccentLight = new THREE.PointLight(0x4caf50, 0.6, 8);
    greenAccentLight.position.set(3, 1, -1);
    scene.add(greenAccentLight);

    // Création du robot avancé
    robot = new THREE.Group();

    // ===== CORPS PRINCIPAL =====
    // Base du corps avec texture métallique
    const bodyGeometry = new THREE.CylinderGeometry(0.5, 0.7, 1.8, 12);
    const bodyMaterial = new THREE.MeshStandardMaterial({
        color: 0x4B5320, // Vert militaire
        metalness: 0.7,
        roughness: 0.3,
        envMapIntensity: 1.0
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.castShadow = true;
    body.receiveShadow = true;
    robot.add(body);

    // Détails du corps - plaques d'armure
    const armorPlateGeometry = new THREE.BoxGeometry(0.8, 0.3, 0.6);
    const armorPlateMaterial = new THREE.MeshStandardMaterial({
        color: 0x5D6D3B,
        metalness: 0.8,
        roughness: 0.2
    });

    // Plaque frontale
    const frontPlate = new THREE.Mesh(armorPlateGeometry, armorPlateMaterial);
    frontPlate.position.set(0, 0.2, 0.4);
    frontPlate.castShadow = true;
    body.add(frontPlate);

    // Plaque arrière
    const backPlate = new THREE.Mesh(armorPlateGeometry, armorPlateMaterial);
    backPlate.position.set(0, 0.2, -0.4);
    backPlate.castShadow = true;
    body.add(backPlate);

    // Ceinture de munitions
    const beltGeometry = new THREE.TorusGeometry(0.7, 0.08, 8, 24);
    const beltMaterial = new THREE.MeshStandardMaterial({
        color: 0x8B4513,
        roughness: 0.8,
        metalness: 0.2
    });
    const belt = new THREE.Mesh(beltGeometry, beltMaterial);
    belt.position.y = -0.2;
    belt.rotation.x = Math.PI / 2;
    belt.castShadow = true;
    body.add(belt);

    // ===== TÊTE AVANCÉE =====
    // Base de la tête avec visière
    const headGroup = new THREE.Group();
    headGroup.position.y = 1.2;

    const headGeometry = new THREE.SphereGeometry(0.5, 24, 24, 0, Math.PI * 2, 0, Math.PI * 0.8);
    const headMaterial = new THREE.MeshStandardMaterial({
        color: 0x6B8E23, // Vert olive
        metalness: 0.6,
        roughness: 0.4
    });
    const headMesh = new THREE.Mesh(headGeometry, headMaterial);
    headMesh.castShadow = true;
    headMesh.receiveShadow = true;
    headGroup.add(headMesh);

    // Visière avec effet réfléchissant
    const visierGeometry = new THREE.SphereGeometry(0.51, 24, 12, 0, Math.PI, Math.PI * 0.4, Math.PI * 0.3);
    const visierMaterial = new THREE.MeshPhysicalMaterial({
        color: 0x111111,
        metalness: 1.0,
        roughness: 0.1,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1,
        envMapIntensity: 1.5
    });
    const visier = new THREE.Mesh(visierGeometry, visierMaterial);
    visier.rotation.x = Math.PI * 0.1;
    visier.position.z = 0.05;
    visier.castShadow = true;
    headGroup.add(visier);

    robot.add(headGroup);

    // ===== YEUX AVANCÉS =====
    // Yeux avec effet de lueur
    const eyeGeometry = new THREE.SphereGeometry(0.08, 16, 16);
    const eyeMaterial = new THREE.MeshStandardMaterial({
        color: 0x00FF00,
        emissive: 0x00FF00,
        emissiveIntensity: 1.0,
        metalness: 0.9,
        roughness: 0.1
    });

    // Ajout des yeux avec positionnement précis
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.22, 1.35, 0.38);
    leftEye.castShadow = true;

    // Ajout d'une lueur autour des yeux
    const leftEyeGlow = new THREE.PointLight(0x00ff00, 0.6, 0.5);
    leftEyeGlow.position.copy(leftEye.position);
    leftEye.add(leftEyeGlow);
    robot.add(leftEye);

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.22, 1.35, 0.38);
    rightEye.castShadow = true;

    const rightEyeGlow = new THREE.PointLight(0x00ff00, 0.6, 0.5);
    rightEyeGlow.position.copy(rightEye.position);
    rightEye.add(rightEyeGlow);
    robot.add(rightEye);

    // ===== ANTENNES AVANCÉES =====
    // Base des antennes
    const antennaBaseGeometry = new THREE.CylinderGeometry(0.06, 0.06, 0.05, 8);
    const antennaBaseMaterial = new THREE.MeshStandardMaterial({
        color: 0x2F4F4F,
        metalness: 0.8,
        roughness: 0.2
    });

    // Tige des antennes
    const antennaRodGeometry = new THREE.CylinderGeometry(0.02, 0.01, 0.4, 8);
    const antennaRodMaterial = new THREE.MeshStandardMaterial({
        color: 0x2F4F4F,
        metalness: 0.9,
        roughness: 0.1
    });

    // Pointe des antennes avec effet lumineux
    const antennaTipGeometry = new THREE.SphereGeometry(0.03, 8, 8);
    const antennaTipMaterial = new THREE.MeshStandardMaterial({
        color: 0xff3333,
        emissive: 0xff0000,
        emissiveIntensity: 1.0
    });

    // Antenne gauche
    const leftAntennaGroup = new THREE.Group();
    leftAntennaGroup.position.set(-0.3, 1.8, 0);

    const leftAntennaBase = new THREE.Mesh(antennaBaseGeometry, antennaBaseMaterial);
    leftAntennaGroup.add(leftAntennaBase);

    const leftAntennaRod = new THREE.Mesh(antennaRodGeometry, antennaRodMaterial);
    leftAntennaRod.position.y = 0.225;
    leftAntennaRod.rotation.x = Math.PI / 6;
    leftAntennaGroup.add(leftAntennaRod);

    const leftAntennaTip = new THREE.Mesh(antennaTipGeometry, antennaTipMaterial);
    leftAntennaTip.position.set(0, 0.42, 0.07);
    leftAntennaGroup.add(leftAntennaTip);

    // Lumière clignotante pour la pointe de l'antenne
    const leftAntennaTipLight = new THREE.PointLight(0xff0000, 0.8, 0.3);
    leftAntennaTipLight.position.copy(leftAntennaTip.position);
    leftAntennaGroup.add(leftAntennaTipLight);

    robot.add(leftAntennaGroup);

    // Antenne droite
    const rightAntennaGroup = new THREE.Group();
    rightAntennaGroup.position.set(0.3, 1.8, 0);

    const rightAntennaBase = new THREE.Mesh(antennaBaseGeometry, antennaBaseMaterial);
    rightAntennaGroup.add(rightAntennaBase);

    const rightAntennaRod = new THREE.Mesh(antennaRodGeometry, antennaRodMaterial);
    rightAntennaRod.position.y = 0.225;
    rightAntennaRod.rotation.x = -Math.PI / 6;
    rightAntennaGroup.add(rightAntennaRod);

    const rightAntennaTip = new THREE.Mesh(antennaTipGeometry, antennaTipMaterial);
    rightAntennaTip.position.set(0, 0.42, -0.07);
    rightAntennaGroup.add(rightAntennaTip);

    // Lumière clignotante pour la pointe de l'antenne
    const rightAntennaTipLight = new THREE.PointLight(0xff0000, 0.8, 0.3);
    rightAntennaTipLight.position.copy(rightAntennaTip.position);
    rightAntennaGroup.add(rightAntennaTipLight);

    robot.add(rightAntennaGroup);

    // ===== ÉPAULES ET BRAS =====
    // Épaules avec détails mécaniques
    const shoulderGeometry = new THREE.BoxGeometry(1.8, 0.3, 0.4);
    const shoulderMaterial = new THREE.MeshStandardMaterial({
        color: 0x4B5320,
        metalness: 0.7,
        roughness: 0.3
    });
    const shoulders = new THREE.Mesh(shoulderGeometry, shoulderMaterial);
    shoulders.position.y = 0.5;
    shoulders.castShadow = true;
    shoulders.receiveShadow = true;
    robot.add(shoulders);

    // Détails des épaules - joints mécaniques
    const shoulderJointGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.2, 16);
    const shoulderJointMaterial = new THREE.MeshStandardMaterial({
        color: 0x333333,
        metalness: 0.9,
        roughness: 0.2
    });

    // Joint gauche
    const leftShoulderJoint = new THREE.Mesh(shoulderJointGeometry, shoulderJointMaterial);
    leftShoulderJoint.rotation.z = Math.PI / 2;
    leftShoulderJoint.position.set(-0.9, 0.5, 0);
    leftShoulderJoint.castShadow = true;
    robot.add(leftShoulderJoint);

    // Joint droit
    const rightShoulderJoint = new THREE.Mesh(shoulderJointGeometry, shoulderJointMaterial);
    rightShoulderJoint.rotation.z = Math.PI / 2;
    rightShoulderJoint.position.set(0.9, 0.5, 0);
    rightShoulderJoint.castShadow = true;
    robot.add(rightShoulderJoint);

    // Bras supérieurs
    const upperArmGeometry = new THREE.CylinderGeometry(0.12, 0.1, 0.6, 8);
    const armMaterial = new THREE.MeshStandardMaterial({
        color: 0x5D6D3B,
        metalness: 0.6,
        roughness: 0.4
    });

    // Bras gauche
    const leftUpperArm = new THREE.Mesh(upperArmGeometry, armMaterial);
    leftUpperArm.position.set(-0.9, 0.15, 0);
    leftUpperArm.castShadow = true;
    robot.add(leftUpperArm);

    // Bras droit
    const rightUpperArm = new THREE.Mesh(upperArmGeometry, armMaterial);
    rightUpperArm.position.set(0.9, 0.15, 0);
    rightUpperArm.castShadow = true;
    robot.add(rightUpperArm);

    // ===== LOGOS FAR SUR LE CORPS =====
    // Approche radicalement différente avec des sprites
    // Les sprites sont toujours orientés vers la caméra, ce qui garantit une visibilité optimale

    // Chargeur de textures
    const textureLoader = new THREE.TextureLoader();

    // Fonction pour créer une texture de secours si nécessaire
    const createFallbackTexture = () => {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Fond transparent
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Dessiner un cercle turquoise (comme dans l'image)
        ctx.beginPath();
        ctx.arc(128, 128, 80, 0, Math.PI * 2);
        ctx.fillStyle = '#00CED1'; // Turquoise
        ctx.fill();

        // Dessiner un contour
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 10;
        ctx.stroke();

        // Ajouter du texte "FAR"
        ctx.font = 'bold 60px Arial';
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('FAR', 128, 128);

        return new THREE.CanvasTexture(canvas);
    };

    // Chargement direct de la texture avec gestion d'erreur
    textureLoader.load(
        '{{ url_for("static", filename="images/far.png") }}',
        function(texture) {
            // Paramètres optimaux pour la texture
            texture.minFilter = THREE.LinearFilter;
            texture.magFilter = THREE.LinearFilter;
            texture.anisotropy = 16;

            // Paramètres originaux de la texture
            texture.offset.set(0, 0);
            texture.repeat.set(1, 1);
            texture.center.set(0.5, 0.5);

        // Création d'un groupe pour le logo avant - permet de l'attacher solidement au robot
        const frontLogoGroup = new THREE.Group();
        robot.add(frontLogoGroup);

        // Création d'un cadre circulaire blanc extérieur pour le logo avant
        const frontOuterFrameGeometry = new THREE.CircleGeometry(0.25, 32); // Diamètre 0.5 unités
        const frontOuterFrameMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFFFFF, // Blanc
            metalness: 0.5,
            roughness: 0.2,
            side: THREE.DoubleSide
        });
        const frontOuterFrame = new THREE.Mesh(frontOuterFrameGeometry, frontOuterFrameMaterial);
        frontOuterFrame.position.set(0, 0.6, 0.58);
        frontOuterFrame.rotation.x = -Math.PI * 0.1;
        frontLogoGroup.add(frontOuterFrame);

        // Création d'un cadre circulaire gris foncé intérieur pour le logo avant
        const frontInnerFrameGeometry = new THREE.CircleGeometry(0.23, 32); // Diamètre 0.46 unités
        const frontInnerFrameMaterial = new THREE.MeshStandardMaterial({
            color: 0x333333, // Gris foncé
            metalness: 0.6,
            roughness: 0.3,
            side: THREE.DoubleSide
        });
        const frontInnerFrame = new THREE.Mesh(frontInnerFrameGeometry, frontInnerFrameMaterial);
        frontInnerFrame.position.set(0, 0.6, 0.581); // Légèrement devant le cadre extérieur
        frontInnerFrame.rotation.x = -Math.PI * 0.1;
        frontLogoGroup.add(frontInnerFrame);

        // Création d'un cercle pour le logo avant
        const frontLogoGeometry = new THREE.CircleGeometry(0.22, 32); // Diamètre 0.44 unités
        const frontLogoMaterial = new THREE.MeshBasicMaterial({
            map: texture,
            color: 0xFFFFFF,
            transparent: true,
            alphaTest: 0.0, // Désactivé pour éviter de couper des parties de l'image
            side: THREE.FrontSide, // Visible uniquement de face
            depthTest: true,
            depthWrite: true,
            blending: THREE.NormalBlending
        });

        const frontLogo = new THREE.Mesh(frontLogoGeometry, frontLogoMaterial);
        frontLogo.position.set(0, 0.6, 0.582); // Légèrement devant le cadre intérieur
        frontLogo.rotation.x = -Math.PI * 0.1;
        frontLogoGroup.add(frontLogo);

        // Création d'un groupe pour le logo arrière
        const backLogoGroup = new THREE.Group();
        robot.add(backLogoGroup);

        // Création d'un cadre circulaire blanc extérieur pour le logo arrière
        const backOuterFrameGeometry = new THREE.CircleGeometry(0.25, 32); // Diamètre 0.5 unités
        const backOuterFrameMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFFFFF, // Blanc
            metalness: 0.5,
            roughness: 0.2,
            side: THREE.DoubleSide
        });
        const backOuterFrame = new THREE.Mesh(backOuterFrameGeometry, backOuterFrameMaterial);
        backOuterFrame.position.set(0, 0.6, -0.58);
        backOuterFrame.rotation.y = Math.PI; // Rotation pour faire face à l'arrière
        backOuterFrame.rotation.x = Math.PI * 0.1;
        backLogoGroup.add(backOuterFrame);

        // Création d'un cadre circulaire gris foncé intérieur pour le logo arrière
        const backInnerFrameGeometry = new THREE.CircleGeometry(0.23, 32); // Diamètre 0.46 unités
        const backInnerFrameMaterial = new THREE.MeshStandardMaterial({
            color: 0x333333, // Gris foncé
            metalness: 0.6,
            roughness: 0.3,
            side: THREE.DoubleSide
        });
        const backInnerFrame = new THREE.Mesh(backInnerFrameGeometry, backInnerFrameMaterial);
        backInnerFrame.position.set(0, 0.6, -0.581); // Légèrement devant le cadre extérieur
        backInnerFrame.rotation.y = Math.PI;
        backInnerFrame.rotation.x = Math.PI * 0.1;
        backLogoGroup.add(backInnerFrame);

        // Création d'un cercle pour le logo arrière
        const backLogoGeometry = new THREE.CircleGeometry(0.22, 32); // Diamètre 0.44 unités
        const backLogoMaterial = new THREE.MeshBasicMaterial({
            map: texture,
            color: 0xFFFFFF,
            transparent: true,
            alphaTest: 0.0, // Désactivé pour éviter de couper des parties de l'image
            side: THREE.FrontSide, // Visible uniquement de face
            depthTest: true,
            depthWrite: true,
            blending: THREE.NormalBlending
        });

        const backLogo = new THREE.Mesh(backLogoGeometry, backLogoMaterial);
        backLogo.position.set(0, 0.6, -0.582); // Légèrement devant le cadre intérieur
        backLogo.rotation.y = Math.PI; // Rotation pour faire face à l'arrière
        backLogo.rotation.x = Math.PI * 0.1;
        backLogoGroup.add(backLogo);

        // Ajout de lumières pour éclairer les logos
        const frontLight = new THREE.PointLight(0xFFFFFF, 2.0, 0.8);
        frontLight.position.set(0, 0, 0.2);
        frontLogoGroup.add(frontLight);

        const backLight = new THREE.PointLight(0xFFFFFF, 2.0, 0.8);
        backLight.position.set(0, 0, -0.2);
        backLogoGroup.add(backLight);

        // Vérifier si l'image est correctement chargée après un court délai
        setTimeout(() => {
            // Si l'image n'est pas visible correctement, utiliser la texture de secours
            if (!texture.image || texture.image.width < 10 || texture.image.height < 10) {
                console.log("Utilisation de la texture de secours pour le logo FAR");
                const fallbackTexture = createFallbackTexture();
                frontLogo.material.map = fallbackTexture;
                backLogo.material.map = fallbackTexture;
                frontLogo.material.needsUpdate = true;
                backLogo.material.needsUpdate = true;
            }
        }, 1000);
    });

    // ===== SYSTÈME DE PARTICULES =====
    // Particules pour effet high-tech
    const particlesGeometry = new THREE.BufferGeometry();
    const particleCount = 100;
    const posArray = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        // Distribuer les particules dans un cylindre autour du robot
        const radius = 1.5 + Math.random() * 1.5;
        const theta = Math.random() * Math.PI * 2;
        const y = Math.random() * 4 - 1;

        posArray[i] = radius * Math.cos(theta);
        posArray[i+1] = y;
        posArray[i+2] = radius * Math.sin(theta);
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.03,
        color: 0x00ff00,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
    });

    particles = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particles);

    // Position globale du robot ajustée
    robot.scale.set(1.2, 1.2, 1.2);
    robot.position.y = -0.5;
    robot.rotation.x = 0.1;
    scene.add(robot);

    animate();
}

function animate() {
    requestAnimationFrame(animate);

    // Mise à jour du temps pour les animations
    const delta = clock.getDelta();
    const elapsedTime = clock.getElapsedTime();

    if (robot) {
        // Rotation de base plus fluide
        robot.rotation.y += ROTATION_SPEED * Math.sin(elapsedTime * 0.2) * 0.5 + ROTATION_SPEED;

        // Animation de flottement avancée avec plusieurs fréquences
        robot.position.y = -0.5 +
            Math.sin(elapsedTime * 0.8) * 0.07 +
            Math.sin(elapsedTime * 1.2) * 0.03;

        // Légère oscillation sur l'axe X pour plus de dynamisme
        robot.rotation.z = Math.sin(elapsedTime * 0.4) * 0.02;

        // Animation des antennes
        if (robot.children) {
            // Trouver les groupes d'antennes
            robot.children.forEach(child => {
                if (child.name === 'leftAntennaGroup' || child.name === 'rightAntennaGroup') {
                    // Oscillation des antennes
                    child.rotation.z = Math.sin(elapsedTime * 2 + (child.name === 'leftAntennaGroup' ? 0 : Math.PI)) * 0.05;
                }
            });

            // Animation des lumières des antennes
            const leftAntennaGroup = robot.children.find(child => child.name === 'leftAntennaGroup');
            const rightAntennaGroup = robot.children.find(child => child.name === 'rightAntennaGroup');

            if (leftAntennaGroup && rightAntennaGroup) {
                // Trouver les lumières des antennes
                const leftLight = leftAntennaGroup.children.find(child => child instanceof THREE.PointLight);
                const rightLight = rightAntennaGroup.children.find(child => child instanceof THREE.PointLight);

                if (leftLight && rightLight) {
                    // Clignotement alterné des lumières
                    const blinkIntensity = (Math.sin(elapsedTime * 4) + 1) / 2;
                    leftLight.intensity = 0.4 + blinkIntensity * 0.6;
                    rightLight.intensity = 0.4 + (1 - blinkIntensity) * 0.6;
                }
            }
        }
    }

    // Animation des particules
    if (particles) {
        // Rotation lente des particules
        particles.rotation.y += delta * 0.05;

        // Mise à jour des positions des particules pour un effet de scintillement
        const positions = particles.geometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            // Mouvement vertical lent
            positions[i+1] += Math.sin(elapsedTime + i) * 0.001;

            // Réinitialiser les particules qui sortent de la zone visible
            if (positions[i+1] > 3) positions[i+1] = -1;
        }
        particles.geometry.attributes.position.needsUpdate = true;
    }

    renderer.render(scene, camera);
}

function animateRobot(type) {
    if (!robot) return;

    // Trouver les composants spécifiques pour des animations plus détaillées
    const headGroup = robot.children.find(child => child instanceof THREE.Group && child.position.y === 1.2);
    const leftEye = robot.children.find(child => child instanceof THREE.Mesh && child.position.x < 0 && child.position.y > 1);
    const rightEye = robot.children.find(child => child instanceof THREE.Mesh && child.position.x > 0 && child.position.y > 1);

    // Nommer les groupes d'antennes pour les retrouver facilement
    robot.children.forEach(child => {
        if (child instanceof THREE.Group && child.position.y === 1.8) {
            if (child.position.x < 0) {
                child.name = 'leftAntennaGroup';
            } else if (child.position.x > 0) {
                child.name = 'rightAntennaGroup';
            }
        }
    });

    const leftAntennaGroup = robot.children.find(child => child.name === 'leftAntennaGroup');
    const rightAntennaGroup = robot.children.find(child => child.name === 'rightAntennaGroup');

    switch(type) {
        case 'thinking':
            // Animation de réflexion plus complexe
            // Inclinaison de la tête
            if (headGroup) {
                gsap.to(headGroup.rotation, {
                    x: 0.3,
                    duration: 0.8,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
            }

            // Clignotement des yeux plus rapide
            if (leftEye && rightEye) {
                // Séquence pour l'œil gauche
                gsap.to(leftEye.material, {
                    emissiveIntensity: 0.2,
                    duration: 0.2,
                    yoyo: true,
                    repeat: 5,
                    ease: "power1.inOut"
                });

                // Séquence pour l'œil droit avec léger décalage
                gsap.to(rightEye.material, {
                    emissiveIntensity: 0.2,
                    duration: 0.2,
                    delay: 0.1,
                    yoyo: true,
                    repeat: 5,
                    ease: "power1.inOut"
                });
            }

            // Mouvement des antennes
            if (leftAntennaGroup && rightAntennaGroup) {
                gsap.to(leftAntennaGroup.rotation, {
                    z: 0.2,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });

                gsap.to(rightAntennaGroup.rotation, {
                    z: -0.2,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });
            }
            break;

        case 'speaking':
            // Animation de parole plus sophistiquée
            // Léger mouvement de la tête
            if (headGroup) {
                gsap.to(headGroup.rotation, {
                    y: 0.1,
                    duration: 0.2,
                    yoyo: true,
                    repeat: 3,
                    ease: "power1.inOut"
                });
            }

            // Pulsation des yeux
            if (leftEye && rightEye) {
                const eyeAnimation = {
                    scale: 1.0
                };

                gsap.to(eyeAnimation, {
                    scale: 1.3,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3,
                    onUpdate: () => {
                        leftEye.scale.set(eyeAnimation.scale, eyeAnimation.scale, eyeAnimation.scale);
                        rightEye.scale.set(eyeAnimation.scale, eyeAnimation.scale, eyeAnimation.scale);
                    }
                });

                // Intensité lumineuse
                gsap.to(leftEye.material, {
                    emissiveIntensity: 1.5,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3
                });

                gsap.to(rightEye.material, {
                    emissiveIntensity: 1.5,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3
                });
            }

            // Léger rebond du robot
            gsap.to(robot.position, {
                y: robot.position.y + 0.15,
                duration: 0.2,
                yoyo: true,
                repeat: 1,
                ease: "power2.out"
            });
            break;

        case 'greeting':
            // Animation de salutation plus élaborée
            // Rotation complète avec effet de rebond
            gsap.to(robot.rotation, {
                y: robot.rotation.y + Math.PI * 2,
                duration: 1.5,
                ease: "back.inOut(1.7)"
            });

            // Mouvement vertical
            gsap.to(robot.position, {
                y: robot.position.y + 0.3,
                duration: 0.75,
                yoyo: true,
                repeat: 1,
                ease: "power2.inOut"
            });

            // Animation des yeux
            if (leftEye && rightEye) {
                // Séquence pour les deux yeux
                gsap.to([leftEye.material, rightEye.material], {
                    emissiveIntensity: 2.0,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });
            }

            // Animation des antennes
            if (leftAntennaGroup && rightAntennaGroup) {
                // Mouvement synchronisé des antennes
                gsap.to([leftAntennaGroup.rotation, rightAntennaGroup.rotation], {
                    z: 0.3,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1,
                    stagger: 0.2
                });
            }
            break;
    }
}

// Initialisation de la scène 3D et du chatbot
document.addEventListener('DOMContentLoaded', function() {
    initScene();

    // Animation de salutation initiale après un court délai
    setTimeout(() => {
        animateRobot('greeting');
    }, 1000);

    // Initialiser le défilement du chatbot
    initChatScroll();

    // Gestion du redimensionnement
    window.addEventListener('resize', function() {
        const container = document.getElementById('ai-agent-container');
        const isMobile = window.innerWidth <= 768;
        const width = isMobile ? 150 : container.clientWidth;
        const height = isMobile ? 200 : container.clientHeight;

        camera.aspect = width / height;
        camera.updateProjectionMatrix();
        renderer.setSize(width, height);
    });
});

let isProcessing = false;

function addMessage(content, isUser = false) {
    const messagesDiv = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
    messageDiv.innerHTML = `<div class="message-content">${content}</div>`;

    // Ajouter le message à la fin de la liste (ordre chronologique)
    messagesDiv.appendChild(messageDiv);

    // Calculer la position pour afficher le début du message
    // Nous voulons que le haut du message soit visible
    const messageRect = messageDiv.getBoundingClientRect();
    const containerRect = messagesDiv.getBoundingClientRect();

    // Calculer la position de défilement pour que le message soit en haut
    // mais pas complètement en haut pour éviter de couper le message précédent
    const scrollPosition = messageDiv.offsetTop - 20; // 20px de marge en haut

    // Appliquer le défilement
    messagesDiv.scrollTo({
        top: scrollPosition,
        behavior: 'smooth'
    });

    console.log("Message ajouté, défilement à la position:", scrollPosition);
}

function addTypingIndicator() {
    const messagesDiv = document.getElementById('chatMessages');
    const indicatorDiv = document.createElement('div');
    indicatorDiv.className = 'message bot';
    indicatorDiv.innerHTML = `
        <div class="typing-indicator">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    `;

    // Ajouter l'indicateur de frappe à la fin de la liste
    messagesDiv.appendChild(indicatorDiv);

    // Calculer la position pour afficher le début de l'indicateur
    const scrollPosition = indicatorDiv.offsetTop - 20; // 20px de marge en haut

    // Appliquer le défilement
    messagesDiv.scrollTo({
        top: scrollPosition,
        behavior: 'smooth'
    });

    return indicatorDiv;
}

function removeTypingIndicator(indicator) {
    if (indicator) {
        indicator.remove();
    }
}

async function sendMessage(customMessage = null) {
    if (isProcessing) return;

    const input = document.getElementById('userInput');
    const message = customMessage || input.value.trim();

    if (!message) return;

    isProcessing = true;
    addMessage(message, true);
    input.value = '';

    // Animation de réflexion pendant le chargement
    animateRobot('thinking');
    const typingIndicator = addTypingIndicator();

    try {
        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        });

        const data = await response.json();
        removeTypingIndicator(typingIndicator);
        addMessage(data.response);
        // Animation de parole après la réponse
        animateRobot('speaking');
    } catch (error) {
        removeTypingIndicator(typingIndicator);
        addMessage("Désolé, une erreur s'est produite. Veuillez réessayer.");
    }

    isProcessing = false;
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// Fonction pour initialiser le défilement du chatbot
function initChatScroll() {
    const messagesDiv = document.getElementById('chatMessages');

    // S'assurer que le défilement commence en haut
    messagesDiv.scrollTop = 0;

    // Observer les changements dans le conteneur de messages
    const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
            // Quand le contenu change, s'assurer que le défilement est correct
            const lastMessage = messagesDiv.lastElementChild;
            if (lastMessage) {
                const scrollPosition = lastMessage.offsetTop - 20;
                messagesDiv.scrollTo({
                    top: scrollPosition,
                    behavior: 'smooth'
                });
            }
        }
    });

    // Observer le conteneur de messages
    resizeObserver.observe(messagesDiv);
}
</script>
{% endblock %}