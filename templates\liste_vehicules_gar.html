{% extends "base.html" %}

{% block title %}Liste des Véhicules GAR{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-truck me-2"></i>Liste des Véhicules GAR
                    </h4>
                    <a href="{{ url_for('ajouter_vehicule_gar') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Ajouter un Véhicule
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
        <thead>
            <tr>
                <th>Matricule</th>
                                    <th>Type</th>
                                    <th>Marque</th>
                <th>Unité</th>
            </tr>
        </thead>
        <tbody>
            {% for vehicule in vehicules %}
            <tr>
                <td>{{ vehicule.matricule }}</td>
                <td>{{ vehicule.type_vehicule }}</td>
                <td>{{ vehicule.marque }}</td>
                                    <td>{{ vehicule.unite }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
