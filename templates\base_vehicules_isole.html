<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Véhicules - Division Technique{% endblock %}</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/details-sidebar.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/digital-clock.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-responsive.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-mobile.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/navbar-fix.css', v='1.0') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation isolée pour Division Technique -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('liste_vehicules') }}">
                <i class="fas fa-truck me-2"></i>
                <span>Division Technique - Véhicules</span>
            </a>
            
            <!-- Horloge numérique -->
            <div class="digital-clock-container d-none d-md-block">
                <div class="digital-clock" id="digitalClock"></div>
            </div>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <!-- Navigation spécifique à la Division Technique -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'liste_vehicules' %}active{% endif %}" href="{{ url_for('liste_vehicules') }}">
                            <i class="fas fa-car me-1"></i> <span>Véhicules</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'entretiens' %}active{% endif %}" href="{{ url_for('entretiens') }}">
                            <i class="fas fa-tools me-1"></i> <span>Entretiens</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'courrier_vehicules' %}active{% endif %}" href="{{ url_for('courrier_vehicules') }}">
                            <i class="fas fa-envelope me-1"></i> <span>Gestion du Courrier</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('chatbot') }}">
                            <i class="fas fa-robot me-1"></i> <span>Assistant IA</span>
                        </a>
                    </li>
                    
                    {% if session.logged_in %}
                    <li class="nav-item">
                        <span class="nav-link">
                            <i class="fas fa-user me-1"></i> <span>{{ session.username }}</span>
                        </span>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-1"></i> <span>Déconnexion</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenu principal -->
    <div class="container-fluid mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer isolé -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2024 Division Technique - Gestion des Véhicules. Tous droits réservés.</span>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Horloge numérique
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('fr-FR', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            const clockElement = document.getElementById('digitalClock');
            if (clockElement) {
                clockElement.innerHTML = `
                    <div class="time">${timeString}</div>
                    <div class="date">${dateString}</div>
                `;
            }
        }

        // Mettre à jour l'horloge toutes les secondes
        setInterval(updateClock, 1000);
        updateClock(); // Affichage initial
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
