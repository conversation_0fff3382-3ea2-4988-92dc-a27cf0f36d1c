{% extends "base.html" %}

{% block title %}Accueil{% endblock %}

{% block extra_css %}
<style>
    /* Style pour le champ de recherche */
    .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }

    /* Style pour le bouton de tri */
    #sortDropdown {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        padding-top: 0.375rem;
        padding-bottom: 0.375rem;
    }

    /* Style pour les options de tri */
    .dropdown-item.sort-option {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .dropdown-item.sort-option:hover {
        background-color: rgba(40, 167, 69, 0.1);
    }

    /* Style pour la modal des détails */
    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.3);
        pointer-events: none;
    }

    .modal-backdrop.show {
        opacity: 0.3;
    }

    .modal {
        background: transparent;
        pointer-events: auto;
    }

    .modal-dialog {
        margin: 1.75rem auto;
        max-width: 800px;
        pointer-events: auto;
    }

    .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
        pointer-events: auto;
    }

    .modal-header {
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 15px 15px 0 0;
        pointer-events: auto;
    }

    .modal-body {
        padding: 1.5rem;
        max-height: 70vh;
        overflow-y: auto;
        pointer-events: auto;
    }
</style>
{% endblock %}

{% block content %}
<!-- Modal pour les détails -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i>Informations du véhicule
        </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="historiqueContainer">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-3 text-muted">Chargement de l'historique...</p>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <a href="#" class="btn btn-success" id="detailsHistoriqueLink">
                        <i class="fas fa-history me-2"></i>Voir l'historique complet
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header py-2 d-flex justify-content-between align-items-center" style="min-height: 60px; overflow: hidden;">
                <h4 class="mb-0">
                    <i class="fas fa-car me-2"></i>Liste des Véhicules
                </h4>
                <div class="d-flex align-items-center gap-2 mx-2" style="height: 60px;">
                    <img src="{{ url_for('static', filename='images/atlas.png') }}" alt="Atlas" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                    <img src="{{ url_for('static', filename='images/m109.png') }}" alt="M109" style="height: 90px; width: auto; object-fit: contain; transform: scale(0.95);">
                    <img src="{{ url_for('static', filename='images/vvv.png') }}" alt="Véhicule" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                </div>
                <div class="d-flex gap-2">
                <a href="{{ url_for('ajouter') }}" class="btn btn-primary">
                    <i class="fas fa-tools me-1"></i>Nouvelle Panne
                </a>
                </div>
            </div>
            <div class="card-body bg-light py-2">
                <div class="d-flex justify-content-between align-items-center">
                    <form action="{{ url_for('liste_vehicules') }}" method="get" class="d-flex flex-grow-1 me-3">
                        <div class="input-group">
                            <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search_matricule %}
                            <a href="{{ url_for('liste_vehicules') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Effacer
                            </a>
                            {% endif %}
                        </div>
                    </form>

                    <!-- Bouton de tri avec menu déroulant simplifié -->
                    <div class="dropdown">
                        <button class="btn btn-outline-success dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-sort me-1"></i> Trier par
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item sort-option" href="#" data-sort="type" data-direction="asc">Type de véhicule</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="unite" data-direction="asc">GAR</a></li>
                            <li><a class="dropdown-item sort-option" href="#" data-sort="date" data-direction="desc">Date</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if vehicules %}
                <div class="table-responsive">
                    <table id="vehiculesTable" class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 8%;">Type</th>
                                <th style="width: 12%;">Marque</th>
                                <th style="width: 12%;">Matricule</th>
                                <th style="width: 10%;">Unité</th>
                                <th style="width: 18%;">Type&nbsp;de&nbsp;Panne</th>
                                <th style="width: 12%;">Date&nbsp;de&nbsp;Panne</th>
                                <th style="width: 10%;">Statut</th>
                                <th style="width: 18%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for vehicule in vehicules %}
                            <tr class="fade-in"
                                style="animation-delay: {{ loop.index0 * 0.1 }}s"
                                data-type="{{ vehicule['type_vehicule'] }}"
                                data-unite="{{ vehicule['unite'] if vehicule['unite'] else 'Non assigné' }}"
                                data-date="{{ vehicule['date_panne'] }}">
                                <td>
                                    <span class="badge bg-{{ 'primary' if vehicule['type_vehicule'] == 'VL' else 'secondary' }}">
                                        {{ vehicule['type_vehicule'] }}
                                    </span>
                                </td>
                                <td>{{ vehicule['marque'] }}</td>
                                <td>{{ vehicule['matricule'] }}</td>
                                <td>
                                    <span class="badge bg-info text-dark">
                                        {{ vehicule['unite'] if vehicule['unite'] else 'Non assigné' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-warning text-dark">
                                        {{ vehicule['type_panne'] }}
                                    </span>
                                </td>
                                <td>{{ vehicule['date_panne'] }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if vehicule['statut'] == 'Réparé' else ('warning text-dark' if vehicule['statut'] == 'Indisponible' else ('info' if vehicule['statut'] == 'En réparation' else 'danger')) }}">
                                        {{ vehicule['statut'] }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#"
                                           class="btn btn-sm"
                                           style="background-color: #e6f7ff; color: #0066cc; border: 1px solid #80bdff;"
                                           data-id="{{ vehicule['id'] }}"
                                           data-type="{{ vehicule['type_vehicule'] }}"
                                           data-marque="{{ vehicule['marque'] }}"
                                           data-matricule="{{ vehicule['matricule'] }}"
                                           data-unite="{{ vehicule['unite'] }}"
                                           data-panne="{{ vehicule['type_panne'] }}"
                                           data-date="{{ vehicule['date_panne'] }}"
                                           data-statut="{{ vehicule['statut'] }}"
                                           data-description="{{ vehicule['description'] }}"
                                           onclick="showDetailsSidebar(this); return false;"
                                           title="Détails"
                                           onmouseover="this.style.backgroundColor='#d0e9ff'; this.style.borderColor='#4da3ff';"
                                           onmouseout="this.style.backgroundColor='#e6f7ff'; this.style.borderColor='#80bdff';">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{{ url_for('modifier', id=vehicule['id']) }}"
                                           class="btn btn-sm btn-outline-primary"
                                           data-bs-toggle="tooltip" data-bs-placement="top" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('supprimer', id=vehicule['id']) }}" method="post" style="display: inline;">
                                            <button type="submit"
                                                class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce véhicule ?')"
                                                data-bs-toggle="tooltip" data-bs-placement="top" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="d-flex justify-content-center align-items-center flex-wrap gap-4 mb-4">
                        <img src="{{ url_for('static', filename='images/vv.jpg') }}" alt="Image véhicule"
                        style="max-width: 350px; border-radius: 50%; object-fit: cover;">
                        <img src="{{ url_for('static', filename='images/atlas.png') }}" alt="Atlas"
                        style="max-width: 300px; border-radius: 10px; object-fit: cover;">
                        <img src="{{ url_for('static', filename='images/m109.png') }}" alt="M109"
                        style="max-width: 300px; border-radius: 10px; object-fit: cover;">
                    </div>

                    <h3 class="text-muted">Aucun véhicule enregistré</h3>
                    <p class="text-muted mb-4">Commencez par ajouter votre premier véhicule</p>
                    <a href="{{ url_for('ajouter') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-tools me-2"></i>Enregistrer une Panne
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Animation des lignes du tableau et initialisation du tri
    document.addEventListener('DOMContentLoaded', function() {
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            setTimeout(() => {
                row.style.opacity = '1';
            }, index * 100);
        });

        // Initialiser le tri du tableau
        initTableSort();
    });

    // Fonction pour initialiser le tri du tableau
    function initTableSort() {
        const sortOptions = document.querySelectorAll('.sort-option');

        sortOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();

                // Récupérer les attributs de tri
                const sortBy = this.getAttribute('data-sort');
                const direction = this.getAttribute('data-direction');

                // Mettre à jour le texte du bouton de tri
                document.getElementById('sortDropdown').innerHTML = `<i class="fas fa-sort me-1"></i> Trié par ${this.textContent}`;

                // Trier le tableau
                sortTable(sortBy, direction);
            });
        });
    }

    // Fonction pour extraire le numéro d'une unité GAR
    function extractGarNumber(unit) {
        if (!unit) return { num: 999, text: unit };
        const match = unit.match(/(\d+)GAR/i);
        if (match) {
            return { num: parseInt(match[1], 10), text: unit };
        }
        return { num: 999, text: unit };
    }

    // Fonction pour trier le tableau
    function sortTable(sortBy, direction) {
        const table = document.getElementById('vehiculesTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        // Trier les lignes
        rows.sort((a, b) => {
            let valueA, valueB;

            if (sortBy === 'type') {
                valueA = a.getAttribute('data-type');
                valueB = b.getAttribute('data-type');
                
                // Comparaison simple pour les types
                if (valueA < valueB) return direction === 'asc' ? -1 : 1;
                if (valueA > valueB) return direction === 'asc' ? 1 : -1;
                return 0;
                
            } else if (sortBy === 'unite') {
                const garA = extractGarNumber(a.getAttribute('data-unite'));
                const garB = extractGarNumber(b.getAttribute('data-unite'));
                
                // D'abord trier par numéro, puis par texte si les numéros sont égaux
                if (garA.num !== garB.num) {
                    return direction === 'asc' 
                        ? garA.num - garB.num 
                        : garB.num - garA.num;
                }
                
                // Si les numéros sont égaux, trier par le texte complet
                if (garA.text < garB.text) return direction === 'asc' ? -1 : 1;
                if (garA.text > garB.text) return direction === 'asc' ? 1 : -1;
                return 0;
                
            } else if (sortBy === 'date') {
                valueA = new Date(a.getAttribute('data-date'));
                valueB = new Date(b.getAttribute('data-date'));
                
                return direction === 'asc' 
                    ? valueA - valueB 
                    : valueB - valueA;
            }
            
            return 0;
        });

        // Réorganiser les lignes dans le tableau
        rows.forEach(row => {
            tbody.appendChild(row);
        });
    }

    // Fonction pour afficher les détails
    function showDetailsSidebar(element) {
        // Récupérer les données du véhicule
        const id = element.getAttribute('data-id');
        const matricule = element.getAttribute('data-matricule');

        // Mettre à jour le titre
        document.getElementById('detailsModalLabel').innerHTML = 
            `<i class="fas fa-info-circle me-2"></i>Informations du véhicule - Matricule: ${matricule}`;

        // Mettre à jour le lien vers l'historique
        document.getElementById('detailsHistoriqueLink').href = `/historique?search_matricule=${matricule}`;

        // Faire défiler vers le haut de la page
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });

        // Afficher la modal
        const modal = new bootstrap.Modal(document.getElementById('detailsModal'), {
            backdrop: true,
            keyboard: true
        });

        // Configurer la modal pour qu'elle ne bloque pas le défilement
        const modalElement = document.getElementById('detailsModal');
        modalElement.addEventListener('shown.bs.modal', function () {
            document.body.style.overflow = 'auto';
            document.body.style.paddingRight = '0';

            // S'assurer que les boutons sont cliquables
            const closeButton = modalElement.querySelector('.btn-close');
            const historiqueLink = modalElement.querySelector('#detailsHistoriqueLink');
            
            if (closeButton) {
                closeButton.style.pointerEvents = 'auto';
            }
            if (historiqueLink) {
                historiqueLink.style.pointerEvents = 'auto';
            }
        });

        modal.show();

        // Charger l'historique du véhicule
        loadVehiculeHistorique(id);
    }

    // Fonction pour charger l'historique du véhicule
    function loadVehiculeHistorique(vehiculeId) {
        // Afficher le spinner de chargement
        document.getElementById('historiqueContainer').innerHTML = `
            <div class="text-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Chargement de l'historique...</p>
            </div>
        `;

        // Appeler l'API pour récupérer l'historique
        fetch(`/api/historique/${vehiculeId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Erreur lors de la récupération de l\'historique');
                }
                return response.json();
            })
            .then(data => {
                if (data && data.historique) {
                displayHistorique(data.historique);
                } else {
                    throw new Error('Données d\'historique invalides');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                document.getElementById('historiqueContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>Erreur lors du chargement de l'historique: ${error.message}
                    </div>
                `;
            });
    }

    // Fonction pour afficher l'historique
    function displayHistorique(historique) {
        if (!historique || historique.length === 0) {
            document.getElementById('historiqueContainer').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Aucun historique disponible
                </div>
            `;
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Statut</th>
                            <th>Type de panne</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        historique.forEach((entry, index) => {
            const badgeClass = getBadgeClassForStatus(entry.statut);
            html += `
                <tr class="animate-item" style="animation-delay: ${index * 0.1}s">
                    <td>${formatDateFr(entry.date_changement)}</td>
                    <td><span class="badge ${badgeClass}">${entry.statut}</span></td>
                    <td>${entry.type_panne || 'Non spécifié'}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        document.getElementById('historiqueContainer').innerHTML = html;
    }

    // Fonction pour formater la date
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toISOString().replace('T', ' ').substring(0, 19);
    }

    // Fonction pour formater la date au format français (JJ/MM/AAAA)
    function formatDateFr(dateString) {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    }

    // Fonction pour obtenir la classe de badge en fonction du statut
    function getBadgeClassForStatus(statut) {
        if (statut === 'Réparé') {
            return 'bg-success text-white';
        } else if (statut === 'Indisponible') {
            return 'bg-warning text-dark';
        } else if (statut === 'En réparation') {
            return 'bg-info text-white';
        } else {
            return 'bg-danger text-white';
        }
    }
</script>
{% endblock %}