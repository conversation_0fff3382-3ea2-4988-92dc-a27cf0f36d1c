/* Table Mobile CSS - Améliorations pour les tableaux sur mobile */

/* R<PERSON>gles générales pour les tableaux sur mobile */
@media (max-width: 768px) {
    /* Conteneur de tableau responsive */
    .table-responsive {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    /* Style du tableau */
    .table {
        margin-bottom: 0;
        font-size: 0.85rem;
        background: white;
    }
    
    /* En-têtes de tableau */
    .table thead th {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
        white-space: nowrap;
    }
    
    /* Cellules du tableau */
    .table tbody td {
        padding: 0.75rem 0.5rem;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
        word-wrap: break-word;
        max-width: 120px;
    }
    
    /* Dernière ligne sans bordure */
    .table tbody tr:last-child td {
        border-bottom: none;
    }
    
    /* Hover sur les lignes */
    .table tbody tr:hover {
        background-color: rgba(75, 83, 32, 0.05);
    }
    
    /* Badges dans les tableaux */
    .table .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Boutons dans les tableaux */
    .table .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        margin: 0.1rem;
    }
    
    .table .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
    
    /* Masquer certaines colonnes sur mobile */
    .table-responsive .table th:nth-child(4),
    .table-responsive .table td:nth-child(4),
    .table-responsive .table th:nth-child(5),
    .table-responsive .table td:nth-child(5) {
        display: none;
    }
    
    /* Colonnes importantes toujours visibles */
    .table-responsive .table th:first-child,
    .table-responsive .table td:first-child,
    .table-responsive .table th:nth-child(2),
    .table-responsive .table td:nth-child(2),
    .table-responsive .table th:nth-child(3),
    .table-responsive .table td:nth-child(3) {
        display: table-cell;
    }
    
    /* Actions dans la dernière colonne */
    .table-responsive .table th:last-child,
    .table-responsive .table td:last-child {
        display: table-cell;
        text-align: center;
        min-width: 80px;
    }
}

/* Règles pour très petits écrans */
@media (max-width: 576px) {
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }
    
    .table tbody td {
        padding: 0.5rem 0.25rem;
        max-width: 100px;
    }
    
    /* Masquer encore plus de colonnes sur très petits écrans */
    .table-responsive .table th:nth-child(3),
    .table-responsive .table td:nth-child(3) {
        display: none;
    }
    
    /* Garder seulement les colonnes essentielles */
    .table-responsive .table th:first-child,
    .table-responsive .table td:first-child,
    .table-responsive .table th:nth-child(2),
    .table-responsive .table td:nth-child(2),
    .table-responsive .table th:last-child,
    .table-responsive .table td:last-child {
        display: table-cell;
    }
    
    /* Boutons plus compacts */
    .table .btn {
        padding: 0.2rem 0.3rem;
        font-size: 0.7rem;
        margin: 0.05rem;
    }
    
    .table .btn-sm {
        padding: 0.15rem 0.25rem;
        font-size: 0.65rem;
    }
}

/* Règles pour les tableaux de véhicules spécifiquement */
@media (max-width: 768px) {
    .vehicle-table .table th,
    .vehicle-table .table td {
        text-align: center;
    }
    
    /* Colonnes spécifiques pour les véhicules */
    .vehicle-table .table th:nth-child(1), /* ID */
    .vehicle-table .table td:nth-child(1) {
        display: none;
    }
    
    .vehicle-table .table th:nth-child(2), /* Type */
    .vehicle-table .table td:nth-child(2) {
        display: table-cell;
        font-weight: bold;
    }
    
    .vehicle-table .table th:nth-child(3), /* Marque */
    .vehicle-table .table td:nth-child(3) {
        display: table-cell;
    }
    
    .vehicle-table .table th:nth-child(4), /* Matricule */
    .vehicle-table .table td:nth-child(4) {
        display: table-cell;
        font-weight: bold;
        color: var(--primary-color);
    }
    
    .vehicle-table .table th:nth-child(5), /* Statut */
    .vehicle-table .table td:nth-child(5) {
        display: table-cell;
    }
    
    .vehicle-table .table th:nth-child(6), /* Unité */
    .vehicle-table .table td:nth-child(6) {
        display: none;
    }
    
    .vehicle-table .table th:nth-child(7), /* Actions */
    .vehicle-table .table td:nth-child(7) {
        display: table-cell;
    }
}

/* Règles pour les tableaux d'historique */
@media (max-width: 768px) {
    .history-table .table th,
    .history-table .table td {
        text-align: center;
    }
    
    .history-table .table th:nth-child(1), /* Date */
    .history-table .table td:nth-child(1) {
        display: table-cell;
        font-weight: bold;
    }
    
    .history-table .table th:nth-child(2), /* Statut */
    .history-table .table td:nth-child(2) {
        display: table-cell;
    }
    
    .history-table .table th:nth-child(3), /* Description */
    .history-table .table td:nth-child(3) {
        display: none;
    }
}

/* Règles pour les tableaux d'entretiens */
@media (max-width: 768px) {
    .maintenance-table .table th,
    .maintenance-table .table td {
        text-align: center;
    }
    
    .maintenance-table .table th:nth-child(1), /* Date */
    .maintenance-table .table td:nth-child(1) {
        display: table-cell;
        font-weight: bold;
    }
    
    .maintenance-table .table th:nth-child(2), /* Type */
    .maintenance-table .table td:nth-child(2) {
        display: table-cell;
    }
    
    .maintenance-table .table th:nth-child(3), /* Kilométrage */
    .maintenance-table .table td:nth-child(3) {
        display: table-cell;
    }
    
    .maintenance-table .table th:nth-child(4), /* Actions */
    .maintenance-table .table td:nth-child(4) {
        display: table-cell;
    }
}

/* Améliorations pour les tableaux avec des données longues */
@media (max-width: 768px) {
    .table td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    /* Permettre le retour à la ligne pour certaines cellules */
    .table td.allow-wrap {
        white-space: normal;
        word-wrap: break-word;
        max-width: 150px;
    }
}

/* Améliorations pour les tableaux avec des images */
@media (max-width: 768px) {
    .table img {
        max-width: 30px;
        max-height: 30px;
        border-radius: 4px;
    }
    
    .table .avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        object-fit: cover;
    }
}

/* Améliorations pour les tableaux avec des icônes */
@media (max-width: 768px) {
    .table .fas,
    .table .far,
    .table .fab {
        font-size: 0.9rem;
        margin: 0 0.1rem;
    }
    
    .table .btn .fas,
    .table .btn .far,
    .table .btn .fab {
        font-size: 0.8rem;
        margin: 0;
    }
}

/* Améliorations pour les tableaux avec des statuts */
@media (max-width: 768px) {
    .table .status-badge {
        display: inline-block;
        padding: 0.2rem 0.4rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table .status-en-panne {
        background-color: #dc3545;
        color: white;
    }
    
    .table .status-en-reparation {
        background-color: #ffc107;
        color: #212529;
    }
    
    .table .status-repare {
        background-color: #28a745;
        color: white;
    }
    
    .table .status-indisponible {
        background-color: #6c757d;
        color: white;
    }
}

/* Améliorations pour les tableaux avec des actions */
@media (max-width: 768px) {
    .table .actions-cell {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        align-items: center;
    }
    
    .table .actions-cell .btn {
        width: 100%;
        margin: 0;
    }
    
    .table .actions-cell .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .table .actions-cell .btn-group .btn {
        border-radius: 4px;
        margin: 0.1rem 0;
    }
}

/* Améliorations pour les tableaux avec des formulaires */
@media (max-width: 768px) {
    .table .form-check {
        margin: 0;
        text-align: center;
    }
    
    .table .form-check-input {
        margin: 0;
    }
    
    .table .form-control {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
    
    .table .form-select {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Améliorations pour les tableaux avec des liens */
@media (max-width: 768px) {
    .table a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }
    
    .table a:hover {
        text-decoration: underline;
    }
    
    .table .link-button {
        background: none;
        border: none;
        color: var(--primary-color);
        padding: 0;
        font-size: inherit;
        text-decoration: underline;
        cursor: pointer;
    }
}

/* Améliorations pour les tableaux vides */
@media (max-width: 768px) {
    .table tbody:empty::after {
        content: "Aucune donnée disponible";
        display: table-cell;
        text-align: center;
        padding: 2rem;
        color: #6c757d;
        font-style: italic;
        grid-column: 1 / -1;
    }
}

/* Améliorations pour les tableaux avec pagination */
@media (max-width: 768px) {
    .table-pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }
    
    .table-pagination .pagination {
        margin: 0;
    }
    
    .table-pagination .page-info {
        font-size: 0.8rem;
        color: #6c757d;
    }
}

/* Améliorations pour les tableaux avec recherche */
@media (max-width: 768px) {
    .table-search {
        margin-bottom: 1rem;
    }
    
    .table-search .input-group {
        flex-direction: column;
    }
    
    .table-search .input-group .form-control {
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }
    
    .table-search .input-group .btn {
        border-radius: 4px;
        width: 100%;
    }
}

/* Améliorations pour les tableaux avec filtres */
@media (max-width: 768px) {
    .table-filters {
        margin-bottom: 1rem;
    }
    
    .table-filters .row {
        margin: 0;
    }
    
    .table-filters .col-md-3,
    .table-filters .col-md-4 {
        margin-bottom: 0.5rem;
        padding: 0 0.25rem;
    }
    
    .table-filters .form-select,
    .table-filters .form-control {
        font-size: 0.9rem;
        padding: 0.5rem;
    }
} 