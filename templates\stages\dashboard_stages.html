{% extends "stages/base_stages.html" %}

{% block title %}Tableau de Bord - Stages{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Tableau de Bord - Gestion des Stages</h1>
    </div>
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="card shadow-lg border-0 dashboard-card position-relative overflow-hidden" style="background: linear-gradient(135deg, #007bff 60%, #00c6ff 100%);">
                <div class="card-body text-white text-center">
                    <div class="mb-2 animate__animated animate__bounceIn">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                    <h2 class="display-5 fw-bold">125</h2>
                    <div class="fs-5">Stagiaires inscrits</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-lg border-0 dashboard-card position-relative overflow-hidden" style="background: linear-gradient(135deg, #28a745 60%, #a8e063 100%);">
                <div class="card-body text-white text-center">
                    <div class="mb-2 animate__animated animate__pulse animate__infinite infinite">
                        <i class="fas fa-briefcase fa-3x"></i>
                    </div>
                    <h2 class="display-5 fw-bold">12</h2>
                    <div class="fs-5">Stages en cours</div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-lg border-0 dashboard-card position-relative overflow-hidden" style="background: linear-gradient(135deg, #17a2b8 60%, #6dd5ed 100%);">
                <div class="card-body text-white text-center">
                    <div class="mb-2 animate__animated animate__fadeInDown animate__delay-1s">
                        <i class="fas fa-layer-group fa-3x"></i>
                    </div>
                    <h2 class="display-5 fw-bold">8</h2>
                    <div class="fs-5">Promotions</div>
                </div>
            </div>
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow border-0 mb-4">
                <div class="card-body text-center">
                    <div style="max-width: 260px; margin: 0 auto;">
                        <canvas id="chartStatutDashboard" height="140" width="260"></canvas>
                    </div>
                    <div class="fw-semibold mt-3" style="font-size: 1.08rem; color: #555;">Répartition des stages selon leur statut</div>
                </div>
            </div>
        </div>
    </div>
</div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Données fictives pour le graphique
    var statutLabels = ['En attente', 'Validé', 'Terminé', 'Prorogé'];
    var stagesParStatut = [4, 5, 2, 1];
    var ctxStatut = document.getElementById('chartStatutDashboard');
    if (ctxStatut) {
        new Chart(ctxStatut.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: statutLabels,
                datasets: [{
                    label: 'Nombre de stages',
                    data: stagesParStatut,
                    backgroundColor: [
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: { legend: { position: 'bottom' } },
                cutout: '65%',
                layout: { padding: 0 },
            }
        });
    }
    // Effet de survol sur les cards
    document.querySelectorAll('.dashboard-card').forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            card.style.transform = 'scale(1.04)';
            card.style.boxShadow = '0 8px 32px rgba(0,0,0,0.18)';
        });
        card.addEventListener('mouseleave', function() {
            card.style.transform = 'scale(1)';
            card.style.boxShadow = '';
        });
    });
});
</script>
{% endblock %} 