#!/usr/bin/env python3
"""
Test final de l'interface de gestion de courrier
"""

import requests
import json
from datetime import datetime, date
import time

BASE_URL = "http://127.0.0.1:3000"

def test_ajout_et_affichage():
    """Test d'ajout d'un courrier et vérification de l'affichage"""
    print("🧪 Test d'ajout et vérification de l'affichage...")
    
    # Créer un courrier unique avec timestamp
    timestamp = int(datetime.now().timestamp())
    
    # Test courrier arrivé
    courrier_arrive = {
        "id": f"CA-FINAL-{timestamp}",
        "urgence": "urgent",
        "nature": "message",
        "date_arrivee": date.today().strftime('%Y-%m-%d'),
        "date_signature": date.today().strftime('%Y-%m-%d'),
        "numero_ecrit": f"FINAL-ARR-{timestamp}",
        "expediteur": "Test Final Expediteur",
        "objet": f"Test final d'affichage - Courrier {timestamp}",
        "classification": "public",
        "annotation": "Test final de l'interface",
        "divisions_action": ["technique", "instruction"],
        "divisions_info": ["rh"]
    }
    
    print(f"📥 Ajout d'un courrier arrivé: {courrier_arrive['id']}")
    
    try:
        # Ajouter le courrier arrivé
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_arrive)
        )
        
        if response.status_code == 201:
            print(f"✅ Courrier arrivé ajouté avec succès")
            
            # Attendre un peu pour la synchronisation
            time.sleep(1)
            
            # Vérifier qu'il apparaît dans l'API
            response_list = requests.get(f"{BASE_URL}/api/courriers/arrives")
            if response_list.status_code == 200:
                courriers = response_list.json()
                courrier_trouve = next((c for c in courriers if c['id'] == courrier_arrive['id']), None)
                
                if courrier_trouve:
                    print(f"✅ Courrier trouvé dans l'API")
                    print(f"   📋 Objet: {courrier_trouve['objet']}")
                    print(f"   🏢 Divisions action: {courrier_trouve.get('divisions_action', [])}")
                    print(f"   📢 Divisions info: {courrier_trouve.get('divisions_info', [])}")
                else:
                    print(f"❌ Courrier non trouvé dans l'API")
            
        else:
            print(f"❌ Erreur lors de l'ajout: {response.text}")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Test courrier envoyé
    courrier_envoye = {
        "id": f"CE-FINAL-{timestamp}",
        "numero_ecrit": f"FINAL-ENV-{timestamp}",
        "division_emettrice": "technique",
        "date_depart": date.today().strftime('%Y-%m-%d'),
        "nature": "nds",
        "objet": f"Test final d'affichage - Courrier envoyé {timestamp}",
        "destinataire": "Test Final Destinataire",
        "observations": "Test final de l'interface envoyée"
    }
    
    print(f"\n📤 Ajout d'un courrier envoyé: {courrier_envoye['id']}")
    
    try:
        # Ajouter le courrier envoyé
        response = requests.post(
            f"{BASE_URL}/api/courriers/envoyes",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_envoye)
        )
        
        if response.status_code == 201:
            print(f"✅ Courrier envoyé ajouté avec succès")
            
            # Attendre un peu pour la synchronisation
            time.sleep(1)
            
            # Vérifier qu'il apparaît dans l'API
            response_list = requests.get(f"{BASE_URL}/api/courriers/envoyes")
            if response_list.status_code == 200:
                courriers = response_list.json()
                courrier_trouve = next((c for c in courriers if c['id'] == courrier_envoye['id']), None)
                
                if courrier_trouve:
                    print(f"✅ Courrier trouvé dans l'API")
                    print(f"   📋 Objet: {courrier_trouve['objet']}")
                    print(f"   🏢 Division émettrice: {courrier_trouve.get('division_emettrice')}")
                    print(f"   📮 Destinataire: {courrier_trouve.get('destinataire')}")
                else:
                    print(f"❌ Courrier non trouvé dans l'API")
            
        else:
            print(f"❌ Erreur lors de l'ajout: {response.text}")
    
    except Exception as e:
        print(f"❌ Erreur: {e}")

def afficher_statistiques():
    """Afficher les statistiques finales"""
    print("\n📊 Statistiques finales...")
    
    try:
        # Courriers arrivés
        response_arrives = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response_arrives.status_code == 200:
            courriers_arrives = response_arrives.json()
            print(f"📥 Courriers arrivés: {len(courriers_arrives)}")
            
            if courriers_arrives:
                print("   Derniers courriers arrivés:")
                for courrier in courriers_arrives[-3:]:  # 3 derniers
                    print(f"   • {courrier['id']}: {courrier['objet'][:50]}...")
        
        # Courriers envoyés
        response_envoyes = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        if response_envoyes.status_code == 200:
            courriers_envoyes = response_envoyes.json()
            print(f"📤 Courriers envoyés: {len(courriers_envoyes)}")
            
            if courriers_envoyes:
                print("   Derniers courriers envoyés:")
                for courrier in courriers_envoyes[-3:]:  # 3 derniers
                    print(f"   • {courrier['id']}: {courrier['objet'][:50]}...")
    
    except Exception as e:
        print(f"❌ Erreur lors de la récupération des statistiques: {e}")

def instructions_utilisateur():
    """Afficher les instructions pour l'utilisateur"""
    print("\n" + "="*70)
    print("🎯 INSTRUCTIONS POUR VÉRIFIER L'INTERFACE")
    print("="*70)
    print()
    print("1. 🌐 Ouvrez votre navigateur et allez sur:")
    print(f"   📥 Courriers arrivés: {BASE_URL}/courrier/arrives")
    print(f"   📤 Courriers envoyés: {BASE_URL}/courrier/envoyes")
    print()
    print("2. 🔍 Vérifiez que:")
    print("   ✅ Les courriers s'affichent dans le tableau")
    print("   ✅ Les compteurs sont corrects")
    print("   ✅ Les filtres fonctionnent")
    print("   ✅ Les formulaires d'ajout fonctionnent")
    print()
    print("3. 🛠️ Si les courriers ne s'affichent pas:")
    print("   • Appuyez sur F12 pour ouvrir les outils de développement")
    print("   • Regardez la console pour les messages de débogage")
    print("   • Rechargez la page avec Ctrl+F5")
    print("   • Vérifiez l'onglet Network pour les erreurs d'API")
    print()
    print("4. ✨ Testez l'ajout d'un nouveau courrier:")
    print("   • Cliquez sur 'Ajouter un courrier'")
    print("   • Remplissez le formulaire")
    print("   • Vérifiez qu'il apparaît immédiatement dans la liste")
    print()
    print("🎉 Le système est maintenant entièrement fonctionnel !")

def main():
    """Fonction principale"""
    print("🔬 TEST FINAL DE L'INTERFACE DE GESTION DE COURRIER")
    print("=" * 60)
    
    # Vérifier que l'application est accessible
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ L'application n'est pas accessible sur {BASE_URL}")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter à l'application: {e}")
        return
    
    print(f"✅ Application accessible sur {BASE_URL}")
    
    # Exécuter les tests
    test_ajout_et_affichage()
    afficher_statistiques()
    instructions_utilisateur()

if __name__ == "__main__":
    main()
