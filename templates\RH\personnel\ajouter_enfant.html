{% extends "base.html" %}

{% block title %}Ajouter Enfant - RH{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-baby"></i>
                        Ajouter un enfant pour {{ personnel.prenom }} {{ personnel.nom }}
                    </h3>
                </div>
                
                <form method="POST" action="{{ url_for('rh.ajouter_enfant', matricule=personnel.matricule) }}" class="needs-validation" novalidate>
                    <div class="card-body">
                        
                        <!-- Section Informations personnelles -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> Informations personnelles
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom" class="required">Nom</label>
                                    <input type="text" class="form-control" id="nom" name="nom" 
                                           value="{{ personnel.nom }}" readonly>
                                    <small class="form-text text-muted">Nom de famille hérité du parent</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom" class="required">Prénom</label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" required>
                                    <div class="invalid-feedback">Le prénom est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="sexe_id" class="required">Sexe</label>
                                    <select class="form-control" id="sexe_id" name="sexe_id" required>
                                        <option value="">Sélectionner...</option>
                                        {% for genre in genres %}
                                        <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                        {% endfor %}
                                    </select>
                                    <div class="invalid-feedback">Le sexe est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_naissance" class="required">Date de naissance</label>
                                    <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                                    <div class="invalid-feedback">La date de naissance est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lieu_naissance" class="required">Lieu de naissance</label>
                                    <input type="text" class="form-control" id="lieu_naissance" name="lieu_naissance" required>
                                    <div class="invalid-feedback">Le lieu de naissance est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_deces">Date de décès (si applicable)</label>
                                    <input type="date" class="form-control" id="date_deces" name="date_deces">
                                    <small class="form-text text-muted">Laisser vide si l'enfant est vivant</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Informations complémentaires -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-secondary mb-3">
                                    <i class="fas fa-info-circle"></i> Informations complémentaires
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Note :</strong> Les informations de cet enfant seront liées au dossier de 
                                    <strong>{{ personnel.prenom }} {{ personnel.nom }}</strong> (Matricule: {{ personnel.matricule }}).
                                </div>
                            </div>
                        </div>
                        
                        <!-- Validation des dates -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Attention :</strong> 
                                    <ul class="mb-0 mt-2">
                                        <li>La date de naissance ne peut pas être dans le futur</li>
                                        <li>Si une date de décès est renseignée, elle doit être postérieure à la date de naissance</li>
                                        <li>Vérifiez bien l'orthographe du prénom car il sera utilisé dans les documents officiels</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=personnel.matricule) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.required::after {
    content: " *";
    color: red;
}
</style>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Validation des dates
document.addEventListener('DOMContentLoaded', function() {
    const dateNaissance = document.getElementById('date_naissance');
    const dateDeces = document.getElementById('date_deces');
    
    // Validation date de naissance (pas dans le futur)
    dateNaissance.addEventListener('change', function() {
        const today = new Date().toISOString().split('T')[0];
        if (this.value > today) {
            this.setCustomValidity('La date de naissance ne peut pas être dans le futur');
        } else {
            this.setCustomValidity('');
        }
        
        // Vérifier aussi la cohérence avec la date de décès
        if (dateDeces.value && this.value > dateDeces.value) {
            dateDeces.setCustomValidity('La date de décès doit être postérieure à la date de naissance');
        } else if (dateDeces.value) {
            dateDeces.setCustomValidity('');
        }
    });
    
    // Validation date de décès
    dateDeces.addEventListener('change', function() {
        if (this.value && dateNaissance.value && this.value <= dateNaissance.value) {
            this.setCustomValidity('La date de décès doit être postérieure à la date de naissance');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>
{% endblock %}
