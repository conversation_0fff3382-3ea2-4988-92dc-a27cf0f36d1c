{% extends "base.html" %}

{% block title %}Historique des Véhicules{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/history-animation.css') }}">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header py-2" style="min-height: 60px; overflow: hidden;">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique des Véhicules
                    </h4>
                    <div class="d-flex align-items-center gap-2 mx-2" style="height: 60px;">
                        <img src="{{ url_for('static', filename='images/atlas.png') }}" alt="Atlas" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                        <img src="{{ url_for('static', filename='images/m109.png') }}" alt="M109" style="height: 90px; width: auto; object-fit: contain; transform: scale(0.95);">
                        <img src="{{ url_for('static', filename='images/vvv.png') }}" alt="Véhicule" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                    </div>
                    <div style="width: 150px;"></div> <!-- Élément vide pour équilibrer la mise en page -->
                </div>
            </div>
            <div class="card-body bg-light py-3">
                <form id="searchForm" action="{{ url_for('historique') }}" method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                        {% if search_matricule %}
                        <a href="{{ url_for('historique') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Effacer
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
            <div class="card-body">
                {% if search_matricule and not vehicule %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>Aucun véhicule trouvé avec le matricule "{{ search_matricule }}"
                </div>
                {% endif %}

                {% if vehicule %}
                <div class="vehicle-detail mb-4">
                    <h4 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-car me-2"></i>Informations du Véhicule
                    </h4>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="bg-light">Type</th>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if vehicule.type_vehicule == 'VL' else 'secondary' }}">
                                            {{ vehicule.type_vehicule }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Marque</th>
                                    <td>{{ vehicule.marque }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Matricule</th>
                                    <td>{{ vehicule.matricule }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Unité</th>
                                    <td>
                                        <span class="badge bg-info text-dark">
                                            {{ vehicule.unite if vehicule.unite else 'Non assigné' }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="bg-light">Statut actuel</th>
                                    <td>
                                        <span class="badge bg-{{ 'success' if vehicule.statut == 'Réparé' else ('warning text-dark' if vehicule.statut == 'Indisponible' else ('info' if vehicule.statut == 'En réparation' else 'danger')) }}">
                                            {{ vehicule.statut }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Panne actuelle</th>
                                    <td>
                                        <span class="badge bg-warning text-dark">
                                            {{ vehicule.type_panne }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Date de panne</th>
                                    <td>{{ vehicule.date_panne }}</td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Description</th>
                                    <td>{{ vehicule.description if vehicule.description else 'Aucune description' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="historique-detail">
                    <h4 class="border-bottom pb-2 mb-3">
                        <i class="fas fa-history me-2"></i>Historique des Changements de Statut
                    </h4>
                    {% if historique %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Type de panne</th>
                                    <th>Statut</th>
                                    <th>Date de changement</th>
                                    <th>Date de panne</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% set current_panne = '' %}
                                {% set current_panne_date = None %}
                                {% for entry in historique %}
                                    {% if entry.type_panne != current_panne or entry.panne_date != current_panne_date %}
                                        {% set current_panne = entry.type_panne %}
                                        {% set current_panne_date = entry.panne_date %}
                                        <!-- Ligne d'espacement entre les pannes (si ce n'est pas la première panne) -->
                                        {% if not loop.first %}
                                        <tr>
                                            <td colspan="4" style="height: 20px;"></td>
                                        </tr>
                                        {% endif %}
                                    {% endif %}
                                    <tr class="fade-in" style="animation-delay: {{ loop.index0 * 0.1 }}s">
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                {{ entry.type_panne }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if entry.statut == 'Réparé' else ('warning text-dark' if entry.statut == 'Indisponible' else ('info' if entry.statut == 'En réparation' else 'danger')) }}">
                                                {{ entry.statut }}
                                            </span>
                                        </td>
                                        <td><strong>{{ entry.date_changement.strftime('%d/%m/%Y') }}</strong></td>
                                        <td>{{ entry.date_panne.strftime('%d/%m/%Y') if entry.date_panne else '-' }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucun historique disponible pour ce véhicule
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <div class="mb-4">
                        <div id="history-animation" class="history-animation-container">
                            <!-- Animation CSS de secours qui sera toujours visible -->
                            <div class="history-icon-css">
                                <div class="circle"></div>
                                <div class="arrow"></div>
                                <div class="dot"></div>
                            </div>
                        </div>
                    </div>
                    <h3 style="color: #198754; font-weight: 600;">Recherchez un véhicule par matricule</h3>
                    <p class="text-muted mb-4">Entrez le matricule d'un véhicule pour consulter son historique complet</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Bibliothèque Lottie pour les animations 3D -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
<script src="{{ url_for('static', filename='js/history-animation.js') }}"></script>
<script>
    // Animation des lignes du tableau
    document.addEventListener('DOMContentLoaded', function() {
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            setTimeout(() => {
                row.style.opacity = '1';
            }, index * 100);
        });
    });
</script>
{% endblock %}
