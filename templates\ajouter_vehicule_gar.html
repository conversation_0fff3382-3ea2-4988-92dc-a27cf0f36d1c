{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <h2>Ajouter un véhicule GAR</h2>
    <form method="POST" action="{{ url_for('ajouter_vehicule_gar') }}">
        <div class="form-group">
            <label for="matricule">Matricule</label>
            <input type="text" class="form-control" id="matricule" name="matricule" required>
        </div>
        <div class="form-group">
            <label for="unite">Unité</label>
            <select class="form-control" id="unite" name="unite" required>
                {% for i in range(1, 27) %}
                    <option value="{{ i }}GAR">{{ i }}GAR</option>
                {% endfor %}
            </select>
        </div>
        <div class="form-group">
            <label for="type_vehicule">Type de véhicule</label>
            <select class="form-control" id="type_vehicule" name="type_vehicule" required onchange="updateMarques()">
                <option value="VL">VL</option>
                <option value="PL">PL</option>
                <option value="SA">SA</option>
                <option value="Engin chenillé">Engin chenillé</option>
            </select>
        </div>
        <div class="form-group">
            <label for="marque">Marque</label>
            <select class="form-control" id="marque" name="marque" required>
                <!-- Les options seront remplies dynamiquement par JavaScript -->
            </select>
        </div>
        <button type="submit" class="btn btn-primary mt-3">Ajouter</button>

        <script>
            const TYPES_VEHICULES = {
                'VL': ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan'],
                'PL': ['JBD', 'Kaiser', 'Renault'],
                'Engin chenillé': ['M109 155mm', 'M110 203mm', 'Vulcan'],
                'SA': ['PULS', 'HIMARS', 'CESAR']
            };

            function updateMarques() {
                const typeVehicule = document.getElementById('type_vehicule').value;
                const marqueSelect = document.getElementById('marque');
                marqueSelect.innerHTML = ''; // Vider les options actuelles
                
                const marques = TYPES_VEHICULES[typeVehicule] || [];
                marques.forEach(marque => {
                    const option = document.createElement('option');
                    option.value = marque;
                    option.textContent = marque;
                    marqueSelect.appendChild(option);
                });

                // Déclencher l'événement change pour mettre à jour les options au chargement
                if (marques.length > 0) {
                    marqueSelect.value = marques[0];
                }
            }

            // Initialiser les marques au chargement de la page
            document.addEventListener('DOMContentLoaded', updateMarques);
        </script>
    </form>
</div>
{% endblock %}
