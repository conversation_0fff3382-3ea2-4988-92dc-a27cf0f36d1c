{% extends "stages/base_stages.html" %}

{% block title %}{{ action }} une Promotion{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{{ action }} une Promotion</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="nom" class="form-label">Nom de la Promotion</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="{{ promotion.nom if promotion else '' }}" required>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="annee" class="form-label">Année</label>
                        <input type="number" class="form-control" id="annee" name="annee" value="{{ promotion.annee if promotion else '' }}" required placeholder="Ex: 2024">
                    </div>
                     <div class="col-md-3 mb-3">
                        <label for="filiere" class="form-label">Filière (Optionnel)</label>
                        <input type="text" class="form-control" id="filiere" name="filiere" value="{{ promotion.filiere if promotion else '' }}" placeholder="Ex: Infanterie">
                    </div>
                </div>
                
                <hr>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_promotions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {{ action }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 