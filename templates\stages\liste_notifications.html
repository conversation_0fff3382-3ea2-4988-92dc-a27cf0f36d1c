{% extends "stages/base_stages.html" %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Notifications</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('ajouter_notification') }}" class="btn btn-lg btn-gradient-primary shadow-lg position-relative d-flex align-items-center px-4 py-2" style="background: linear-gradient(90deg, #ff512f 0%, #dd2476 100%); color: #fff; border: none; transition: transform 0.2s, box-shadow 0.2s; font-size: 1.2rem;" data-bs-toggle="tooltip" data-bs-placement="left" title="Créer une nouvelle notification">
                <span class="me-2 animate__animated animate__tada animate__infinite infinite">
                    <i class="fas fa-plus-circle fa-lg"></i>
                </span>
                <span>Ajouter une Notification</span>
            </a>
        </div>
    </div>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Stagiaire</th>
                            <th>Contenu</th>
                            <th>Date d'envoi</th>
                            <th>Lue</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for notif in notifications %}
                        <tr>
                            <td>{{ notif.stagiaire.nom }} {{ notif.stagiaire.prenom }}</td>
                            <td>{{ notif.contenu }}</td>
                            <td>{{ notif.date_envoi.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>{% if notif.lue %}<span class="badge bg-success">Oui</span>{% else %}<span class="badge bg-secondary">Non</span>{% endif %}</td>
                            <td class="text-center">
                                <form action="{{ url_for('supprimer_notification', id=notif.id_notif) }}" method="post" style="display:inline;" onsubmit="return confirm('Supprimer cette notification ?');">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center">Aucune notification trouvée.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });
    var btn = document.querySelector('.btn-gradient-primary');
    if(btn) {
        btn.addEventListener('mouseenter', function() {
            btn.style.transform = 'scale(1.06)';
            btn.style.boxShadow = '0 8px 24px rgba(221, 36, 118, 0.25)';
        });
        btn.addEventListener('mouseleave', function() {
            btn.style.transform = 'scale(1)';
            btn.style.boxShadow = '0 4px 12px rgba(255, 81, 47, 0.15)';
        });
    }
});
</script>
{% endblock %} 