#!/usr/bin/env python3
"""
Diagnostic complet pour identifier le problème d'affichage des courriers
"""

import requests
import json
import webbrowser
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:3000"

def print_header(title):
    """Afficher un en-tête formaté"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def test_database_connection():
    """Tester la connexion à la base de données via l'API"""
    print_header("TEST 1: CONNEXION BASE DE DONNÉES")
    
    try:
        # Test courriers arrivés
        response = requests.get(f"{BASE_URL}/api/courriers/arrives", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Courriers Arrivés: {len(data)} courriers")
            if data:
                print(f"   Premier courrier: {data[0]['id']} - {data[0]['objet'][:50]}...")
        else:
            print(f"❌ API Courriers Arrivés: Erreur {response.status_code}")
            return False
            
        # Test courriers envoyés
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Courriers Envoyés: {len(data)} courriers")
        else:
            print(f"❌ API Courriers Envoyés: Erreur {response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def test_web_pages():
    """Tester l'accessibilité des pages web"""
    print_header("TEST 2: ACCESSIBILITÉ DES PAGES WEB")
    
    pages = [
        ("/", "Page d'accueil"),
        ("/courrier/arrives", "Interface Courriers Arrivés"),
        ("/courrier/envoyes", "Interface Courriers Envoyés"),
        ("/test_simple", "Page de Test Simple"),
        ("/diagnostic", "Page de Diagnostic")
    ]
    
    all_ok = True
    for path, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name}: Accessible")
            else:
                print(f"❌ {name}: Erreur {response.status_code}")
                all_ok = False
        except Exception as e:
            print(f"❌ {name}: {e}")
            all_ok = False
    
    return all_ok

def add_test_courrier():
    """Ajouter un courrier de test pour vérifier le fonctionnement"""
    print_header("TEST 3: AJOUT D'UN COURRIER DE TEST")
    
    timestamp = int(datetime.now().timestamp())
    test_courrier = {
        "id": f"CA-DIAGNOSTIC-{timestamp}",
        "urgence": "urgent",
        "nature": "message", 
        "date_arrivee": datetime.now().strftime('%Y-%m-%d'),
        "date_signature": datetime.now().strftime('%Y-%m-%d'),
        "numero_ecrit": f"DIAG-{timestamp}",
        "expediteur": "Test Diagnostic",
        "objet": f"Courrier de diagnostic automatique - {datetime.now().strftime('%H:%M:%S')}",
        "classification": "public",
        "annotation": "Test automatique pour diagnostic",
        "divisions_action": ["technique"],
        "divisions_info": ["rh"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(test_courrier),
            timeout=10
        )
        
        if response.status_code == 201:
            print(f"✅ Courrier de test ajouté: {test_courrier['id']}")
            
            # Vérifier qu'il apparaît dans la liste
            time.sleep(1)
            response_list = requests.get(f"{BASE_URL}/api/courriers/arrives")
            if response_list.status_code == 200:
                courriers = response_list.json()
                test_found = any(c['id'] == test_courrier['id'] for c in courriers)
                if test_found:
                    print("✅ Courrier de test trouvé dans la liste API")
                    return True
                else:
                    print("❌ Courrier de test non trouvé dans la liste API")
            
        else:
            print(f"❌ Erreur lors de l'ajout: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    return False

def open_diagnostic_pages():
    """Ouvrir les pages de diagnostic dans le navigateur"""
    print_header("OUVERTURE DES PAGES DE DIAGNOSTIC")
    
    pages = [
        (f"{BASE_URL}/test_simple", "Page de Test Simple"),
        (f"{BASE_URL}/diagnostic", "Page de Diagnostic Complète"),
        (f"{BASE_URL}/courrier/arrives", "Interface Courriers Arrivés")
    ]
    
    for url, name in pages:
        print(f"🌐 Ouverture: {name}")
        webbrowser.open(url)
        time.sleep(1)

def generate_report():
    """Générer un rapport de diagnostic"""
    print_header("RAPPORT DE DIAGNOSTIC")
    
    print("📊 RÉSUMÉ DES TESTS:")
    
    # Test API
    api_ok = test_database_connection()
    
    # Test pages web
    web_ok = test_web_pages()
    
    # Test ajout courrier
    add_ok = add_test_courrier()
    
    print(f"\n🎯 RÉSULTATS:")
    print(f"   API Backend: {'✅ OK' if api_ok else '❌ PROBLÈME'}")
    print(f"   Pages Web: {'✅ OK' if web_ok else '❌ PROBLÈME'}")
    print(f"   Ajout Courrier: {'✅ OK' if add_ok else '❌ PROBLÈME'}")
    
    if api_ok and web_ok and add_ok:
        print(f"\n🎉 DIAGNOSTIC: Le backend fonctionne parfaitement !")
        print(f"   Le problème vient probablement du navigateur ou du JavaScript.")
        print(f"\n💡 SOLUTIONS RECOMMANDÉES:")
        print(f"   1. Videz le cache du navigateur (Ctrl+F5)")
        print(f"   2. Vérifiez la console JavaScript (F12)")
        print(f"   3. Testez en mode incognito")
        print(f"   4. Vérifiez que JavaScript est activé")
        
    else:
        print(f"\n🚨 DIAGNOSTIC: Problème détecté au niveau du backend !")
        print(f"   Vérifiez que l'application Flask fonctionne correctement.")
    
    return api_ok and web_ok and add_ok

def main():
    """Fonction principale de diagnostic"""
    print("🔬 DIAGNOSTIC COMPLET - SYSTÈME DE GESTION DE COURRIER")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Vérifier que l'application est accessible
    try:
        response = requests.get(BASE_URL, timeout=5)
        if response.status_code != 200:
            print(f"❌ L'application n'est pas accessible sur {BASE_URL}")
            print(f"   Vérifiez que Flask fonctionne avec: python app.py")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter à l'application: {e}")
        print(f"   Vérifiez que Flask fonctionne avec: python app.py")
        return
    
    print(f"✅ Application accessible sur {BASE_URL}")
    
    # Exécuter le diagnostic complet
    backend_ok = generate_report()
    
    # Ouvrir les pages de diagnostic
    print(f"\n🌐 Ouverture des pages de diagnostic dans le navigateur...")
    open_diagnostic_pages()
    
    print(f"\n" + "="*60)
    print(f"🏁 DIAGNOSTIC TERMINÉ")
    print(f"="*60)
    
    if backend_ok:
        print(f"\n📋 INSTRUCTIONS POUR L'UTILISATEUR:")
        print(f"1. 🌐 Les pages de diagnostic sont maintenant ouvertes")
        print(f"2. 🔍 Vérifiez la page 'Test Simple' en premier")
        print(f"3. 🛠️ Si les courriers ne s'affichent pas:")
        print(f"   • Appuyez sur F12 pour ouvrir les outils de développement")
        print(f"   • Regardez l'onglet Console pour les erreurs")
        print(f"   • Regardez l'onglet Network pour les requêtes API")
        print(f"   • Essayez Ctrl+F5 pour vider le cache")
        print(f"4. 📞 Si le problème persiste, notez les erreurs de la console")
    else:
        print(f"\n🚨 PROBLÈME BACKEND DÉTECTÉ:")
        print(f"   Redémarrez l'application Flask et relancez ce diagnostic")

if __name__ == "__main__":
    main()
