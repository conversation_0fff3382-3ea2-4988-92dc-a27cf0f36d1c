#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger la structure des tables RH
Aligner les tables avec les modèles SQLAlchemy
"""

import mysql.connector
from mysql.connector import <PERSON>rror

def fix_table_structure():
    """Corriger la structure des tables pour correspondre aux modèles SQLAlchemy"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        print("🔧 CORRECTION DE LA STRUCTURE DES TABLES RH")
        print("=" * 60)
        
        # Désactiver les contraintes de clés étrangères temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # =============================================================================
        # 1. CORRIGER LA TABLE situation_medicale
        # =============================================================================
        print("🔄 Correction de la table situation_medicale...")
        
        # Supprimer l'ancienne table
        cursor.execute("DROP TABLE IF EXISTS situation_medicale")
        
        # Recréer avec la bonne structure
        cursor.execute("""
            CREATE TABLE situation_medicale (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                aptitude ENUM('Apte', 'Inapte') NOT NULL DEFAULT 'Apte',
                date_derniere_visite DATE NULL,
                observations_generales TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table situation_medicale corrigée")
        
        # =============================================================================
        # 2. CORRIGER LA TABLE vaccination
        # =============================================================================
        print("🔄 Correction de la table vaccination...")
        
        cursor.execute("DROP TABLE IF EXISTS vaccination")
        cursor.execute("""
            CREATE TABLE vaccination (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                nom_vaccin VARCHAR(100) NOT NULL,
                date_vaccination DATE NOT NULL,
                numero_lot VARCHAR(50) NULL,
                lieu_vaccination VARCHAR(200) NULL,
                medecin_vaccinateur VARCHAR(100) NULL,
                rappel_prevu DATE NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table vaccination corrigée")
        
        # =============================================================================
        # 3. CORRIGER LA TABLE ptc
        # =============================================================================
        print("🔄 Correction de la table ptc...")
        
        cursor.execute("DROP TABLE IF EXISTS ptc")
        cursor.execute("""
            CREATE TABLE ptc (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                duree_jours INT NOT NULL,
                objet VARCHAR(500) NOT NULL,
                lieu_ptc VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table ptc corrigée")
        
        # =============================================================================
        # 4. CRÉER LA TABLE hospitalisation (manquante)
        # =============================================================================
        print("🔄 Création de la table hospitalisation...")
        
        cursor.execute("DROP TABLE IF EXISTS hospitalisation")
        cursor.execute("""
            CREATE TABLE hospitalisation (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_entree DATE NOT NULL,
                date_sortie DATE NULL,
                etablissement VARCHAR(200) NOT NULL,
                motif VARCHAR(500) NOT NULL,
                diagnostic TEXT NULL,
                traitement TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table hospitalisation créée")
        
        # =============================================================================
        # 5. CORRIGER LES AUTRES TABLES AVEC MATRICULE
        # =============================================================================
        
        # Corriger conjoint
        print("🔄 Correction de la table conjoint...")
        cursor.execute("DROP TABLE IF EXISTS enfant")  # Supprimer d'abord enfant (dépendance)
        cursor.execute("DROP TABLE IF EXISTS conjoint")
        cursor.execute("""
            CREATE TABLE conjoint (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(80) NOT NULL,
                prenom VARCHAR(80) NOT NULL,
                nom_ar VARCHAR(80) NOT NULL,
                prenom_ar VARCHAR(80) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                lieu_naissance_ar VARCHAR(100) NOT NULL,
                adresse VARCHAR(200) NOT NULL,
                adresse_ar VARCHAR(200) NOT NULL,
                date_mariage DATE NOT NULL,
                lieu_mariage VARCHAR(100) NOT NULL,
                profession VARCHAR(100) NOT NULL,
                profession_ar VARCHAR(100) NOT NULL,
                cin_numero VARCHAR(20) NOT NULL,
                gsm VARCHAR(20) NOT NULL,
                nom_pere VARCHAR(80) NOT NULL,
                prenom_pere VARCHAR(80) NOT NULL,
                nom_pere_ar VARCHAR(80) NOT NULL,
                prenom_pere_ar VARCHAR(80) NOT NULL,
                nom_mere VARCHAR(80) NOT NULL,
                prenom_mere VARCHAR(80) NOT NULL,
                nom_mere_ar VARCHAR(80) NOT NULL,
                prenom_mere_ar VARCHAR(80) NOT NULL,
                profession_pere VARCHAR(100) NOT NULL,
                profession_mere VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table conjoint corrigée")
        
        # Recréer enfant
        cursor.execute("""
            CREATE TABLE enfant (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(80) NOT NULL,
                prenom VARCHAR(80) NOT NULL,
                genre_id INT,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                date_deces DATE NULL,
                lieu_deces VARCHAR(100) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (genre_id) REFERENCES referentiel_genre(id_genre)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table enfant corrigée")
        
        # Corriger les tables d'absences
        print("🔄 Correction des tables d'absences...")
        
        cursor.execute("DROP TABLE IF EXISTS absence_desertion")
        cursor.execute("DROP TABLE IF EXISTS absence_detachement") 
        cursor.execute("DROP TABLE IF EXISTS absence_permission")
        
        # Créer table permission unifiée
        cursor.execute("""
            CREATE TABLE permission (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                type_absence_id INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                duree_jours INT NOT NULL,
                adresse_permission VARCHAR(200) NOT NULL,
                numero_serie VARCHAR(50) NOT NULL,
                motif VARCHAR(500) NOT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (type_absence_id) REFERENCES referentiel_type_absence(id_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table permission créée")
        
        # Corriger historique_grade
        print("🔄 Correction de la table historique_grade...")
        cursor.execute("DROP TABLE IF EXISTS historique_grade")
        cursor.execute("""
            CREATE TABLE historique_grade (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                grade_id INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (grade_id) REFERENCES referentiel_grade(id_grade)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table historique_grade corrigée")
        
        # Corriger personnel_langue
        print("🔄 Correction de la table personnel_langue...")
        cursor.execute("DROP TABLE IF EXISTS personnel_langue")
        cursor.execute("""
            CREATE TABLE personnel_langue (
                matricule VARCHAR(20),
                langue_id INT,
                niveau ENUM('Débutant', 'Intermédiaire', 'Avancé', 'Natif') DEFAULT 'Intermédiaire',
                PRIMARY KEY (matricule, langue_id),
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (langue_id) REFERENCES referentiel_langue(id_langue)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table personnel_langue corrigée")
        
        # Réactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n🎉 CORRECTION TERMINÉE AVEC SUCCÈS !")
        print("✅ Toutes les tables sont maintenant alignées avec les modèles SQLAlchemy")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur lors de la correction : {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CORRECTION DE LA STRUCTURE DES TABLES RH")
    print("Alignement avec les modèles SQLAlchemy")
    print()
    
    if fix_table_structure():
        print("\n✅ Structure corrigée avec succès !")
        print("🎯 Les tables sont maintenant compatibles avec l'application Flask")
    else:
        print("\n❌ Échec de la correction")

if __name__ == "__main__":
    main()
