{% extends "stages/base_stages.html" %}

{% block title %}Détails de la Promotion{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Détails de la Promotion: {{ promotion.nom }}</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('liste_promotions') }}" class="btn btn-sm btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>
                Retour aux promotions
            </a>
            <a href="#" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-user-plus me-1"></i>
                Associer un Stagiaire
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            Informations sur la promotion
        </div>
        <div class="card-body">
            <p><strong>Année:</strong> {{ promotion.annee }}</p>
            <p><strong>Filière:</strong> {{ promotion.filiere if promotion.filiere else 'Non spécifiée' }}</p>
            <p><strong>Nombre de stagiaires:</strong> {{ promotion.inscriptions|length }}</p>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            Stagiaires de la promotion
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">Photo</th>
                            <th scope="col">Nom</th>
                            <th scope="col">Prénom</th>
                            <th scope="col">Email</th>
                            <th scope="col" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inscription in promotion.inscriptions %}
                        <tr>
                            <td>
                                {% if inscription.stagiaire.photo %}
                                    <img src="{{ url_for('uploaded_file', filename=inscription.stagiaire.photo) }}" alt="Photo" class="img-thumbnail" style="width: 50px; height: 50px;">
                                {% else %}
                                    <img src="{{ url_for('static', filename='images/default_avatar.png') }}" alt="Avatar" class="img-thumbnail" style="width: 50px; height: 50px;">
                                {% endif %}
                            </td>
                            <td>{{ inscription.stagiaire.nom }}</td>
                            <td>{{ inscription.stagiaire.prenom }}</td>
                            <td>{{ inscription.stagiaire.email }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('modifier_stagiaire', id=inscription.stagiaire.id_stagiaire) }}" class="btn btn-warning btn-sm" title="Modifier le stagiaire">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="#" method="post" style="display:inline;">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Retirer de la promotion">
                                        <i class="fas fa-user-minus"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center">Aucun stagiaire dans cette promotion pour le moment.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 