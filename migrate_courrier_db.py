#!/usr/bin/env python3
"""
Script de migration pour créer/mettre à jour les tables de courrier
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime

# Configuration de la base de données
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'gestion_vehicules'
}

def create_courrier_tables():
    """Créer les tables de courrier avec la bonne structure"""
    
    # SQL pour créer la table courrier_arrive
    create_courrier_arrive_sql = """
    CREATE TABLE IF NOT EXISTS courrier_arrive (
        id INT AUTO_INCREMENT PRIMARY KEY,
        id_courrier VARCHAR(50) NOT NULL UNIQUE,
        urgence ENUM('extreme', 'extreme_urgent', 'urgent', 'routine') NOT NULL,
        nature ENUM('message', 'nds', 'note_royale', 'decision') NOT NULL,
        date_arrivee DATE NOT NULL,
        date_signature DATE,
        numero_ecrit VARCHAR(100) NOT NULL,
        expediteur VARCHAR(200) NOT NULL,
        objet TEXT NOT NULL,
        classification ENUM('public', 'restreint', 'confidentiel', 'secret'),
        annotation TEXT,
        date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    # SQL pour créer la table courrier_envoye
    create_courrier_envoye_sql = """
    CREATE TABLE IF NOT EXISTS courrier_envoye (
        id INT AUTO_INCREMENT PRIMARY KEY,
        id_courrier VARCHAR(50) NOT NULL UNIQUE,
        numero_ecrit VARCHAR(100) NOT NULL UNIQUE,
        division_emettrice ENUM('courrier', 'instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa') NOT NULL,
        date_depart DATE NOT NULL,
        nature ENUM('message', 'nds', 'note_royale', 'decision') NOT NULL,
        objet TEXT NOT NULL,
        destinataire VARCHAR(200) NOT NULL,
        observations TEXT,
        date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    # SQL pour créer la table courrier_division_action
    create_division_action_sql = """
    CREATE TABLE IF NOT EXISTS courrier_division_action (
        id INT AUTO_INCREMENT PRIMARY KEY,
        courrier_id INT NOT NULL,
        division ENUM('instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa') NOT NULL,
        FOREIGN KEY (courrier_id) REFERENCES courrier_arrive(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    # SQL pour créer la table courrier_division_info
    create_division_info_sql = """
    CREATE TABLE IF NOT EXISTS courrier_division_info (
        id INT AUTO_INCREMENT PRIMARY KEY,
        courrier_id INT NOT NULL,
        division ENUM('instruction', 'technique', 'rh', 'mcpo', 'informatique', 'planification', 'asa') NOT NULL,
        FOREIGN KEY (courrier_id) REFERENCES courrier_arrive(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    # SQL pour créer la table courrier (centralisée)
    create_courrier_sql = """
    CREATE TABLE IF NOT EXISTS courrier (
        id INT AUTO_INCREMENT PRIMARY KEY,
        reference VARCHAR(100) NOT NULL UNIQUE,
        type_courrier ENUM('arrive', 'depart') NOT NULL,
        nature ENUM('message', 'nds', 'note_royale', 'decision') NOT NULL,
        urgence ENUM('extreme', 'extreme_urgent', 'urgent', 'routine') NOT NULL,
        date_courrier DATE NOT NULL,
        date_signature DATE,
        expediteur VARCHAR(200),
        destinataire_externe VARCHAR(200),
        objet TEXT NOT NULL,
        classification ENUM('public', 'restreint', 'confidentiel', 'secret'),
        annotation TEXT,
        observations TEXT,
        statut VARCHAR(50) NOT NULL DEFAULT 'nouveau',
        date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
    
    try:
        # Connexion à la base de données
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("🔗 Connexion à la base de données réussie")
        
        # Supprimer les tables existantes si elles existent (pour éviter les conflits)
        print("🗑️ Suppression des anciennes tables...")
        cursor.execute("DROP TABLE IF EXISTS courrier_division_info")
        cursor.execute("DROP TABLE IF EXISTS courrier_division_action")
        cursor.execute("DROP TABLE IF EXISTS courrier_arrive")
        cursor.execute("DROP TABLE IF EXISTS courrier_envoye")
        cursor.execute("DROP TABLE IF EXISTS courrier")
        
        # Créer les nouvelles tables
        print("🏗️ Création des nouvelles tables...")
        
        cursor.execute(create_courrier_arrive_sql)
        print("✅ Table courrier_arrive créée")
        
        cursor.execute(create_courrier_envoye_sql)
        print("✅ Table courrier_envoye créée")
        
        cursor.execute(create_division_action_sql)
        print("✅ Table courrier_division_action créée")
        
        cursor.execute(create_division_info_sql)
        print("✅ Table courrier_division_info créée")
        
        cursor.execute(create_courrier_sql)
        print("✅ Table courrier créée")
        
        # Insérer des données de test
        print("📝 Insertion de données de test...")
        
        # Courrier arrivé de test
        cursor.execute("""
            INSERT INTO courrier_arrive (id_courrier, urgence, nature, date_arrivee, date_signature, numero_ecrit, expediteur, objet, classification, annotation)
            VALUES ('CA-001', 'urgent', 'message', '2024-01-15', '2024-01-14', 'MSG-2024-001', 'Commandement Régional Sud', 'Demande de rapport mensuel des activités', 'restreint', 'Traitement prioritaire demandé')
        """)
        
        courrier_id = cursor.lastrowid
        
        # Divisions pour le courrier arrivé
        cursor.execute("INSERT INTO courrier_division_action (courrier_id, division) VALUES (%s, 'instruction')", (courrier_id,))
        cursor.execute("INSERT INTO courrier_division_action (courrier_id, division) VALUES (%s, 'technique')", (courrier_id,))
        cursor.execute("INSERT INTO courrier_division_info (courrier_id, division) VALUES (%s, 'rh')", (courrier_id,))
        
        # Courrier envoyé de test
        cursor.execute("""
            INSERT INTO courrier_envoye (id_courrier, numero_ecrit, division_emettrice, date_depart, nature, objet, destinataire, observations)
            VALUES ('CE-001', 'DIV-2024-001', 'instruction', '2024-01-16', 'nds', 'Réponse au rapport mensuel des activités de formation', 'Commandement Régional Sud', 'Envoi urgent demandé')
        """)
        
        connection.commit()
        print("✅ Données de test insérées")
        
        print("\n🎉 Migration terminée avec succès !")
        
    except Error as e:
        print(f"❌ Erreur lors de la migration: {e}")
        if connection:
            connection.rollback()
    
    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()
            print("🔌 Connexion fermée")

def main():
    """Fonction principale"""
    print("🚀 Début de la migration des tables de courrier")
    print("=" * 60)
    
    create_courrier_tables()
    
    print("=" * 60)
    print("🏁 Migration terminée")

if __name__ == "__main__":
    main()
