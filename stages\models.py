from datetime import datetime, date

# Mock data structures (to be replaced with actual database models later)
class Stagiaire:
    def __init__(self, id, nom, prenom, email, photo=None, date_naissance=None):
        self.id = id
        self.nom = nom
        self.prenom = prenom
        self.email = email
        self.photo = photo
        self.date_naissance = date_naissance

class Promotion:
    def __init__(self, id, annee, filiere):
        self.id = id
        self.annee = annee
        self.filiere = filiere

class Stage:
    def __init__(self, id, date_debut, date_fin, statut, type_stage):
        self.id = id
        self.date_debut = date_debut
        self.date_fin = date_fin
        self.statut = statut
        self.type_stage = type_stage

class TypeStage:
    def __init__(self, id, libelle, conditions_admission):
        self.id = id
        self.libelle = libelle
        self.conditions_admission = conditions_admission

# Mock data for testing
mock_stagiaires = [
    Stagiaire(1, "EL AMRANI", "<PERSON>", "<EMAIL>", None, date(1995, 5, 15)),
    Stagi<PERSON>(2, "B<PERSON>AL<PERSON>", "Yousse<PERSON>", "<EMAIL>", None, date(1998, 3, 21)),
    Stagiaire(3, "T<PERSON><PERSON>I", "<PERSON>", "<EMAIL>", None, date(1997, 11, 8)),
    Stagiaire(4, "IDRISSI", "Omar", "<EMAIL>", None, date(1996, 7, 12)),
    Stagiaire(5, "ALAOUI", "Karim", "<EMAIL>", None, date(1999, 2, 28)),
    Stagiaire(6, "BENJELLOUN", "Hamza", "<EMAIL>", None, date(1997, 9, 3)),
    Stagiaire(7, "ZIANI", "Amine", "<EMAIL>", None, date(1998, 6, 17)),
    Stagiaire(8, "BOUAZZAOUI", "Mehdi", "<EMAIL>", None, date(1996, 12, 5)),
    Stagiaire(9, "CHRAIBI", "Nabil", "<EMAIL>", None, date(1995, 8, 22)),
    Stagiaire(10, "FASSI", "Rachid", "<EMAIL>", None, date(1999, 4, 14))
]

mock_promotions = [
    Promotion(1, 2024, "BCM"),
    Promotion(2, 2024, "BE"),
    Promotion(3, 2024, "BS"),
    Promotion(4, 2024, "CDC")
]

mock_types_stages = [
    TypeStage(1, "BCM", "Brevet de Chef de Mission"),
    TypeStage(2, "BE", "Brevet Élémentaire"),
    TypeStage(3, "BS", "Brevet Supérieur"),
    TypeStage(4, "APPLICATION", "Stage d'Application"),
    TypeStage(5, "PERFECTIONNEMENT", "Stage de Perfectionnement"),
    TypeStage(6, "CDC", "Chef de Centre"),
    TypeStage(7, "COORDONNATEUR TACTIQUE", "Coordonnateur Tactique")
]

mock_stages = [
    Stage(1, date(2024, 1, 1), date(2024, 6, 30), "En cours", mock_types_stages[0]),  # BCM
    Stage(2, date(2024, 2, 1), date(2024, 7, 31), "Planifié", mock_types_stages[1]),  # BE
    Stage(3, date(2024, 3, 1), date(2024, 8, 31), "En cours", mock_types_stages[2]),  # BS
    Stage(4, date(2024, 2, 15), date(2024, 5, 15), "En cours", mock_types_stages[3]),  # APPLICATION
    Stage(5, date(2024, 4, 1), date(2024, 9, 30), "Planifié", mock_types_stages[4]),  # PERFECTIONNEMENT
    Stage(6, date(2024, 5, 1), date(2024, 10, 31), "Planifié", mock_types_stages[5]),  # CDC
    Stage(7, date(2024, 3, 15), date(2024, 8, 15), "En cours", mock_types_stages[6])   # COORDONNATEUR TACTIQUE
]
