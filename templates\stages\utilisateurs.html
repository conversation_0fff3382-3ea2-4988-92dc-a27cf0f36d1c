{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Gestion des Utilisateurs</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="fas fa-user-plus"></i> Nouvel Utilisateur
        </button>
    </div>
</div>

<div class="row">
    <!-- Liste des utilisateurs -->
    <div class="col-12 col-xl-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Utilisateurs</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <select class="form-select" id="filterRole">
                                <option value="">Tous les rôles</option>
                                <option value="admin">Administrateur</option>
                                <option value="responsable">Responsable</option>
                                <option value="utilisateur">Utilisateur</option>
                            </select>
                            <input type="text" class="form-control" placeholder="Rechercher...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Utilisateur</th>
                                <th>Email</th>
                                <th>Rôle</th>
                                <th>Statut</th>
                                <th>Dernière connexion</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}"
                                        class="rounded-circle me-2" width="32" height="32">
                                    <span>Jean DUPONT</span>
                                </td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-primary">Administrateur</span></td>
                                <td><span class="badge bg-success">Actif</span></td>
                                <td>15/03/2024 14:30</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Désactiver">
                                        <i class="fas fa-user-slash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}"
                                        class="rounded-circle me-2" width="32" height="32">
                                    <span>Sophie MARTIN</span>
                                </td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-info">Responsable</span></td>
                                <td><span class="badge bg-success">Actif</span></td>
                                <td>15/03/2024 12:15</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Désactiver">
                                        <i class="fas fa-user-slash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Statistiques utilisateurs -->
    <div class="col-12 col-xl-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Statistiques</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6>Répartition par rôle</h6>
                    <canvas id="roleChart" height="200"></canvas>
                </div>

                <div class="mb-4">
                    <h6>Activité utilisateurs</h6>
                    <canvas id="activityChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajout Utilisateur -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Nouvel Utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" required>
                    </div>

                    <div class="mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" required>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" required>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label">Rôle</label>
                        <select class="form-select" id="role" required>
                            <option value="">Sélectionner un rôle</option>
                            <option value="admin">Administrateur</option>
                            <option value="responsable">Responsable</option>
                            <option value="utilisateur">Utilisateur</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password" class="form-control" id="password" required>
                    </div>

                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Graphique de répartition des rôles
    new Chart(document.getElementById('roleChart'), {
        type: 'doughnut',
        data: {
            labels: ['Administrateurs', 'Responsables', 'Utilisateurs'],
            datasets: [{
                data: [2, 5, 8],
                backgroundColor: ['#0d6efd', '#0dcaf0', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Graphique d'activité
    new Chart(document.getElementById('activityChart'), {
        type: 'line',
        data: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            datasets: [{
                label: 'Connexions',
                data: [15, 12, 18, 14, 16, 8, 5],
                borderColor: '#0d6efd',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Gestion des filtres
    const filterRole = document.getElementById('filterRole');
    const searchInput = document.querySelector('input[type="text"]');

    filterRole.addEventListener('change', function() {
        // Implémenter la logique de filtrage ici
        console.log('Rôle sélectionné:', this.value);
    });

    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });

    // Validation du formulaire d'ajout d'utilisateur
    const addUserForm = document.getElementById('addUserForm');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    const saveButton = addUserForm.nextElementSibling.querySelector('.btn-primary');

    saveButton.addEventListener('click', function() {
        if (password.value !== confirmPassword.value) {
            alert('Les mots de passe ne correspondent pas');
            return;
        }

        // Simulation de sauvegarde
        const formData = {
            nom: document.getElementById('nom').value,
            prenom: document.getElementById('prenom').value,
            email: document.getElementById('email').value,
            role: document.getElementById('role').value
        };

        console.log('Nouvel utilisateur:', formData);
        
        // Fermeture de la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
        modal.hide();
    });
});
</script>
{% endblock %} 