{% extends "base_vehicules_isole.html" %}
{% block title %}Gestion du Courrier - Division Technique{% endblock %}

{% block extra_css %}
<style>
/* Styles spécifiques pour l'interface courrier véhicules */
.courrier-header {
    background: #6B8E23;
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* Désactiver le pattern de camouflage pour les en-têtes des courriers */
.courrier-header::before {
    display: none !important;
}

.courrier-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.courrier-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    border-radius: 20px 20px 0 0;
}

.courrier-tabs .nav-link.active {
    background-color: #f8f9fa;
    color: #27ae60;
    font-weight: 600;
}

.courrier-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.courrier-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.badge-urgence {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
}

.badge-action {
    background-color: #e74c3c;
    color: white;
}

.badge-info {
    background-color: #27ae60;
    color: white;
}

.btn-scan {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-scan:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.vehicule-context {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #27ae60;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête avec statistiques -->
    <div class="courrier-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    Gestion du Courrier - Division Technique (Véhicules)
                </h2>
                <p class="mb-0 mt-2">Interface de consultation et d'envoi des courriers liés aux véhicules</p>
            </div>
            <div class="col-md-4">
                <div class="courrier-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="count-recus">0</span>
                        <span class="stat-label">Reçus</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="count-action">0</span>
                        <span class="stat-label">Pour Action</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="count-envoyes">0</span>
                        <span class="stat-label">Envoyés</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation par onglets -->
    <ul class="nav nav-tabs courrier-tabs mb-4" id="courrierTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="recus-tab" data-bs-toggle="tab" data-bs-target="#recus" type="button" role="tab">
                <i class="fas fa-inbox me-2"></i>Courriers Reçus
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="envoyer-tab" data-bs-toggle="tab" data-bs-target="#envoyer" type="button" role="tab">
                <i class="fas fa-paper-plane me-2"></i>Envoyer un Courrier
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="historique-tab" data-bs-toggle="tab" data-bs-target="#historique" type="button" role="tab">
                <i class="fas fa-history me-2"></i>Historique
            </button>
        </li>
    </ul>

    <!-- Contenu des onglets -->
    <div class="tab-content" id="courrierTabContent">
        <!-- Onglet Courriers Reçus -->
        <div class="tab-pane fade show active" id="recus" role="tabpanel">
            <div class="row">
                <!-- Filtres -->
                <div class="col-md-3">
                    <div class="courrier-card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Type</label>
                                <select class="form-select" id="filter-type">
                                    <option value="">Tous</option>
                                    <option value="action">Pour Action</option>
                                    <option value="info">Pour Information</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Urgence</label>
                                <select class="form-select" id="filter-urgence">
                                    <option value="">Toutes</option>
                                    <option value="extreme">Extrême</option>
                                    <option value="urgent">Urgent</option>
                                    <option value="routine">Routine</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Sujet véhicule</label>
                                <select class="form-select" id="filter-vehicule">
                                    <option value="">Tous</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="pieces">Pièces de rechange</option>
                                    <option value="carburant">Carburant</option>
                                    <option value="accident">Accidents</option>
                                    <option value="inspection">Inspections</option>
                                </select>
                            </div>
                            <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Liste des courriers -->
                <div class="col-md-9">
                    <div class="courrier-card">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>Courriers Reçus - Division Technique</h6>
                            <span class="badge bg-light text-dark">Total: <span id="total-recus">0</span></span>
                        </div>
                        <div class="card-body">
                            <div id="courriers-list">
                                <!-- Contenu dynamique -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Envoyer un Courrier -->
        <div class="tab-pane fade" id="envoyer" role="tabpanel">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <!-- Contexte véhicule -->
                    <div class="vehicule-context">
                        <h6><i class="fas fa-info-circle me-2"></i>Contexte Division Technique</h6>
                        <p class="mb-0">Les courriers envoyés par la Division Technique concernent généralement la maintenance, les pièces de rechange, le carburant, les inspections et les rapports d'état des véhicules.</p>
                    </div>

                    <div class="courrier-card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Nouveau Courrier à Envoyer</h6>
                        </div>
                        <div class="card-body">
                            <form id="form-envoyer-courrier">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ID Courrier *</label>
                                            <input type="text" class="form-control" id="id-courrier-envoyer" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">N° Écrit Division Technique *</label>
                                            <input type="text" class="form-control" id="numero-ecrit-division" required placeholder="TECH-2024-XXX">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Nature du Courrier *</label>
                                            <select class="form-select" id="nature-courrier" required>
                                                <option value="">Choisir...</option>
                                                <option value="message">Message</option>
                                                <option value="nds">NDS</option>
                                                <option value="note_royale">Note Royale</option>
                                                <option value="decision">Décision</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Sujet véhicule</label>
                                            <select class="form-select" id="sujet-vehicule">
                                                <option value="">Choisir...</option>
                                                <option value="maintenance">Maintenance préventive/corrective</option>
                                                <option value="pieces">Demande pièces de rechange</option>
                                                <option value="carburant">Gestion carburant</option>
                                                <option value="accident">Rapport d'accident</option>
                                                <option value="inspection">Rapport d'inspection</option>
                                                <option value="etat">État du parc véhicules</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Objet *</label>
                                    <textarea class="form-control" id="objet-courrier" rows="3" required placeholder="Objet du courrier lié aux véhicules..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Organe Destinataire *</label>
                                    <input type="text" class="form-control" id="destinataire-externe" required placeholder="Ex: Direction Centrale du Matériel">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Le courrier sera transmis via le module principal de gestion du courrier
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Détails techniques</label>
                                    <textarea class="form-control" id="details-techniques" rows="3" placeholder="Détails techniques spécifiques aux véhicules (modèles, numéros de série, etc.)"></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Envoyer le Courrier
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Historique -->
        <div class="tab-pane fade" id="historique" role="tabpanel">
            <div class="courrier-card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Historique des Courriers Envoyés</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Nature</th>
                                    <th>Sujet</th>
                                    <th>Objet</th>
                                    <th>Destinataire</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="historique-list">
                                <!-- Contenu dynamique -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales
let courriersRecus = [];
let courriersEnvoyes = [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    loadMockData();
    updateStats();
    renderCourriersRecus();
    renderHistorique();
    setupEvents();
});

// Charger des données de test
function loadMockData() {
    // Courriers reçus par la Division Technique
    courriersRecus = [
        {
            id: 'CA-002',
            type: 'action',
            urgence: 'urgent',
            date_arrivee: '2024-01-16',
            nature: 'message',
            numero_ecrit: 'MSG-2024-002',
            expediteur: 'Direction Centrale du Matériel',
            objet: 'Demande d\'état du parc véhicules et besoins en maintenance',
            annotation: 'Rapport demandé pour fin janvier',
            sujet_vehicule: 'maintenance',
            document_scanne: null,
            lu: false
        },
        {
            id: 'CA-004',
            type: 'info',
            urgence: 'routine',
            date_arrivee: '2024-01-17',
            nature: 'nds',
            numero_ecrit: 'NDS-2024-004',
            expediteur: 'État-Major Général',
            objet: 'Nouvelles procédures de gestion du carburant',
            annotation: 'À appliquer dès réception',
            sujet_vehicule: 'carburant',
            document_scanne: null,
            lu: true
        }
    ];

    // Courriers envoyés par la Division Technique
    courriersEnvoyes = [
        {
            id: 'CE-TECH-001',
            numero_ecrit: 'TECH-2024-001',
            date_depart: '2024-01-18',
            nature: 'message',
            sujet_vehicule: 'maintenance',
            objet: 'État mensuel du parc véhicules et planning maintenance',
            destinataire: 'Direction Centrale du Matériel',
            details_techniques: 'Rapport incluant 15 véhicules en maintenance préventive',
            statut: 'envoyé'
        }
    ];
}

// Mettre à jour les statistiques
function updateStats() {
    const totalRecus = courriersRecus.length;
    const pourAction = courriersRecus.filter(c => c.type === 'action').length;
    const totalEnvoyes = courriersEnvoyes.length;

    document.getElementById('count-recus').textContent = totalRecus;
    document.getElementById('count-action').textContent = pourAction;
    document.getElementById('count-envoyes').textContent = totalEnvoyes;
    document.getElementById('total-recus').textContent = totalRecus;
}

// Configurer les événements
function setupEvents() {
    // Filtres
    document.getElementById('filter-type').addEventListener('change', renderCourriersRecus);
    document.getElementById('filter-urgence').addEventListener('change', renderCourriersRecus);
    document.getElementById('filter-vehicule').addEventListener('change', renderCourriersRecus);

    // Formulaire d'envoi
    document.getElementById('form-envoyer-courrier').addEventListener('submit', function(e) {
        e.preventDefault();
        envoyerCourrier();
    });
}

// Rendre la liste des courriers reçus
function renderCourriersRecus() {
    const container = document.getElementById('courriers-list');
    container.innerHTML = '';

    // Appliquer les filtres
    let courriers = [...courriersRecus];

    const filterType = document.getElementById('filter-type').value;
    const filterUrgence = document.getElementById('filter-urgence').value;
    const filterVehicule = document.getElementById('filter-vehicule').value;

    if (filterType) courriers = courriers.filter(c => c.type === filterType);
    if (filterUrgence) courriers = courriers.filter(c => c.urgence === filterUrgence);
    if (filterVehicule) courriers = courriers.filter(c => c.sujet_vehicule === filterVehicule);

    if (courriers.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">Aucun courrier trouvé</div>';
        return;
    }

    courriers.forEach((courrier, index) => {
        const urgenceBadge = getUrgenceBadge(courrier.urgence);
        const typeBadge = courrier.type === 'action' ?
            '<span class="badge badge-action">POUR ACTION</span>' :
            '<span class="badge badge-info">POUR INFO</span>';

        const sujetBadge = getSujetVehiculeBadge(courrier.sujet_vehicule);
        const luIcon = courrier.lu ?
            '<i class="fas fa-envelope-open text-success" title="Lu"></i>' :
            '<i class="fas fa-envelope text-warning" title="Non lu"></i>';

        const card = `
            <div class="card mb-3 ${!courrier.lu ? 'border-warning' : ''}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-1 text-center">
                            ${luIcon}
                        </div>
                        <div class="col-md-2">
                            <strong>${courrier.id}</strong><br>
                            <small class="text-muted">${formatDate(courrier.date_arrivee)}</small>
                        </div>
                        <div class="col-md-2">
                            ${urgenceBadge}<br>
                            ${typeBadge}
                        </div>
                        <div class="col-md-2">
                            ${sujetBadge}
                        </div>
                        <div class="col-md-3">
                            <strong>${courrier.objet}</strong><br>
                            <small class="text-muted">De: ${courrier.expediteur}</small>
                        </div>
                        <div class="col-md-2 text-end">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="voirDetails(${index})" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-scan" onclick="scannerDocument(${index})" title="Scanner document">
                                <i class="fas fa-scan"></i>
                            </button>
                            ${!courrier.lu ? '<br><button class="btn btn-sm btn-outline-success mt-1" onclick="marquerLu(' + index + ')" title="Marquer comme lu"><i class="fas fa-check"></i></button>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML += card;
    });
}

// Rendre l'historique
function renderHistorique() {
    const tbody = document.getElementById('historique-list');
    tbody.innerHTML = '';

    courriersEnvoyes.forEach((courrier, index) => {
        const row = `
            <tr>
                <td><strong>${courrier.id}</strong></td>
                <td>${formatDate(courrier.date_depart)}</td>
                <td>${getNatureBadge(courrier.nature)}</td>
                <td>${getSujetVehiculeBadge(courrier.sujet_vehicule)}</td>
                <td>${courrier.objet}</td>
                <td>${courrier.destinataire}</td>
                <td><span class="badge bg-success">Envoyé</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="voirDetailsEnvoye(${index})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Envoyer un courrier
function envoyerCourrier() {
    const id = document.getElementById('id-courrier-envoyer').value;
    const numeroEcrit = document.getElementById('numero-ecrit-division').value;
    const nature = document.getElementById('nature-courrier').value;
    const sujetVehicule = document.getElementById('sujet-vehicule').value;
    const objet = document.getElementById('objet-courrier').value;
    const destinataire = document.getElementById('destinataire-externe').value;
    const detailsTechniques = document.getElementById('details-techniques').value;

    if (!id || !numeroEcrit || !nature || !objet || !destinataire) {
        alert('Veuillez remplir tous les champs obligatoires !');
        return;
    }

    const nouveauCourrier = {
        id: id,
        numero_ecrit: numeroEcrit,
        date_depart: new Date().toISOString().split('T')[0],
        nature: nature,
        sujet_vehicule: sujetVehicule,
        objet: objet,
        destinataire: destinataire,
        details_techniques: detailsTechniques,
        statut: 'envoyé'
    };

    courriersEnvoyes.push(nouveauCourrier);

    // Simulation d'envoi au module principal
    console.log('🚛 Courrier véhicules envoyé au module principal:', nouveauCourrier);

    // Réinitialiser le formulaire
    document.getElementById('form-envoyer-courrier').reset();

    // Mettre à jour l'affichage
    updateStats();
    renderHistorique();

    alert('✅ Courrier technique envoyé avec succès au module principal !');
}

// Fonctions utilitaires
function getUrgenceBadge(urgence) {
    const badges = {
        'extreme': '<span class="badge bg-danger">Extrême</span>',
        'urgent': '<span class="badge bg-warning">Urgent</span>',
        'routine': '<span class="badge bg-secondary">Routine</span>'
    };
    return badges[urgence] || '<span class="badge bg-light text-dark">-</span>';
}

function getNatureBadge(nature) {
    const badges = {
        'message': '<span class="badge bg-info">Message</span>',
        'nds': '<span class="badge bg-primary">NDS</span>',
        'note_royale': '<span class="badge bg-warning text-dark">Note Royale</span>',
        'decision': '<span class="badge bg-success">Décision</span>'
    };
    return badges[nature] || '<span class="badge bg-light text-dark">-</span>';
}

function getSujetVehiculeBadge(sujet) {
    const badges = {
        'maintenance': '<span class="badge bg-warning text-dark">Maintenance</span>',
        'pieces': '<span class="badge bg-info">Pièces</span>',
        'carburant': '<span class="badge bg-primary">Carburant</span>',
        'accident': '<span class="badge bg-danger">Accident</span>',
        'inspection': '<span class="badge bg-success">Inspection</span>',
        'etat': '<span class="badge bg-secondary">État Parc</span>'
    };
    return badges[sujet] || '<span class="badge bg-light text-dark">Général</span>';
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}

function resetFilters() {
    document.getElementById('filter-type').value = '';
    document.getElementById('filter-urgence').value = '';
    document.getElementById('filter-vehicule').value = '';
    renderCourriersRecus();
}

function marquerLu(index) {
    courriersRecus[index].lu = true;
    renderCourriersRecus();
    updateStats();
}

function scannerDocument(index) {
    alert(`📄 Scanner le document pour le courrier ${courriersRecus[index].id}\n\nFonctionnalité de scan à implémenter pour joindre le document physique au courrier électronique.`);
}

function voirDetails(index) {
    const courrier = courriersRecus[index];
    alert(`Détails du courrier ${courrier.id}:\n\nObjet: ${courrier.objet}\nExpéditeur: ${courrier.expediteur}\nSujet véhicule: ${courrier.sujet_vehicule}\nAnnotation: ${courrier.annotation}\nType: ${courrier.type.toUpperCase()}`);
}

function voirDetailsEnvoye(index) {
    const courrier = courriersEnvoyes[index];
    alert(`Détails du courrier envoyé ${courrier.id}:\n\nObjet: ${courrier.objet}\nDestinataire: ${courrier.destinataire}\nSujet: ${courrier.sujet_vehicule}\nDétails techniques: ${courrier.details_techniques || 'Aucun'}`);
}
</script>
{% endblock %}
