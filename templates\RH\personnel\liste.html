{% extends "rh/base_rh.html" %}

{% block title %}Liste du Personnel - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-users"></i>
                                Gestion du Personnel Militaire
                            </h2>
                            <small class="text-muted">Liste complète du personnel de l'Inspection de l'Artillerie</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouveau_personnel') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouveau Personnel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Nom, prénom, matricule..." value="{{ search }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">Grade</label>
                            <select name="grade" class="form-control form-control-military">
                                <option value="">Tous les grades</option>
                                {% for grade in grades %}
                                <option value="{{ grade }}" {% if grade == grade_filter %}selected{% endif %}>
                                    {{ grade }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label-military">Unité</label>
                            <select name="unite" class="form-control form-control-military">
                                <option value="">Toutes les unités</option>
                                {% for unite in unites %}
                                <option value="{{ unite }}" {% if unite == unite_filter %}selected{% endif %}>
                                    {{ unite }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">Statut</label>
                            <select name="statut" class="form-control form-control-military">
                                <option value="actif" {% if statut_filter == 'actif' %}selected{% endif %}>Actif</option>
                                <option value="inactif" {% if statut_filter == 'inactif' %}selected{% endif %}>Inactif</option>
                                <option value="" {% if statut_filter == '' %}selected{% endif %}>Tous</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-users stat-icon"></i>
                <div class="stat-number">{{ personnel.total }}</div>
                <div class="stat-label">Total Personnel</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-user-check stat-icon"></i>
                <div class="stat-number">{{ personnel.items|selectattr('statut_actif')|list|length }}</div>
                <div class="stat-label">Personnel Actif</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-star stat-icon"></i>
                <div class="stat-number">{{ personnel.items|selectattr('grade_actuel', 'match', '.*Officier.*')|list|length }}</div>
                <div class="stat-label">Officiers</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-shield-alt stat-icon"></i>
                <div class="stat-number">{{ personnel.items|selectattr('grade_actuel', 'match', '.*Sous.*')|list|length }}</div>
                <div class="stat-label">Sous-Officiers</div>
            </div>
        </div>
    </div>

    <!-- Liste du Personnel -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Liste du Personnel
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if personnel.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Photo</th>
                                    <th>Matricule</th>
                                    <th>Nom et Prénom</th>
                                    <th>Grade</th>
                                    <th>Unité</th>
                                    <th>Poste</th>
                                    <th>Date Incorporation</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for militaire in personnel.items %}
                                <tr>
                                    <td>
                                        {% if militaire.photo %}
                                        <img src="{{ url_for('static', filename='uploads/photos/' + militaire.photo) }}" 
                                             class="rounded-circle" width="40" height="40" alt="Photo">
                                        {% else %}
                                        <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px;">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-warning">{{ militaire.matricule }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ militaire.nom }} {{ militaire.prenom }}</strong>
                                            {% if militaire.nom_arabe %}
                                            <br><small class="text-muted">{{ militaire.nom_arabe }} {{ militaire.prenom_arabe }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-military">{{ militaire.grade_actuel or 'N/A' }}</span>
                                    </td>
                                    <td>{{ militaire.unite_affectation or 'N/A' }}</td>
                                    <td>{{ militaire.poste_occupe or 'N/A' }}</td>
                                    <td>
                                        {% if militaire.date_incorporation %}
                                        {{ militaire.date_incorporation.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if militaire.statut_actif %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-check"></i> Actif
                                        </span>
                                        {% else %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-times"></i> Inactif
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('rh.fiche_personnel', id=militaire.id) }}" 
                                               class="btn btn-info-military btn-sm" title="Voir la fiche">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('rh.modifier_personnel', id=militaire.id) }}" 
                                               class="btn btn-warning-military btn-sm" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-danger-military btn-sm" 
                                                    onclick="archiverPersonnel({{ militaire.id }})" title="Archiver">
                                                <i class="fas fa-archive"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun personnel trouvé</h5>
                        <p class="text-muted">Aucun personnel ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouveau_personnel') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Ajouter du Personnel
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if personnel.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if personnel.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_personnel', page=personnel.prev_num, search=search, grade=grade_filter, unite=unite_filter, statut=statut_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in personnel.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != personnel.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_personnel', page=page_num, search=search, grade=grade_filter, unite=unite_filter, statut=statut_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if personnel.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_personnel', page=personnel.next_num, search=search, grade=grade_filter, unite=unite_filter, statut=statut_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Affichage de {{ personnel.per_page * (personnel.page - 1) + 1 }} à 
                            {{ personnel.per_page * (personnel.page - 1) + personnel.items|length }} 
                            sur {{ personnel.total }} résultats
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de Confirmation d'Archivage -->
<div class="modal fade" id="archiverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: var(--card-bg); border: 1px solid var(--border-color);">
            <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title text-warning">
                    <i class="fas fa-archive"></i> Archiver le Personnel
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir archiver ce personnel ?</p>
                <p class="text-muted">Cette action changera son statut en "Inactif".</p>
            </div>
            <div class="modal-footer" style="border-top: 1px solid var(--border-color);">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger-military" id="confirmerArchivage">
                    <i class="fas fa-archive"></i> Archiver
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.page-link {
    background: rgba(45, 80, 22, 0.2);
    border-color: var(--border-color);
    color: var(--text-light);
}

.page-link:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--dark-bg);
}

.page-item.active .page-link {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: var(--dark-bg);
}

.page-item.disabled .page-link {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--border-color);
    color: var(--text-muted);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let personnelIdToArchive = null;

function archiverPersonnel(id) {
    personnelIdToArchive = id;
    const modal = new bootstrap.Modal(document.getElementById('archiverModal'));
    modal.show();
}

document.getElementById('confirmerArchivage').addEventListener('click', function() {
    if (personnelIdToArchive) {
        // Ici vous pouvez ajouter l'appel AJAX pour archiver
        fetch(`/rh/personnel/${personnelIdToArchive}/archiver`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de l\'archivage');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'archivage');
        });
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('archiverModal'));
        modal.hide();
    }
});

function exportToExcel() {
    window.location.href = '/rh/personnel/export/excel?' + new URLSearchParams(window.location.search);
}

function exportToPDF() {
    window.location.href = '/rh/personnel/export/pdf?' + new URLSearchParams(window.location.search);
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
