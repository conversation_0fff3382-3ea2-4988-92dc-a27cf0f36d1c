{% extends "rh/base_rh.html" %}

{% block title %}Nouveau Personnel - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-user-plus"></i>
                                Nouveau Personnel Militaire
                            </h2>
                            <small class="text-muted">Création d'une nouvelle fiche personnel complète</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Section 1: Informations Personnelles -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Matricule *</label>
                                <input type="text" name="matricule" class="form-control form-control-military" required>
                                <div class="invalid-feedback">Le matricule est obligatoire</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Numéro de Dossier</label>
                                <input type="text" name="numero_dossier" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Nom *</label>
                                <input type="text" name="nom" class="form-control form-control-military" required>
                                <div class="invalid-feedback">Le nom est obligatoire</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Prénom *</label>
                                <input type="text" name="prenom" class="form-control form-control-military" required>
                                <div class="invalid-feedback">Le prénom est obligatoire</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Nom en Arabe</label>
                                <input type="text" name="nom_arabe" class="form-control form-control-military" dir="rtl">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Prénom en Arabe</label>
                                <input type="text" name="prenom_arabe" class="form-control form-control-military" dir="rtl">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Naissance *</label>
                                <input type="date" name="date_naissance" class="form-control form-control-military" required>
                                <div class="invalid-feedback">La date de naissance est obligatoire</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Lieu de Naissance</label>
                                <input type="text" name="lieu_naissance" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Nationalité</label>
                                <select name="nationalite" class="form-control form-control-military">
                                    <option value="Marocaine" selected>Marocaine</option>
                                    <option value="Française">Française</option>
                                    <option value="Espagnole">Espagnole</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">CIN</label>
                                <input type="text" name="cin" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date Délivrance CIN</label>
                                <input type="date" name="date_delivrance_cin" class="form-control form-control-military">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Lieu Délivrance CIN</label>
                                <input type="text" name="lieu_delivrance_cin" class="form-control form-control-military">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Situation Familiale et Contact -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-home"></i>
                            Situation Familiale et Contact
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Situation Familiale</label>
                                <select name="situation_familiale" class="form-control form-control-military">
                                    <option value="">Sélectionner...</option>
                                    <option value="Célibataire">Célibataire</option>
                                    <option value="Marié">Marié(e)</option>
                                    <option value="Divorcé">Divorcé(e)</option>
                                    <option value="Veuf">Veuf/Veuve</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Nombre d'Enfants</label>
                                <input type="number" name="nombre_enfants" class="form-control form-control-military" min="0" value="0">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label-military">Adresse Actuelle</label>
                            <textarea name="adresse_actuelle" class="form-control form-control-military" rows="3"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Téléphone</label>
                                <input type="tel" name="telephone" class="form-control form-control-military">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Téléphone d'Urgence</label>
                                <input type="tel" name="telephone_urgence" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label-military">Email</label>
                            <input type="email" name="email" class="form-control form-control-military">
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Niveau d'Études</label>
                                <select name="niveau_etude" class="form-control form-control-military">
                                    <option value="">Sélectionner...</option>
                                    <option value="Primaire">Primaire</option>
                                    <option value="Collège">Collège</option>
                                    <option value="Lycée">Lycée</option>
                                    <option value="Baccalauréat">Baccalauréat</option>
                                    <option value="Licence">Licence</option>
                                    <option value="Master">Master</option>
                                    <option value="Doctorat">Doctorat</option>
                                    <option value="École Militaire">École Militaire</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Spécialité Civile</label>
                                <input type="text" name="specialite_civile" class="form-control form-control-military">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section 3: Informations Militaires -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt"></i>
                            Informations Militaires
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date d'Incorporation</label>
                                <input type="date" name="date_incorporation" class="form-control form-control-military">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date Fin de Service</label>
                                <input type="date" name="date_fin_service" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Grade Actuel</label>
                                <select name="grade_actuel" class="form-control form-control-military">
                                    <option value="">Sélectionner un grade...</option>
                                    {% for grade in grades %}
                                    <option value="{{ grade.nom_grade }}">{{ grade.nom_grade }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date du Grade</label>
                                <input type="date" name="date_grade" class="form-control form-control-military">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label-military">Unité d'Affectation</label>
                            <select name="unite_affectation" class="form-control form-control-military">
                                <option value="">Sélectionner une unité...</option>
                                {% for unite in unites %}
                                <option value="{{ unite }}">{{ unite }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label-military">Poste Occupé</label>
                            <input type="text" name="poste_occupe" class="form-control form-control-military">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Actions -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-save"></i>
                            Validation et Enregistrement
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-military mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Les champs marqués d'un astérisque (*) sont obligatoires.
                            Assurez-vous que toutes les informations sont correctes avant de valider.
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success-military btn-lg">
                                <i class="fas fa-save"></i> Créer la Fiche Personnel
                            </button>
                            <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>

                        <hr class="my-4">

                        <div class="text-center">
                            <h6 class="text-warning mb-3">Actions Rapides</h6>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-info-military">
                                    <i class="fas fa-users"></i> Liste Personnel
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}

.form-label-military {
    font-weight: 600;
    color: var(--accent-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn-success-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.alert-military {
    border-left: 4px solid var(--accent-color);
    background: rgba(255, 213, 79, 0.1);
    border-radius: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';

    // Validation Bootstrap
    const forms = document.querySelectorAll('.needs-validation');

    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();

                // Scroll vers le premier champ invalide
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('was-validated')) {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            }
        });
    });

    // Auto-génération du matricule
    const nomInput = document.querySelector('input[name="nom"]');
    const prenomInput = document.querySelector('input[name="prenom"]');
    const matriculeInput = document.querySelector('input[name="matricule"]');

    function generateMatricule() {
        if (nomInput.value && prenomInput.value && !matriculeInput.value) {
            const nom = nomInput.value.substring(0, 3).toUpperCase();
            const prenom = prenomInput.value.substring(0, 2).toUpperCase();
            const year = new Date().getFullYear().toString().substring(2);
            const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');

            matriculeInput.value = `${nom}${prenom}${year}${random}`;
        }
    }

    nomInput.addEventListener('blur', generateMatricule);
    prenomInput.addEventListener('blur', generateMatricule);

    // Auto-génération du numéro de dossier
    const dateIncorporationInput = document.querySelector('input[name="date_incorporation"]');
    const numeroDossierInput = document.querySelector('input[name="numero_dossier"]');

    dateIncorporationInput.addEventListener('change', function() {
        if (this.value && !numeroDossierInput.value) {
            const date = new Date(this.value);
            const year = date.getFullYear();
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

            numeroDossierInput.value = `DOS${random}/${year}`;
        }
    });

    // Calcul automatique de la date de fin de service (25 ans après incorporation)
    dateIncorporationInput.addEventListener('change', function() {
        if (this.value) {
            const dateIncorp = new Date(this.value);
            const dateFinService = new Date(dateIncorp);
            dateFinService.setFullYear(dateFinService.getFullYear() + 25);

            const dateFinInput = document.querySelector('input[name="date_fin_service"]');
            if (!dateFinInput.value) {
                dateFinInput.value = dateFinService.toISOString().split('T')[0];
            }
        }
    });

    // Animation des cartes
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });

})();

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const confirmation = confirm('Êtes-vous sûr de vouloir créer cette fiche personnel ?');
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
