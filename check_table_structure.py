#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier la structure des tables RH
"""

import mysql.connector
from mysql.connector import Error

def check_table_structure():
    """Vérifier la structure des tables problématiques"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔍 VÉRIFICATION DE LA STRUCTURE DES TABLES")
        print("=" * 50)
        
        # Tables à vérifier
        tables_a_verifier = [
            'situation_medicale',
            'personnel',
            'ptc',
            'vaccination',
            'hospitalisation'
        ]
        
        for table in tables_a_verifier:
            try:
                cursor.execute(f"DESCRIBE {table}")
                colonnes = cursor.fetchall()
                print(f"\n📋 Table '{table}' - {len(colonnes)} colonnes :")
                for col in colonnes:
                    print(f"  - {col[0]} ({col[1]}) {col[2]} {col[3]} {col[4]}")
            except Error as e:
                print(f"❌ Table '{table}' n'existe pas : {e}")
        
        cursor.close()
        connection.close()
        
    except Error as e:
        print(f"❌ Erreur de connexion : {e}")

if __name__ == "__main__":
    check_table_structure()
