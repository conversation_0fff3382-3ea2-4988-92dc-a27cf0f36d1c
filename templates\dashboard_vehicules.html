{% extends "base.html" %}

{% block title %}Tableau de Bord - Gestion des Véhicules{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card fade-in">
            <div class="card-header py-2" style="min-height: 60px; overflow: hidden;">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Tableau de Bord - Gestion des Véhicules
                    </h4>
                    <div class="d-flex align-items-center gap-2 mx-2" style="height: 60px;">
                        <img src="{{ url_for('static', filename='images/atlas.png') }}" alt="Atlas" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                        <img src="{{ url_for('static', filename='images/m109.png') }}" alt="M109" style="height: 90px; width: auto; object-fit: contain; transform: scale(0.95);">
                        <img src="{{ url_for('static', filename='images/vvv.png') }}" alt="Véhicule" style="height: 65px; width: auto; object-fit: contain; transform: scale(0.95);">
                    </div>
                    <span class="badge bg-primary" style="font-size: 0.8rem;">
                        <i class="fas fa-sync-alt me-1"></i>Mis à jour: {{ now.strftime('%d/%m/%Y %H:%M') if now else '' }}
                    </span>
                </div>
            </div>
            <div class="card-body py-2">
                <p class="text-muted small mb-1">
                    <i class="fas fa-info-circle me-1"></i>
                    Ce tableau de bord affiche les statistiques et la répartition des véhicules.
                </p>
            </div>
        </div>
    </div>
</div>

{% if total_vehicules > 0 %}
<div class="row">
    <!-- Statistiques des Véhicules -->
    <div class="col-md-2-4 mb-4">
        <div class="card fade-in dashboard-card h-100">
            <div class="card-header py-2">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-car me-2"></i>Total
                </h4>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center p-2">
                <div class="stat-circle bg-primary text-white mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                    <h3 class="mb-0">{{ total_vehicules }}</h3>
                </div>
                <p class="text-muted text-center mb-0 small">Véhicules enregistrés</p>
            </div>
        </div>
    </div>

    <div class="col-md-2-4 mb-4">
        <div class="card fade-in dashboard-card h-100">
            <div class="card-header py-2">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-car-side me-2"></i>Véhicules Légers
                </h4>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center p-2">
                <div class="stat-circle bg-info text-white mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                    <h3 class="mb-0">{{ vehicules_vl }}</h3>
                </div>
                <p class="text-muted text-center mb-0 small">Véhicules légers</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2-4 mb-4">
        <div class="card fade-in dashboard-card h-100">
            <div class="card-header py-2">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-truck me-2"></i>Véhicules Lourds
                </h4>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center p-2">
                <div class="stat-circle bg-warning text-white mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                    <h3 class="mb-0">{{ vehicules_pl }}</h3>
                </div>
                <p class="text-muted text-center mb-0 small">Véhicules lourds</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-2-4 mb-4">
        <div class="card fade-in dashboard-card h-100">
            <div class="card-header py-2">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-missile me-2"></i>Systèmes d'Armes
                </h4>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center p-2">
                <div class="stat-circle bg-danger text-white mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                    <h3 class="mb-0">{{ vehicules_sa }}</h3>
                </div>
                <p class="text-muted text-center mb-0 small">Systèmes d'armes</p>
            </div>
        </div>
    </div>

    <div class="col-md-2-4 mb-4">
        <div class="card fade-in dashboard-card h-100">
            <div class="card-header py-2">
                <h4 class="mb-0 text-center">
                    <i class="fas fa-tank me-2"></i>Engins Chenillés
                </h4>
            </div>
            <div class="card-body d-flex flex-column align-items-center justify-content-center p-2">
                <div class="stat-circle bg-success text-white mb-2" style="width: 80px; height: 80px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">
                    <h3 class="mb-0">{{ vehicules_chenilles }}</h3>
                </div>
                <p class="text-muted text-center mb-0 small">Engins chenillés</p>
            </div>
        </div>
    </div>
</div>

<style>
    /* Ajout d'une classe personnalisée pour les colonnes de largeur égale */
    .col-md-2-4 {
        position: relative;
        width: 20%;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
        -ms-flex: 0 0 20%;
        flex: 0 0 20%;
        max-width: 20%;
    }
    
    /* Ajustement pour les petits écrans */
    @media (max-width: 1200px) {
        .col-md-2-4 {
            -ms-flex: 0 0 33.333333%;
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }
    }
    
    @media (max-width: 768px) {
        .col-md-2-4 {
            -ms-flex: 0 0 50%;
            flex: 0 0 50%;
            max-width: 50%;
        }
    }
    
    @media (max-width: 576px) {
        .col-md-2-4 {
            -ms-flex: 0 0 100%;
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
</style>

<!-- Graphiques -->
<div class="row">
    <!-- Liste des unités (GAR) avec pannes -->
    <div class="col-md-4 mb-4">
        <div class="card fade-in h-100">
            <div class="card-header border-bottom-0">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Pannes par GAR
                </h4>
            </div>
            <div class="py-2 border-bottom"></div>
            <div class="card-body p-0">
                <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                    <table class="table table-sm table-hover mb-0" style="--bs-table-bg: transparent;">
                        <thead>
                            <tr style="background-color: #f8f9fa !important; border-bottom: 2px solid #dee2e6;">
                                <th class="py-3 fw-bold">GAR</th>
                                <th class="text-center py-3 fw-bold">Pannes</th>
                                <th class="text-end py-3 fw-bold">% du total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for gar in gar_list|sort(attribute='en_panne', reverse=true) %}
                            <tr>
                                <td class="text-truncate" style="max-width: 100px;" title="{{ gar.nom }}">
                                    <i class="fas fa-warehouse me-1"></i>{{ gar.nom }}
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-danger">{{ gar.en_panne }}</span>
                                </td>
                                <td class="text-end">
                                    <span class="badge bg-secondary">{{ gar.pourcentage_pannes }}%</span>
                                </td>
                            </tr>
                            {% endfor %}
                            {% if gar_list|length == 0 %}
                            <tr>
                                <td colspan="3" class="text-center text-muted py-3 small">
                                    Aucune panne
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique des marques -->
    <div class="col-md-4 mb-4">
        <div class="card fade-in">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Répartition par Marque
                </h4>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="marquesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique des statuts -->
    <div class="col-md-4 mb-4">
        <div class="card fade-in">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Répartition par Statut
                </h4>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="statutsChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Nouveau diagramme : Degré opérationnel -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>Degré Opérationnel par GAR
                </h5>
            </div>
            <div class="card-body">
                <canvas id="degreOperationnelChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card fade-in">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                <h5>Aucun véhicule enregistré</h5>
                <p class="text-muted">Ajoutez des véhicules pour voir les statistiques</p>
                <a href="{{ url_for('ajouter') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus-circle me-2"></i>Ajouter un Véhicule
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<style>
    /* Style pour désactiver les rayures du tableau */
    .table-striped tbody tr:nth-of-type(odd) {
        background-color: transparent;
    }
    
    .stat-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stat-circle:hover {
        transform: scale(1.1);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }

    .stat-circle h2 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
    }

    .chart-container {
        position: relative;
        height: 300px;
    }

    .progress {
        border-radius: 10px;
        background-color: #f0f0f0;
    }

    .progress-bar {
        border-radius: 10px;
    }

    .sticky-top {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #f8f9fa;
    }
</style>

{% if total_vehicules > 0 %}
{% block scripts %}
<script>
'use strict';

document.addEventListener('DOMContentLoaded', function() {
    // Récupérer les données depuis le template
    var marquesStats = JSON.parse('{{ marques_stats|tojson|safe }}');
    var statutsStats = JSON.parse('{{ statuts_stats|tojson|safe }}');
    // Données pour le graphique des marques
    var marquesData = {
        labels: Object.keys(marquesStats),
        datasets: [{
            label: 'Nombre de véhicules',
            data: Object.values(marquesStats),
            backgroundColor: [
                'rgba(52, 152, 219, 0.7)',
                'rgba(46, 204, 113, 0.7)',
                'rgba(155, 89, 182, 0.7)',
                'rgba(52, 73, 94, 0.7)',
                'rgba(241, 196, 15, 0.7)',
                'rgba(230, 126, 34, 0.7)'
            ],
            borderColor: [
                'rgba(52, 152, 219, 1)',
                'rgba(46, 204, 113, 1)',
                'rgba(155, 89, 182, 1)',
                'rgba(52, 73, 94, 1)',
                'rgba(241, 196, 15, 1)',
                'rgba(230, 126, 34, 1)'
            ],
            borderWidth: 1
        }]
    };

    // Données pour le graphique des statuts
    var statutsData = {
        labels: Object.keys(statutsStats),
        datasets: [{
            data: Object.values(statutsStats),
            backgroundColor: [
                'rgba(231, 76, 60, 0.7)',  // En panne - Rouge
                'rgba(243, 156, 18, 0.7)', // Indisponible - Orange
                'rgba(52, 152, 219, 0.7)', // En réparation - Bleu
                'rgba(46, 204, 113, 0.7)'  // Réparé - Vert
            ],
            borderColor: [
                'rgba(231, 76, 60, 1)',
                'rgba(243, 156, 18, 1)',
                'rgba(52, 152, 219, 1)',
                'rgba(46, 204, 113, 1)'
            ],
            borderWidth: 1
        }]
    };

    const marquesChart = new Chart(
        document.getElementById('marquesChart').getContext('2d'),
        {
            type: 'bar',
            data: marquesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        }
    );

    const statutsChart = new Chart(
        document.getElementById('statutsChart').getContext('2d'),
        {
            type: 'pie',
            data: statutsData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        }
    );

    // Données pour le graphique du degré opérationnel
    var garData = JSON.parse('{{ gar_operational|tojson|safe }}');
    
    // Coefficients de pondération par type de véhicule
    var POIDS_TYPES = {
        'VL': 1.0,          // Véhicules légers
        'PL': 1.2,          // Poids lourds (plus critique)
        'Engin chenillé': 1.5,  // Très critique
        'SA': 1.3          // Système d'arme (très critique)
    };
    
    // Préparer les données pour le graphique
    var garLabels = [];
    var garDegres = [];
    var backgroundColors = [];
    var borderColors = [];
    
    // Fonction pour calculer le score d'un véhicule en fonction de son statut
    function getScoreVehicule(statut) {
        if (!statut || statut === 'Réparé') { return 1.0; }  // Opérationnel
        if (statut === 'En panne') { return 0.0; }          // Panne - pénalité maximale
        if (statut === 'En réparation') { return 0.3; }      // En réparation - pénalité réduite
        if (statut === 'Indisponible') { return 0.2; }       // Indisponible - pénalité réduite
        return 0.8;  // Statut inconnu, considérer comme opérationnel mais avec avertissement
    }
    
    // Fonction pour obtenir la couleur en fonction du score
    function getColor(score) {
        if (score === 100) { 
            return { bg: 'rgba(40, 167, 69, 0.7)', border: 'rgb(40, 167, 69)' };  // Vert
        }
        if (score >= 75) { 
            return { bg: 'rgba(23, 162, 184, 0.7)', border: 'rgb(23, 162, 184)' };  // Bleu clair
        }
        if (score >= 50) { 
            return { bg: 'rgba(255, 193, 7, 0.7)', border: 'rgb(255, 193, 7)' };  // Jaune
        }
        return { bg: 'rgba(220, 53, 69, 0.7)', border: 'rgb(220, 53, 69)' };  // Rouge
    }
    
    // Traiter les données de chaque GAR
    if (garData) {
        Object.entries(garData).forEach(function(entry) {
            const gar = entry[0];
            const data = entry[1];
            let scoreTotal = 0;
            let scoreMax = 0;
            
            // Si c'est un nombre simple, on le traite directement
            if (typeof data === 'number') {
                const colors = getColor(data);
                garLabels.push(gar);
                garDegres.push(data);
                backgroundColors.push(colors.bg);
                borderColors.push(colors.border);
                return;
            }
            
            // Sinon, on traite les données détaillées
            if (data && data.details) {
                Object.entries(data.details).forEach(function(detail) {
                    const type = detail[0];
                    const stats = detail[1];
                    const poids = POIDS_TYPES[type] || 1.0;
                    const score = getScoreVehicule(stats.statut);
                    scoreTotal += score * poids;
                    scoreMax += 1.0 * poids;
                });
            }
            
            // Calculer le pourcentage
            const pourcentage = scoreMax > 0 ? (scoreTotal / scoreMax) * 100 : 0;
            const scoreArrondi = Math.round(pourcentage * 10) / 10;  // Arrondir à 1 décimale
            
            const colors = getColor(scoreArrondi);
            garLabels.push(gar);
            garDegres.push(scoreArrondi);
            backgroundColors.push(colors.bg);
            borderColors.push(colors.border);
        });
    }
    
    // Créer le graphique du degré opérationnel
    var degreOperationnelCtx = document.getElementById('degreOperationnelChart');
    if (degreOperationnelCtx) {
        new Chart(degreOperationnelCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: garLabels,
            datasets: [{
                label: 'Degré Opérationnel (%)',
                data: garDegres,
                backgroundColor: backgroundColors,
                borderColor: borderColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                        display: true,
                        text: 'Pourcentage (%)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'GAR'
                    },
                    ticks: {
                        autoSkip: false,
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `Degré opérationnel : ${context.raw}%`;
                        }
                    }
                },
                legend: { display: false },
                title: {
                    display: false
                }
            }
        }
        });
    }
}); // Fin de l'écouteur DOMContentLoaded
</script>
{% endblock %}
{% endif %}
{% endblock %} 