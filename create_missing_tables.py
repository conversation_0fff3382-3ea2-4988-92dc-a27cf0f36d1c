#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer les tables manquantes selon les modèles SQLAlchemy
"""

import mysql.connector
from mysql.connector import Error

def create_missing_tables():
    """Créer les tables manquantes selon les modèles SQLAlchemy"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        print("🔧 CRÉATION DES TABLES MANQUANTES")
        print("=" * 60)
        
        # Désactiver les contraintes de clés étrangères temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # =============================================================================
        # 1. TABLE DETACHEMENT
        # =============================================================================
        print("🔄 Création de la table detachement...")
        
        cursor.execute("DROP TABLE IF EXISTS detachement")
        cursor.execute("""
            CREATE TABLE detachement (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                lieu_detachement VARCHAR(200) NOT NULL,
                adresse_detachement VARCHAR(500) NULL,
                pays VARCHAR(100) NOT NULL DEFAULT 'Maroc',
                organisme_accueil VARCHAR(200) NULL,
                fonction_detachement VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table detachement créée")
        
        # =============================================================================
        # 2. TABLE DESERTION
        # =============================================================================
        print("🔄 Création de la table desertion...")
        
        cursor.execute("DROP TABLE IF EXISTS desertion")
        cursor.execute("""
            CREATE TABLE desertion (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_absence DATE NOT NULL,
                date_retour DATE NULL,
                date_arret_solde DATE NULL,
                date_prise_solde DATE NULL,
                motif TEXT NULL,
                circonstances TEXT NULL,
                sanctions TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table desertion créée")
        
        # =============================================================================
        # 3. TABLE MUTATION_INTER_BIE
        # =============================================================================
        print("🔄 Création de la table mutation_inter_bie...")
        
        cursor.execute("DROP TABLE IF EXISTS mutation_inter_bie")
        cursor.execute("""
            CREATE TABLE mutation_inter_bie (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                unite_origine_id INT NOT NULL,
                unite_destination_id INT NOT NULL,
                fonction_origine VARCHAR(200) NOT NULL,
                fonction_destination VARCHAR(200) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                numero_decision VARCHAR(50) NULL,
                motif_mutation VARCHAR(500) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (unite_origine_id) REFERENCES referentiel_unite(id_unite),
                FOREIGN KEY (unite_destination_id) REFERENCES referentiel_unite(id_unite)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table mutation_inter_bie créée")
        
        # =============================================================================
        # 4. TABLE SEJOUR_OPS
        # =============================================================================
        print("🔄 Création de la table sejour_ops...")
        
        cursor.execute("DROP TABLE IF EXISTS sejour_ops")
        cursor.execute("""
            CREATE TABLE sejour_ops (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                lieu_mission VARCHAR(200) NOT NULL,
                pays VARCHAR(100) NOT NULL DEFAULT 'Maroc',
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                type_mission VARCHAR(100) NOT NULL,
                unite_id INT NULL,
                fonction_mission VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table sejour_ops créée")
        
        # =============================================================================
        # 5. CORRIGER LA TABLE LIBERATION
        # =============================================================================
        print("🔄 Correction de la table liberation...")
        
        cursor.execute("DROP TABLE IF EXISTS liberation")
        cursor.execute("""
            CREATE TABLE liberation (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_liberation DATE NOT NULL,
                motif_liberation VARCHAR(200) NOT NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ Table liberation corrigée")
        
        # =============================================================================
        # 6. AJOUTER DES TYPES D'ABSENCE MANQUANTS
        # =============================================================================
        print("🔄 Ajout des types d'absence...")
        
        types_absence = [
            'Permission ordinaire',
            'Permission exceptionnelle', 
            'Congé de maladie',
            'Congé de maternité',
            'Congé de paternité',
            'Permission de récupération',
            'Détachement',
            'Désertion'
        ]
        
        for type_abs in types_absence:
            cursor.execute("INSERT IGNORE INTO referentiel_type_absence (libelle) VALUES (%s)", (type_abs,))
        
        print("✅ Types d'absence ajoutés")
        
        # =============================================================================
        # 7. AJOUTER DES SPÉCIALITÉS MANQUANTES
        # =============================================================================
        print("🔄 Ajout des spécialités par service...")
        
        # Récupérer les IDs des services
        cursor.execute("SELECT id_service, code_court FROM referentiel_service")
        services = dict(cursor.fetchall())
        
        specialites = [
            # Artillerie
            ('ART', 'ARTCAN', 'Artillerie de Campagne'),
            ('ART', 'ARTAA', 'Artillerie Anti-Aérienne'),
            ('ART', 'ARTCOT', 'Artillerie Côtière'),
            ('ART', 'ARTOBS', 'Observateur d\'Artillerie'),
            
            # Infanterie
            ('INF', 'INFCOM', 'Infanterie de Combat'),
            ('INF', 'INFPAR', 'Infanterie Parachutiste'),
            ('INF', 'INFMEC', 'Infanterie Mécanisée'),
            
            # Cavalerie
            ('CAV', 'CAVBLI', 'Cavalerie Blindée'),
            ('CAV', 'CAVREC', 'Cavalerie de Reconnaissance'),
            
            # Génie
            ('GEN', 'GENCOM', 'Génie de Combat'),
            ('GEN', 'GENPON', 'Génie des Ponts'),
            
            # Train
            ('TRA', 'TRALOG', 'Logistique'),
            ('TRA', 'TRATRA', 'Transport'),
            
            # Santé
            ('SAN', 'SANMED', 'Médecine'),
            ('SAN', 'SANPHA', 'Pharmacie'),
            ('SAN', 'SANVET', 'Vétérinaire'),
            
            # Administration
            ('ADM', 'ADMFIN', 'Administration Financière'),
            ('ADM', 'ADMPER', 'Administration du Personnel'),
            ('ADM', 'ADMMAT', 'Administration du Matériel')
        ]
        
        for service_code, spec_code, spec_libelle in specialites:
            if service_code in [code for id_svc, code in services.items()]:
                service_id = next(id_svc for id_svc, code in services.items() if code == service_code)
                cursor.execute("""
                    INSERT IGNORE INTO referentiel_specialite (service_id, code, libelle) 
                    VALUES (%s, %s, %s)
                """, (service_id, spec_code, spec_libelle))
        
        print("✅ Spécialités ajoutées")
        
        # Réactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("\n🎉 CRÉATION TERMINÉE AVEC SUCCÈS !")
        print("✅ Toutes les tables manquantes ont été créées")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur lors de la création : {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CRÉATION DES TABLES MANQUANTES")
    print("Selon les modèles SQLAlchemy de l'application")
    print()
    
    if create_missing_tables():
        print("\n✅ Tables créées avec succès !")
        print("🎯 L'application devrait maintenant fonctionner correctement")
    else:
        print("\n❌ Échec de la création")

if __name__ == "__main__":
    main()
