<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Système de Gestion</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-mobile.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header" style="display: flex; flex-direction: column; align-items: center;">
                <div style="display: flex; align-items: center; gap: 26px; margin-bottom: 12px;">
                    <img src="{{ url_for('static', filename='images/far.png') }}" alt="Logo FAR" class="logo" style="height: 78px; width: auto;">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo Artillerie" class="logo" style="height: 72px; width: auto;">
                </div>
                <h3 style="font-size: 1.32em; text-align: center; line-height: 1.15; margin-bottom: 0;">
                    Système de Gestion<br>de l'Artillerie
                </h3>
            </div>

            <ul class="list-unstyled components">
                <li class="active">
                    <a href="{{ url_for('index') }}">
                        <i class="fas fa-home"></i>
                        <span>Accueil</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{{ url_for('gestion_stages') }}">
                        <i class="fas fa-user-graduate"></i>
                        Gestion des Stages
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('gestion_mcpo') }}">
                        <i class="fas fa-tasks"></i>
                        <span>Gestion MCPO</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('gestion_rh') }}">
                        <i class="fas fa-users"></i>
                        <span>Gestion RH</span>
                    </a>
                </li>

                <li>
                    <a href="{{ url_for('gestion_informatique') }}">
                        <i class="fas fa-laptop"></i>
                        <span>Gestion de l'Informatique</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('gestion_planification') }}">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Gestion Planification</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('gestion_asa') }}">
                        <i class="fas fa-rocket"></i>
                        <span>Gestion ASA</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('login') }}">
                        <i class="fas fa-truck-moving"></i>
                        <span>Gestion Technique</span>
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer" style="background: rgba(30,34,45,0.92); border-top: 1px solid rgba(255,255,255,0.07); padding: 18px 0; text-align: center;">
                <a href="#" class="about-btn" style="color: #fff; font-weight: 500; font-size: 1.08em; letter-spacing: 0.01em; display: inline-flex; align-items: center; gap: 8px; transition: background 0.2s, color 0.2s; padding: 8px 22px; border-radius: 24px; background: rgba(255,255,255,0.06);">
                    <i class="fas fa-info-circle" style="color: #ffd54f; font-size: 1.3em;"></i>
                    <span>À propos</span>
                </a>
            </div>
        </nav>


        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid">
                    <!-- Horloge Numérique -->
                    <div class="digital-clock mx-auto">
                        <span class="digital-clock-time" id="digitalClockTime">00:00</span>
                        <span class="digital-clock-seconds" id="digitalClockSeconds">00</span>
                        <span class="digital-clock-ampm" id="digitalClockAMPM">AM</span>
                        <div class="digital-clock-date" id="digitalClockDate">Lundi 1 janvier</div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div class="container-fluid">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}
                <!-- Default Dashboard Content -->
                <div class="row">
                    <div class="col-12">
                        {% if message %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ message }}
                        </div>
                        {% else %}
                        <h1 class="welcome-title" style="margin-bottom: 0.5rem;">Bienvenue dans le Système de Gestion de l'Artillerie</h1>
                        <p class="welcome-text" style="margin-top: 0.2rem;">Sélectionnez une application dans le menu pour commencer.</p>
                        
                        <!-- Applications Grid -->
                        <div class="row mt-4">
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                    <h3>Gestion des Stages</h3>
                                    <p>Gestion des formations et stages</p>
                                    <a href="{{ url_for('login_stages') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <h3>Gestion MCPO</h3>
                                    <p>Mise en condition et preparation opereationnelle</p>
                                    <a href="{{ url_for('gestion_mcpo') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <h3>Gestion RH</h3>
                                    <p>Gestion des ressources humaines</p>
                                    <a href="{{ url_for('gestion_rh') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <h3>Gestion du Courrier</h3>
                                    <p>Suivi et gestion du courrier</p>
                                    <a href="{{ url_for('gestion_courrier') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>

                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-laptop"></i>
                                    </div>
                                    <h3>Gestion de l'Informatique</h3>
                                    <p>Support et maintenance informatique</p>
                                    <a href="{{ url_for('gestion_informatique') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <h3>Gestion Planification</h3>
                                    <p>Planification des activités</p>
                                    <a href="{{ url_for('gestion_planification') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-rocket"></i>
                                    </div>
                                    <h3>Gestion ASA</h3>
                                    <p>Gestion de l'artillerie sol-air</p>
                                    <a href="{{ url_for('gestion_asa') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                            <div class="col-md-3 mb-4">
                                <div class="app-card">
                                    <div class="app-icon">
                                        <i class="fas fa-truck-moving"></i>
                                    </div>
                                    <h3>Gestion Technique</h3>
                                    <p>Gestion des véhicules et maintenance</p>
                                    <a href="{{ url_for('login') }}" class="btn btn-primary">Accéder</a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/digital-clock.js') }}"></script>
    <script>
        $(document).ready(function() {
            // Code pour la carte du Maroc peut être ajouté ici si nécessaire

            // Animation des éléments au chargement
            const elements = document.querySelectorAll('.card, .stat-card, .chart-container');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                setTimeout(() => {
                    element.classList.add('fade-in');
                }, index * 100);
            });

            // Fermeture automatique des alertes après 5 secondes
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const closeButton = alert.querySelector('.btn-close');
                    if (closeButton) {
                        closeButton.click();
                    }
                }, 5000);
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>

<style>
.app-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.app-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.app-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.app-icon i {
    font-size: 2em;
    color: white;
}

.app-card h3 {
    color: var(--primary-color);
    font-size: 1.2em;
    margin-bottom: 10px;
}

.app-card p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 20px;
}

.app-card .btn {
    background: var(--primary-color);
    border: none;
    padding: 8px 20px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.app-card .btn:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* Horloge numérique ultra-moderne et sophistiquée */
.digital-clock {
    display: flex;
    align-items: center;
    justify-content: center;
    background: 
        radial-gradient(circle at 30% 20%, rgba(255, 224, 130, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 193, 7, 0.08) 0%, transparent 50%),
        linear-gradient(135deg, rgba(20, 22, 30, 0.9), rgba(40, 42, 50, 0.8));
    border-radius: 16px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 224, 130, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 20px rgba(255, 224, 130, 0.1);
    padding: 12px 20px;
    position: relative;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    overflow: hidden;
    animation: clockHologram 4s ease-in-out infinite;
    min-width: 120px;
}
.digital-clock::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 224, 130, 0.1), transparent, rgba(255, 193, 7, 0.1), transparent);
    animation: rotateHologram 8s linear infinite;
    pointer-events: none;
}
@keyframes clockHologram {
    0%, 100% { 
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 224, 130, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1),
            0 0 20px rgba(255, 224, 130, 0.1);
    }
    50% { 
        box-shadow: 
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 224, 130, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 0 30px rgba(255, 224, 130, 0.2);
    }
}
@keyframes rotateHologram {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.digital-clock-time {
    font-family: 'Orbitron', 'Roboto Mono', monospace;
    font-size: 1.9rem;
    font-weight: 800;
    background: linear-gradient(135deg, #ffe082 0%, #ffd54f 25%, #ffb300 50%, #ffd54f 75%, #ffe082 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 
        0 0 20px rgba(255, 224, 130, 0.8),
        0 0 40px rgba(255, 224, 130, 0.4),
        0 0 60px rgba(255, 224, 130, 0.2);
    letter-spacing: 0.15em;
    line-height: 1.1;
    position: relative;
    z-index: 2;
    animation: timeGlow 2s ease-in-out infinite alternate;
}
@keyframes timeGlow {
    0% { 
        text-shadow: 
            0 0 20px rgba(255, 224, 130, 0.8),
            0 0 40px rgba(255, 224, 130, 0.4),
            0 0 60px rgba(255, 224, 130, 0.2);
    }
    100% { 
        text-shadow: 
            0 0 25px rgba(255, 224, 130, 1),
            0 0 50px rgba(255, 224, 130, 0.6),
            0 0 75px rgba(255, 224, 130, 0.3);
    }
}
.digital-clock-seconds {
    font-size: 0.85rem;
    color: #fff;
    font-family: 'Orbitron', 'Roboto Mono', monospace;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 224, 130, 0.9);
    z-index: 2;
    animation: secondsFlicker 0.5s ease-in-out infinite;
    margin-left: 2px;
    position: relative;
    top: -2px;
}
@keyframes secondsFlicker {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}
.digital-clock-ampm {
    font-size: 0.85rem;
    color: #ffe082;
    font-family: 'Orbitron', 'Roboto Mono', monospace;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(255, 224, 130, 0.9);
    z-index: 2;
    animation: ampmPulse 3s ease-in-out infinite;
    margin-left: 3px;
    position: relative;
    top: -2px;
}
@keyframes ampmPulse {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 1; }
}
.digital-clock-date {
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 0.8rem;
    color: #e8e8e8;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    letter-spacing: 0.08em;
    text-shadow: 0 0 8px rgba(255, 224, 130, 0.6);
    z-index: 2;
    opacity: 0;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.digital-clock:hover .digital-clock-date {
    opacity: 1;
    transform: translateY(-15px);
}
.digital-clock-date::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(255, 224, 130, 0.1), transparent);
    border-radius: 4px;
    animation: dateScan 2s ease-in-out infinite;
    pointer-events: none;
}
@keyframes dateScan {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}
.sidebar-footer .about-btn:hover {
    background: rgba(255,213,79,0.13) !important;
    color: #ffd54f !important;
    text-decoration: none;
}
.sidebar-footer .about-btn i {
    transition: color 0.2s;
}
.sidebar-footer .about-btn:hover i {
    color: #fff176 !important;
}
</style>