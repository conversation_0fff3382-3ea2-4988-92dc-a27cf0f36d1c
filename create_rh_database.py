#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de création de la base de données RH selon architecture_rh.md
Création des tables de référence avec leur contenu exact
"""

import mysql.connector
from mysql.connector import Error
import sys

def create_connection():
    """Créer une connexion à la base de données MySQL"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            database='gestion_vehicules',
            user='root',
            password='',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        if connection.is_connected():
            print("✅ Connexion à MySQL réussie - Base de données: gestion_vehicules")
            return connection
    except Error as e:
        print(f"❌ Erreur de connexion à MySQL: {e}")
        return None

def create_reference_tables(connection):
    """Créer les 10 tables de référence selon architecture_rh.md"""
    cursor = connection.cursor()
    
    try:
        print("\n🗄️ Création des tables de référence...")
        
        # 1.1 referentiel_genre
        print("📝 Création de referentiel_genre...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_genre (
                id_genre INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(20) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.2 referentiel_categorie
        print("📝 Création de referentiel_categorie...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_categorie (
                id_categorie INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(50) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.3 referentiel_groupe_sanguin
        print("📝 Création de referentiel_groupe_sanguin...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_groupe_sanguin (
                id_groupe INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(3) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.4 referentiel_arme
        print("📝 Création de referentiel_arme...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_arme (
                id_arme INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(50) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.5 referentiel_specialite
        print("📝 Création de referentiel_specialite...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_specialite (
                id_specialite INT AUTO_INCREMENT PRIMARY KEY,
                id_arme INT NOT NULL,
                libelle VARCHAR(50) NOT NULL,
                FOREIGN KEY (id_arme) REFERENCES referentiel_arme(id_arme)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.6 referentiel_unite
        print("📝 Création de referentiel_unite...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_unite (
                id_unite INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(100) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.7 referentiel_grade
        print("📝 Création de referentiel_grade...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_grade (
                id_grade INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(50) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.8 referentiel_situation_familiale
        print("📝 Création de referentiel_situation_familiale...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_situation_familiale (
                id_sitfam INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(20) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.9 referentiel_degre_parente
        print("📝 Création de referentiel_degre_parente...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_degre_parente (
                id_degre INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(20) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 1.10 referentiel_langue
        print("📝 Création de referentiel_langue...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS referentiel_langue (
                id_langue INT AUTO_INCREMENT PRIMARY KEY,
                libelle VARCHAR(20) NOT NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        connection.commit()
        print("✅ Tables de référence créées avec succès")
        
    except Error as e:
        print(f"❌ Erreur lors de la création des tables de référence: {e}")
        connection.rollback()
        raise e
    finally:
        cursor.close()

def insert_reference_data(connection):
    """Insérer les données de référence selon architecture_rh.md"""
    cursor = connection.cursor()
    
    try:
        print("\n📊 Insertion des données de référence...")
        
        # Vider les tables avant insertion
        print("🗑️ Nettoyage des tables de référence...")
        tables_to_clean = [
            'referentiel_langue', 'referentiel_degre_parente', 'referentiel_situation_familiale',
            'referentiel_grade', 'referentiel_unite', 'referentiel_specialite', 'referentiel_arme',
            'referentiel_groupe_sanguin', 'referentiel_categorie', 'referentiel_genre'
        ]
        
        for table in tables_to_clean:
            cursor.execute(f"DELETE FROM {table}")
        
        # 1.1 referentiel_genre - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_genre...")
        genres = ['Masculin', 'Féminin']
        for genre in genres:
            cursor.execute("INSERT INTO referentiel_genre (libelle) VALUES (%s)", (genre,))
        
        # 1.2 referentiel_categorie - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_categorie...")
        categories = ['Officier', 'Officier du rang', 'Militaire du rang']
        for categorie in categories:
            cursor.execute("INSERT INTO referentiel_categorie (libelle) VALUES (%s)", (categorie,))
        
        # 1.3 referentiel_groupe_sanguin - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_groupe_sanguin...")
        groupes_sanguins = ['A+', 'A−', 'B+', 'B−', 'AB+', 'AB−', 'O+', 'O−']
        for groupe in groupes_sanguins:
            cursor.execute("INSERT INTO referentiel_groupe_sanguin (libelle) VALUES (%s)", (groupe,))
        
        # 1.4 referentiel_arme - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_arme...")
        armes = ['Artillerie', 'Blindé', 'Infanterie', 'Transmission', 'Intendance', 'Cavalerie', 'Santé', 'Matériel', 'Génie']
        for arme in armes:
            cursor.execute("INSERT INTO referentiel_arme (libelle) VALUES (%s)", (arme,))
        
        # 1.5 referentiel_specialite - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_specialite...")
        # Récupérer l'ID de l'Artillerie
        cursor.execute("SELECT id_arme FROM referentiel_arme WHERE libelle = 'Artillerie'")
        id_artillerie = cursor.fetchone()[0]
        
        specialites = ['sol-sol', 'sol-air']
        for specialite in specialites:
            cursor.execute("INSERT INTO referentiel_specialite (id_arme, libelle) VALUES (%s, %s)", (id_artillerie, specialite))
        
        # 1.6 referentiel_unite - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_unite...")
        unites = [
            '1°GAR', '2°GAR', '3°GAR', '4°GAR', '5°GAR', '6°GAR', '7°GAR', '8°GAR', '9°GAR', '10°GAR',
            '11°GAR', '12°GAR', '13°GAR', '14°GAR', '15°GAR', '16°GAR', '17°GAR', '18°GAR', '19°GAR', '20°GAR',
            '21°GAR', '22°GAR', '23°GAR', '24°GAR', '25°GAR', '26°GAR', 'Inspection de l\'Artillerie', 'ERART', 'GSA', 'CFA',
            '1°Bureau', '2°Bureau', '3°Bureau', '4°Bureau', '5°Bureau', 'Bureau de recrutement', 'Bureau courrier', 'DPO', 'PCA',
            'Etat major zone sud', 'Etat major zone EST', 'SOPTAF', 'SOPSAG', 'S.ORIENTAL', 'Autre'
        ]
        for unite in unites:
            cursor.execute("INSERT INTO referentiel_unite (libelle) VALUES (%s)", (unite,))

        # 1.7 referentiel_grade - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_grade...")
        grades = [
            'soldat 1°classe', 'soldat 2° classe', 'brigadier', 'brigadier chef', 'MDL', 'MDL Chef',
            'adjudant', 'adjudant chef', 'sous-lieutenant', 'lieutenant', 'capitaine', 'commandant', 'lt-colonel', 'colonel'
        ]
        for grade in grades:
            cursor.execute("INSERT INTO referentiel_grade (libelle) VALUES (%s)", (grade,))

        # 1.8 referentiel_situation_familiale - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_situation_familiale...")
        situations = ['Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf/Veuve']
        for situation in situations:
            cursor.execute("INSERT INTO referentiel_situation_familiale (libelle) VALUES (%s)", (situation,))

        # 1.9 referentiel_degre_parente - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_degre_parente...")
        degres = ['Père', 'Mère', 'Fils', 'Fille', 'Frère', 'Sœur', 'Grand-père', 'Grand-mère']
        for degre in degres:
            cursor.execute("INSERT INTO referentiel_degre_parente (libelle) VALUES (%s)", (degre,))

        # 1.10 referentiel_langue - EXACTEMENT selon architecture_rh.md
        print("📝 Insertion referentiel_langue...")
        langues = ['Français', 'Anglais', 'Espagnol', 'Allemand', 'Italien', 'Portugais', 'Chinois', 'Japonais', 'Russe']
        for langue in langues:
            cursor.execute("INSERT INTO referentiel_langue (libelle) VALUES (%s)", (langue,))

        connection.commit()
        print("✅ Toutes les données de référence insérées avec succès")

    except Error as e:
        print(f"❌ Erreur lors de l'insertion des données de référence: {e}")
        connection.rollback()
        raise e
    finally:
        cursor.close()

if __name__ == "__main__":
    connection = create_connection()
    if connection:
        try:
            create_reference_tables(connection)
            insert_reference_data(connection)
            print("\n🎉 Création des tables de référence terminée avec succès!")
        except Exception as e:
            print(f"\n💥 Erreur fatale: {e}")
            sys.exit(1)
        finally:
            connection.close()
            print("🔌 Connexion fermée")
    else:
        print("💥 Impossible de se connecter à la base de données")
        sys.exit(1)
