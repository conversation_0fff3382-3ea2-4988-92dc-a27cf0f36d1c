{% extends "rh/base_rh.html" %}

{% block title %}Gestion Familiale - {{ militaire.nom }} {{ militaire.prenom }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-heart me-2"></i>
                            Gestion Familiale - {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'Grade' }} {{ militaire.nom }} {{ militaire.prenom }}
                        </h4>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i> Retour à la Fiche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations du Conjoint -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-ring me-2"></i>Informations du Conjoint</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="conjoint">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Nom</label>
                                <input type="text" name="nom" class="form-control" 
                                       value="{{ conjoint.nom if conjoint else '' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Prénom</label>
                                <input type="text" name="prenom" class="form-control" 
                                       value="{{ conjoint.prenom if conjoint else '' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Date de Naissance</label>
                                <input type="date" name="date_naissance" class="form-control" 
                                       value="{{ conjoint.date_naissance if conjoint else '' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Lieu de Naissance</label>
                                <input type="text" name="lieu_naissance" class="form-control" 
                                       value="{{ conjoint.lieu_naissance if conjoint else '' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">CIN</label>
                                <input type="text" name="cin_numero" class="form-control" 
                                       value="{{ conjoint.cin_numero if conjoint else '' }}" required>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Profession</label>
                                <input type="text" name="profession" class="form-control" 
                                       value="{{ conjoint.profession if conjoint else '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">GSM</label>
                                <input type="text" name="gsm" class="form-control" 
                                       value="{{ conjoint.gsm if conjoint else '' }}">
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-info mt-4">
                                    <i class="fas fa-save"></i> 
                                    {% if conjoint %}Mettre à Jour{% else %}Ajouter{% endif %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des Enfants -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-baby me-2"></i>Enfants ({{ enfants|length }})</h5>
                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#modalNouvelEnfant">
                            <i class="fas fa-plus"></i> Ajouter Enfant
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if enfants %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom Prénom</th>
                                        <th>Genre</th>
                                        <th>Date Naissance</th>
                                        <th>Âge</th>
                                        <th>Lieu Naissance</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for enfant in enfants %}
                                    <tr>
                                        <td><strong>{{ enfant.nom }} {{ enfant.prenom }}</strong></td>
                                        <td>
                                            <span class="badge {% if enfant.genre.libelle == 'Masculin' %}bg-primary{% else %}bg-pink{% endif %}">
                                                {{ enfant.genre.libelle if enfant.genre else 'N/A' }}
                                            </span>
                                        </td>
                                        <td>{{ enfant.date_naissance.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            {% set age = (date.today() - enfant.date_naissance).days // 365 %}
                                            {{ age }} ans
                                        </td>
                                        <td>{{ enfant.lieu_naissance }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-baby fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun enfant enregistré</p>
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modalNouvelEnfant">
                                <i class="fas fa-plus"></i> Ajouter le Premier Enfant
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Nouvel Enfant -->
<div class="modal fade" id="modalNouvelEnfant" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Ajouter un Enfant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('rh.nouveau_enfant', matricule=militaire.matricule) }}">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Nom</label>
                            <input type="text" name="nom" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Prénom</label>
                            <input type="text" name="prenom" class="form-control" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Genre</label>
                            <select name="genre_id" class="form-control" required>
                                <option value="">Sélectionner</option>
                                {% for genre in genres %}
                                <option value="{{ genre.id_genre }}">{{ genre.libelle }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Date de Naissance</label>
                            <input type="date" name="date_naissance" class="form-control" required>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">Lieu de Naissance</label>
                            <input type="text" name="lieu_naissance" class="form-control" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
