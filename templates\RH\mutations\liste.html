{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Mutations - RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-exchange-alt"></i>
                                Gestion des Mutations et Affectations
                            </h2>
                            <small class="text-muted">Suivi des mutations, affectations et détachements du personnel</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouvelle_mutation') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouvelle Mutation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Nom, unité origine/destination..." value="{{ search }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label-military">Type de Mutation</label>
                            <select name="type" class="form-control form-control-military">
                                <option value="">Tous les types</option>
                                {% for type_mut in types_mutations %}
                                <option value="{{ type_mut }}" {% if type_mut == type_filter %}selected{% endif %}>
                                    {{ type_mut }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label-military">Statut</label>
                            <select name="statut" class="form-control form-control-military">
                                <option value="">Tous les statuts</option>
                                {% for statut in statuts %}
                                <option value="{{ statut }}" {% if statut == statut_filter %}selected{% endif %}>
                                    {{ statut }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-exchange-alt stat-icon"></i>
                <div class="stat-number">{{ mutations.total }}</div>
                <div class="stat-label">Total Mutations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-clock stat-icon"></i>
                <div class="stat-number">{{ mutations.items|selectattr('statut', 'equalto', 'En cours')|list|length }}</div>
                <div class="stat-label">En Cours</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-check stat-icon"></i>
                <div class="stat-number">{{ mutations.items|selectattr('statut', 'equalto', 'Approuvée')|list|length }}</div>
                <div class="stat-label">Approuvées</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-times stat-icon"></i>
                <div class="stat-number">{{ mutations.items|selectattr('statut', 'equalto', 'Refusée')|list|length }}</div>
                <div class="stat-label">Refusées</div>
            </div>
        </div>
    </div>

    <!-- Liste des Mutations -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Liste des Mutations
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if mutations.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Militaire</th>
                                    <th>Type</th>
                                    <th>Origine → Destination</th>
                                    <th>Date Demande</th>
                                    <th>Date Prévue</th>
                                    <th>Motif</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for mutation in mutations.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ mutation.militaire.nom }} {{ mutation.militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ mutation.militaire.grade_actuel or 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-military">{{ mutation.type_mutation }}</span>
                                    </td>
                                    <td>
                                        <div class="text-center">
                                            <div class="mb-1">
                                                <span class="badge badge-secondary">{{ mutation.unite_origine or 'N/A' }}</span>
                                            </div>
                                            <div class="mb-1">
                                                <i class="fas fa-arrow-down text-warning"></i>
                                            </div>
                                            <div>
                                                <span class="badge badge-info-military">{{ mutation.unite_destination or 'N/A' }}</span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if mutation.date_demande %}
                                        {{ mutation.date_demande.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if mutation.date_prevue %}
                                        {{ mutation.date_prevue.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div title="{{ mutation.motif or 'Aucun motif' }}">
                                            {{ (mutation.motif or 'Aucun motif')[:30] }}{% if mutation.motif and mutation.motif|length > 30 %}...{% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if mutation.statut == 'Approuvée' %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-check"></i> {{ mutation.statut }}
                                        </span>
                                        {% elif mutation.statut == 'Refusée' %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-times"></i> {{ mutation.statut }}
                                        </span>
                                        {% elif mutation.statut == 'En cours' %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-clock"></i> {{ mutation.statut }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-military">{{ mutation.statut or 'N/A' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info-military btn-sm" 
                                                    onclick="voirMutation({{ mutation.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if mutation.statut == 'En cours' %}
                                            <button class="btn btn-success-military btn-sm" 
                                                    onclick="approuverMutation({{ mutation.id }})" title="Approuver">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger-military btn-sm" 
                                                    onclick="refuserMutation({{ mutation.id }})" title="Refuser">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-warning-military btn-sm" 
                                                    onclick="modifierMutation({{ mutation.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune mutation trouvée</h5>
                        <p class="text-muted">Aucune mutation ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouvelle_mutation') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Créer une Mutation
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if mutations.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if mutations.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_mutations', page=mutations.prev_num, search=search, type=type_filter, statut=statut_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in mutations.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != mutations.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_mutations', page=page_num, search=search, type=type_filter, statut=statut_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if mutations.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_mutations', page=mutations.next_num, search=search, type=type_filter, statut=statut_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function voirMutation(id) {
    alert('Détails de la mutation #' + id + ' - Fonctionnalité à développer');
}

function approuverMutation(id) {
    if (confirm('Êtes-vous sûr de vouloir approuver cette mutation ?')) {
        // Simulation d'approbation
        alert('Mutation #' + id + ' approuvée - Fonctionnalité à développer');
        location.reload();
    }
}

function refuserMutation(id) {
    const motif = prompt('Motif du refus (optionnel):');
    if (motif !== null) {
        alert('Mutation #' + id + ' refusée - Fonctionnalité à développer');
        location.reload();
    }
}

function modifierMutation(id) {
    window.location.href = `/rh/mutations/${id}/modifier`;
}

function exportToExcel() {
    alert('Export Excel - Fonctionnalité à développer');
}

function exportToPDF() {
    alert('Export PDF - Fonctionnalité à développer');
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
