<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Gestion des Véhicules Militaires</title>
    <link href="https://fonts.googleapis.com/css2?family=Black+Ops+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/login-mobile.css') }}">
    <!-- Bibliothèque jsQR avec fallback -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
    <script src="https://unpkg.com/jsqr@1.4.0/dist/jsQR.min.js"></script>
    <script src="https://cdn.rawgit.com/cozmo/jsQR/master/dist/jsQR.js"></script>

    <!-- Bibliothèque face-api.js pour la détection faciale (plusieurs sources pour la redondance) -->
    <script>
        // Fonction pour charger face-api.js depuis différentes sources
        function loadFaceAPI() {
            return new Promise((resolve, reject) => {
                // Vérifier si face-api est déjà chargé
                if (window.faceapi) {
                    console.log('face-api.js déjà chargé');
                    return resolve(window.faceapi);
                }

                // Liste des CDNs à essayer
                const cdnUrls = [
                    'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js',
                    'https://unpkg.com/face-api.js@0.22.2/dist/face-api.min.js',
                    'https://cdnjs.cloudflare.com/ajax/libs/face-api.js/0.22.2/face-api.min.js'
                ];

                // Fonction pour essayer de charger depuis une URL
                function tryLoadScript(url, index) {
                    console.log(`Tentative de chargement de face-api.js depuis ${url}`);
                    const script = document.createElement('script');
                    script.src = url;
                    script.async = true;

                    script.onload = () => {
                        console.log(`face-api.js chargé avec succès depuis ${url}`);
                        resolve(window.faceapi);
                    };

                    script.onerror = () => {
                        console.warn(`Échec du chargement depuis ${url}`);
                        // Essayer la prochaine URL si disponible
                        if (index < cdnUrls.length - 1) {
                            tryLoadScript(cdnUrls[index + 1], index + 1);
                        } else {
                            reject(new Error('Impossible de charger face-api.js depuis toutes les sources'));
                        }
                    };

                    document.head.appendChild(script);
                }

                // Commencer avec la première URL
                tryLoadScript(cdnUrls[0], 0);
            });
        }

        // Charger face-api.js au chargement de la page
        window.addEventListener('DOMContentLoaded', () => {
            loadFaceAPI()
                .then(faceapi => {
                    console.log('face-api.js initialisé et prêt à être utilisé');
                })
                .catch(error => {
                    console.error('Erreur lors du chargement de face-api.js:', error);
                });
        });
    </script>

    <!-- Script pour vérifier le chargement des bibliothèques -->
    <script>
        // Vérifier si jsQR est chargé, sinon charger depuis une source alternative
        window.addEventListener('DOMContentLoaded', function() {
            console.log('Vérification de la bibliothèque jsQR...');
            if (typeof jsQR !== 'function') {
                console.warn('Tentative de chargement de jsQR depuis une source alternative...');
                var script = document.createElement('script');
                script.src = "https://cdn.jsdelivr.net/gh/cozmo/jsQR@master/dist/jsQR.js";
                document.head.appendChild(script);

                // Vérifier à nouveau après un court délai
                setTimeout(function() {
                    if (typeof jsQR !== 'function') {
                        console.error('Impossible de charger jsQR. La détection de QR code ne fonctionnera pas.');
                        var qrButton = document.getElementById('qr-button');
                        if (qrButton) {
                            qrButton.disabled = true;
                            qrButton.title = "Bibliothèque QR non disponible";
                        }
                    } else {
                        console.log('jsQR chargé avec succès!');
                    }
                }, 2000);
            } else {
                console.log('jsQR déjà chargé!');
            }
        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto Condensed', sans-serif;
        }

        body {
            min-height: 100vh;
            background: none;
            background-size: cover;
            background-position: center;
            display: flex;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(75, 83, 32, 0.3), rgba(59, 66, 25, 0.3));
            z-index: 1;
        }

        .background-slider {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 0;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .background-slider img {
            position: absolute;
            width: auto;
            height: auto;
            min-width: 100%;
            min-height: 100%;
            max-width: none;
            max-height: none;
            object-fit: contain;
            object-position: center;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }

        .background-slider img.active {
            opacity: 1;
        }

        /* Suppression du ratio d'aspect fixe pour plus de flexibilité */
        .background-slider::before {
            display: none;
        }

        .ops {
            font-family: 'Black Ops One', cursive;
            color: #ffffff;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(0,0,0,0.8);
            letter-spacing: 3px;
            font-weight: 700;
            text-transform: uppercase;
            line-height: 1.2;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* Pas d'effet d'agrandissement au survol */

        /* Effet de survol pour chaque ligne du titre */
        .ops .title-line {
            display: inline-block;
            position: relative;
            transition: all 0.3s ease;
            padding: 0 5px;
        }

        /* Pas d'effet de changement de couleur au survol */

        /* Pas d'effet de ligne qui apparaît sous le texte */

        @keyframes militaryGlow {
            0%, 100% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 10px rgba(0,0,0,0.8); }
            50% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 30px rgba(139, 115, 85, 1); }
        }

        .ops span:last-child {
            animation: militaryGlow 3s infinite;
        }

        /* Effet de particules 3D */
        .ops::before {
            content: '';
            position: absolute;
            top: -20px;
            left: -20px;
            right: -20px;
            bottom: -20px;
            background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0));
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none;
            z-index: -1;
        }

        .ops:hover::before {
            opacity: 1;
            background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0.2));
            animation: pulseGradient 2s infinite alternate;
        }

        @keyframes pulseGradient {
            0% { transform: scale(1); background: radial-gradient(circle at center, transparent 60%, rgba(139, 115, 85, 0.2)); }
            100% { transform: scale(1.1); background: radial-gradient(circle at center, transparent 60%, rgba(230, 199, 139, 0.3)); }
        }

        /* Effet de particules qui apparaissent au survol */
        .ops .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #e6c78b;
            border-radius: 50%;
            pointer-events: none;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 0 5px rgba(230, 199, 139, 0.8);
        }

        .ops:hover .particle {
            opacity: 1;
            animation: floatParticle 3s infinite ease-in-out;
        }

        @keyframes floatParticle {
            0% { transform: translateY(0) translateX(0); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateY(-30px) translateX(10px); opacity: 0; }
        }
        .military-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                repeating-linear-gradient(45deg,
                    transparent 0,
                    transparent 10px,
                    rgba(0,0,0,0.1) 10px,
                    rgba(0,0,0,0.1) 20px
                );
            pointer-events: none;
            z-index: 2;
        }

        .border-frame {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid #8b7355;
            pointer-events: none;
            z-index: 3;
        }

        .border-frame::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px dashed #4b5320;
            animation: borderPulse 2s infinite;
        }

        @keyframes borderPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        @keyframes glowPulse {
            0%, 100% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 10px rgba(0,0,0,0.8); }
            50% { text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 25px rgba(0,0,0,0.9); }
        }

        .container {
            display: flex;
            width: 100%;
            padding: 40px;
            position: relative;
            z-index: 5;
        }

        .left-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #d0d0c0;
            padding: 40px;
            position: relative;
            z-index: 6;
        }

        .left-section h1 {
            font-family: 'Black Ops One', cursive;
            font-size: 3em;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 3px;
            color: #ffffff;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(0,0,0,0.8);
        }

        .right-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding-top: 40px;
            z-index: 6;
            position: relative;
        }

        .login-box {
            background: rgba(75, 83, 32, 0.2);
            border: 4px solid #8b7355;
            padding: 30px;
            width: 100%;
            max-width: 480px;
            position: relative;
            box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 15px rgba(139, 115, 85, 0.5);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            animation: boxGlow 3s infinite alternate;
        }

        @keyframes boxGlow {
            0% { box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 15px rgba(139, 115, 85, 0.5); }
            100% { box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 25px rgba(139, 115, 85, 0.8); }
        }

        .login-box::before,
        .login-box::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            border: 3px solid #8b7355;
        }

        .login-box::before {
            top: -12px;
            left: -12px;
            border-right: none;
            border-bottom: none;
            box-shadow: -3px -3px 10px rgba(0,0,0,0.3);
        }

        .login-box::after {
            bottom: -12px;
            right: -12px;
            border-left: none;
            border-top: none;
            box-shadow: 3px 3px 10px rgba(0,0,0,0.3);
        }

        .login-header {
            text-align: center;
            margin-bottom: 25px;
            position: relative;
        }

        .login-header img {
            width: 160px;
            height: 160px;
            margin-bottom: 15px;
            filter: drop-shadow(0 0 15px rgba(139, 115, 85, 0.7));
            transition: transform 0.3s ease;
        }

        .login-header img:hover {
            transform: scale(1.05);
        }

        .login-header h2 {
            color: #d0d0c0;
            font-family: 'Black Ops One', cursive;
            font-size: 2em;
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 10px;
            position: relative;
            text-shadow: 2px 2px 0 rgba(0,0,0,0.5);
        }

        .login-header h2::after {
            content: '';
            display: block;
            width: 60%;
            height: 2px;
            background: #8b7355;
            margin: 15px auto 0;
            position: relative;
        }

        .login-header h2::before {
            content: '★';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            color: #8b7355;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 15px 15px 15px 45px;
            background: rgba(0, 0, 0, 0.1);
            border: 2px solid #8b7355;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
            font-family: 'Roboto Condensed', sans-serif;
            backdrop-filter: blur(1px);
            -webkit-backdrop-filter: blur(1px);
            box-shadow: inset 0 0 10px rgba(0,0,0,0.2);
        }

        .form-group input:focus {
            outline: none;
            border-color: #d0d0c0;
            box-shadow: 0 0 10px rgba(139, 115, 85, 0.3);
        }

        .form-group input::placeholder {
            color: rgba(208, 208, 192, 0.5);
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #e6c78b;
            font-size: 22px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5), 0 0 8px rgba(230, 199, 139, 0.6);
            transition: all 0.3s ease;
            z-index: 2;
        }

        .form-group:hover i {
            color: #f0d9a8;
            transform: translateY(-50%) scale(1.1);
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5), 0 0 15px rgba(240, 217, 168, 0.8);
        }

        /* Cercle lumineux derrière les icônes */
        .form-group i::after {
            content: '';
            position: absolute;
            width: 32px;
            height: 32px;
            background: rgba(139, 115, 85, 0.2);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: -1;
            box-shadow: 0 0 10px rgba(230, 199, 139, 0.4);
            transition: all 0.3s ease;
        }

        .form-group:hover i::after {
            background: rgba(139, 115, 85, 0.3);
            width: 36px;
            height: 36px;
            box-shadow: 0 0 15px rgba(230, 199, 139, 0.6);
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background: #4b5320;
            border: 2px solid #8b7355;
            color: #ffffff;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            overflow: hidden;
            font-family: 'Black Ops One', cursive;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, #8b7355, transparent, #8b7355);
            z-index: -1;
            animation: borderGlow 3s linear infinite;
            opacity: 0.7;
        }

        .login-btn:hover {
            background: #5a6427;
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
            text-shadow: 1px 1px 3px rgba(0,0,0,0.6);
        }

        @keyframes borderGlow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 2px solid #8b7355;
            color: #d0d0c0;
            text-align: center;
            position: relative;
            background: rgba(0,0,0,0.2);
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px dashed #8b7355;
            pointer-events: none;
        }

        .alert-danger {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.2);
        }

        .alert-success {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.2);
        }

        /* Styles pour les options d'authentification alternatives */
        .auth-options {
            margin-top: 20px;
            text-align: center;
        }

        .auth-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .divider {
            display: flex;
            align-items: center;
            margin: 15px 0;
        }

        .divider::before, .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: #8b7355;
            opacity: 0.5;
        }

        .divider span {
            padding: 0 15px;
            color: #d0d0c0;
            font-weight: 700;
            font-size: 14px;
        }

        .auth-btn {
            background: rgba(75, 83, 32, 0.7);
            border: none;
            color: #ffffff;
            padding: 14px 18px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-top: 5px;
            font-family: 'Black Ops One', cursive;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
            flex: 1;
            min-width: 120px;
            border-radius: 5px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .qr-btn {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.8), rgba(139, 115, 85, 0.8));
        }

        .facial-btn {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.8), rgba(139, 115, 85, 0.8));
        }

        .auth-btn i {
            margin-right: 10px;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .auth-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .auth-btn:hover i {
            transform: scale(1.2);
        }

        .qr-btn:hover {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.9), rgba(139, 115, 85, 0.9));
        }

        .facial-btn:hover {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.9), rgba(139, 115, 85, 0.9));
        }

        .auth-btn:active {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }



        /* Styles pour les modals d'authentification */
        .qr-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .qr-modal-content {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.3), rgba(58, 65, 93, 0.3));
            border: none;
            padding: 30px;
            width: 95%;
            max-width: 600px;
            position: relative;
            box-shadow: 0 0 40px rgba(0,0,0,0.5), 0 0 25px rgba(139, 115, 85, 0.7);
            backdrop-filter: blur(2px);
            text-align: center;
            animation: modalAppear 0.3s ease-out;
            border-radius: 15px;
            overflow: hidden;
        }

        .qr-modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            pointer-events: none;
        }

        @keyframes modalAppear {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .qr-modal-header {
            margin-bottom: 20px;
            position: relative;
        }

        .qr-modal-header h3 {
            color: #e6c78b;
            font-family: 'Black Ops One', cursive;
            font-size: 1.5em;
            text-transform: uppercase;
            letter-spacing: 2px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 0 rgba(0,0,0,0.5);
        }

        .qr-close {
            position: absolute;
            top: -15px;
            right: -15px;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.8), rgba(139, 115, 85, 0.8));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.1);
        }

        .qr-close:hover {
            transform: scale(1.1);
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.9), rgba(139, 115, 85, 0.9));
        }

        #qr-scanner-container, #facial-scanner-container {
            width: 100%;
            height: 350px;
            overflow: hidden;
            position: relative;
            margin-bottom: 20px;
            border: none;
            background: #000;
            border-radius: 12px;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.5);
        }

        #qr-video, #facial-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        #qr-canvas {
            display: none;
        }

        /* Ligne de scan animée */
        .scan-line {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, transparent, #e6c78b, #8b7355, #e6c78b, transparent);
            box-shadow: 0 0 12px rgba(230, 199, 139, 0.8);
            animation: scanAnimation 2.5s cubic-bezier(0.4, 0.0, 0.2, 1) infinite;
            z-index: 10;
            pointer-events: none;
            opacity: 0.8;
        }

        @keyframes scanAnimation {
            0% { top: 0; opacity: 0.5; }
            50% { top: 100%; opacity: 0.9; }
            100% { top: 0; opacity: 0.5; }
        }

        /* Nous utilisons la même ligne de scan pour le QR code et la reconnaissance faciale */
        /* Les styles sont déjà définis dans la classe .scan-line */

        .qr-status {
            color: #e6c78b;
            margin-top: 15px;
            font-weight: bold;
            font-size: 16px;
            padding: 12px 15px;
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.4), rgba(58, 65, 93, 0.4));
            border-radius: 8px;
            border: none;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .qr-status::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #e6c78b, #8b7355);
        }

        .error-details {
            font-size: 0.85em;
            margin-top: 8px;
            opacity: 0.9;
            font-style: italic;
        }

        @media (max-width: 1024px) {
            .container {
                flex-direction: column;
                height: auto;
                max-height: none;
                padding: 20px;
            }
            .left-section {
                padding: 20px;
                text-align: center;
            }
            .left-section h1 {
                font-size: 2em;
            }
            .right-section {
                padding: 20px;
            }
        }

        /* Styles pour le corps de la page */
        body {
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="background-slider">
        <img src="{{ url_for('static', filename='images/a6.jpg') }}" alt="Background 1" class="active">
        <img src="{{ url_for('static', filename='images/puls.jpg') }}" alt="Background 2">
        <img src="{{ url_for('static', filename='images/cesar.jpg') }}" alt="Background 3">
        <img src="{{ url_for('static', filename='images/sd.png') }}" alt="Background 4">
        <img src="{{ url_for('static', filename='images/himars.jpg') }}" alt="Background 5">
    </div>
    <div class="military-overlay"></div>
    <div class="border-frame"></div>
    <div class="container">
        <div class="left-section">
            <h1 class="ops" style="text-align: center;">
                <span class="title-line">SYSTÈME DE</span>
                <span class="title-line">GESTION DES VÉHICULES</span>
                <span class="title-line" style="display: block; font-size: 1.2em; color: #8b7355; text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(139, 115, 85, 0.6);">DE L'ARTILLERIE</span>
            </h1>

        </div>
        <div class="right-section">
            <div class="login-box">
                <div class="login-header">
                    <div style="display: flex; justify-content: center; align-items: center; gap: 40px; margin-bottom: 15px;">
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" style="width: 140px; height: auto; filter: drop-shadow(0 0 10px rgba(139, 115, 85, 0.7));">
                        <img src="{{ url_for('static', filename='images/far.png') }}" alt="FAR" style="width: 180px; height: auto; filter: drop-shadow(0 0 10px rgba(139, 115, 85, 0.7));">
                    </div>
                </div>
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                <form method="POST" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <i class="fas fa-id-card-alt"></i>
                        <input type="text" id="username" name="username" placeholder="Code d'identification" required>
                    </div>
                    <div class="form-group">
                        <i class="fas fa-key"></i>
                        <input type="password" id="password" name="password" placeholder="Code d'accès" required>
                    </div>
                    <button type="submit" class="login-btn">Accéder au système</button>
                </form>
                <div class="auth-options">
                    <div class="divider">
                        <span>MÉTHODES ALTERNATIVES</span>
                    </div>
                    <div class="auth-buttons">
                        <button id="qr-button" class="auth-btn qr-btn">
                            <i class="fas fa-qrcode me-2"></i>QR Code
                        </button>
                        <button id="facial-button" class="auth-btn facial-btn">
                            <i class="fas fa-user-circle me-2"></i>Reconnaissance Faciale
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour le scan QR -->
    <div id="qr-modal" class="qr-modal">
        <div class="qr-modal-content">
            <div class="qr-modal-header">
                <h3>Scanner votre code QR</h3>
                <div class="qr-close" id="qr-close">&times;</div>
            </div>
            <div id="qr-scanner-container">
                <video id="qr-video" playsinline></video>
                <canvas id="qr-canvas"></canvas>
                <div class="scan-line"></div>
            </div>
            <div class="qr-status" id="qr-status">En attente de la caméra...</div>
        </div>
    </div>

    <!-- Modal pour la reconnaissance faciale -->
    <div id="facial-modal" class="qr-modal">
        <div class="qr-modal-content">
            <div class="qr-modal-header">
                <h3>Reconnaissance Faciale</h3>
                <div class="qr-close" id="facial-close">&times;</div>
            </div>
            <div id="facial-scanner-container">
                <video id="facial-video" playsinline></video>
                <canvas id="facial-canvas"></canvas>
                <div class="scan-line"></div>
            </div>
            <div class="qr-status" id="facial-status">En attente de la caméra...</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.background-slider img');
            let currentIndex = 0;

            function nextImage() {
                images[currentIndex].classList.remove('active');
                currentIndex = (currentIndex + 1) % images.length;
                images[currentIndex].classList.add('active');
            }

            // Démarrer la transition automatique
            setInterval(nextImage, 2000);
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const title = document.querySelector('.ops');

            // Créer les particules
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Position aléatoire
                particle.style.top = Math.random() * 100 + '%';
                particle.style.left = Math.random() * 100 + '%';

                // Délai d'animation aléatoire
                particle.style.animationDelay = (Math.random() * 2) + 's';

                title.appendChild(particle);
            }

            // Gestion du scan QR et de la reconnaissance faciale
            const qrButton = document.getElementById('qr-button');
            const qrModal = document.getElementById('qr-modal');
            const qrClose = document.getElementById('qr-close');
            const qrVideo = document.getElementById('qr-video');
            const qrCanvas = document.getElementById('qr-canvas');
            const qrStatus = document.getElementById('qr-status');

            // Éléments pour la reconnaissance faciale
            const facialButton = document.getElementById('facial-button');
            const facialModal = document.getElementById('facial-modal');
            const facialClose = document.getElementById('facial-close');
            const facialVideo = document.getElementById('facial-video');
            const facialCanvas = document.getElementById('facial-canvas');
            const facialStatus = document.getElementById('facial-status');

            let stream = null;

            // Ouvrir la modal de scan QR
            qrButton.addEventListener('click', function() {
                qrModal.style.display = 'flex';
                startQRScan();
            });

            // Fermer la modal de scan QR
            qrClose.addEventListener('click', function() {
                qrModal.style.display = 'none';
                stopQRScan();
            });

            // Démarrer le scan QR
            function startQRScan() {
                qrStatus.textContent = 'Accès à la caméra...';

                // Vérifier si l'API MediaDevices est disponible
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    qrStatus.textContent = 'Votre navigateur ne prend pas en charge l\'accès à la caméra.';
                    console.error('MediaDevices API non supportée');
                    return;
                }

                // Accéder à la caméra
                navigator.mediaDevices.getUserMedia({ video: { facingMode: "environment" } })
                    .then(function(mediaStream) {
                        stream = mediaStream;
                        qrVideo.srcObject = mediaStream;
                        qrVideo.setAttribute('playsinline', true);
                        qrVideo.play();
                        requestAnimationFrame(tick);
                        qrStatus.textContent = 'Caméra activée. Placez un code QR devant.';
                    })
                    .catch(function(err) {
                        qrStatus.textContent = 'Erreur d\'accès à la caméra: ' + err.message;
                        console.error('Erreur d\'accès à la caméra:', err);

                        // Afficher des instructions supplémentaires en cas d'erreur
                        if (err.name === 'NotAllowedError') {
                            qrStatus.textContent += ' Veuillez autoriser l\'accès à la caméra dans les paramètres de votre navigateur.';
                        } else if (err.name === 'NotFoundError') {
                            qrStatus.textContent += ' Aucune caméra n\'a été détectée sur votre appareil.';
                        }
                    });
            }

            // Arrêter le scan QR
            function stopQRScan() {
                if (stream) {
                    stream.getTracks().forEach(track => {
                        track.stop();
                    });
                    stream = null;
                }
                qrVideo.srcObject = null;
            }

            // Gestion de la reconnaissance faciale
            facialButton.addEventListener('click', function() {
                facialModal.style.display = 'flex';
                startFacialRecognition();
            });

            facialClose.addEventListener('click', function() {
                facialModal.style.display = 'none';
                stopFacialRecognition();
            });

            // Démarrer la reconnaissance faciale
            async function startFacialRecognition() {
                facialStatus.textContent = 'Chargement des modèles de détection faciale...';

                try {
                    facialStatus.textContent = 'Initialisation de la détection faciale...';

                    // S'assurer que face-api.js est chargé
                    if (!window.faceapi) {
                        // Tenter de charger face-api.js s'il n'est pas déjà chargé
                        await loadFaceAPI();
                    }

                    // Vérifier à nouveau si face-api.js est disponible
                    if (!window.faceapi) {
                        throw new Error('face-api.js n\'est pas disponible');
                    }

                    // Charger les modèles nécessaires
                    facialStatus.textContent = 'Chargement des modèles de détection faciale...';

                    // Essayer de charger uniquement le modèle TinyFaceDetector qui est léger et rapide
                    // Utiliser une seule source fiable pour accélérer le chargement
                    const modelUrl = 'https://justadudewhohacks.github.io/face-api.js/weights';

                    let modelsLoaded = false;

                    try {
                        facialStatus.textContent = 'Chargement du modèle de détection rapide...';

                        // Configurer les options pour un chargement plus rapide
                        faceapi.env.monkeyPatch({
                            Canvas: HTMLCanvasElement,
                            Image: HTMLImageElement,
                            ImageData: ImageData,
                            Video: HTMLVideoElement,
                            createCanvasElement: () => document.createElement('canvas'),
                            createImageElement: () => document.createElement('img')
                        });

                        // Charger uniquement le modèle nécessaire
                        await faceapi.nets.tinyFaceDetector.loadFromUri(modelUrl);
                        console.log('Modèle TinyFaceDetector chargé avec succès');
                        modelsLoaded = true;
                    } catch (error) {
                        console.warn('Échec du chargement du modèle principal, tentative avec une source alternative...', error);

                        try {
                            // Essayer une source alternative
                            await faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model');
                            console.log('Modèle chargé depuis la source alternative');
                            modelsLoaded = true;
                        } catch (altError) {
                            console.error('Échec du chargement depuis la source alternative', altError);
                        }
                    }

                    // Vérifier si les modèles ont été chargés
                    if (!modelsLoaded) {
                        throw new Error('Impossible de charger les modèles de détection faciale');
                    }

                    facialStatus.textContent = 'Accès à la caméra...';

                    // Vérifier si l'API MediaDevices est disponible
                    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                        facialStatus.textContent = 'Votre navigateur ne prend pas en charge l\'accès à la caméra.';
                        console.error('MediaDevices API non supportée');
                        return;
                    }

                    // Accéder à la caméra
                    navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: "user", // Caméra frontale pour la reconnaissance faciale
                            width: { ideal: 640 },
                            height: { ideal: 480 }
                        }
                    })
                    .then(function(mediaStream) {
                        stream = mediaStream;
                        facialVideo.srcObject = mediaStream;
                        facialVideo.setAttribute('playsinline', true);
                        facialVideo.play();

                        // Démarrer la détection faciale
                        facialStatus.textContent = 'Placez votre visage dans le cadre...';

                        // Initialiser le canvas pour la détection avec une taille réduite pour de meilleures performances
                        // Utiliser une résolution plus basse pour la détection tout en gardant l'affichage de bonne qualité
                        const scaleFactor = 0.5; // Réduire la résolution de moitié pour la détection
                        facialCanvas.width = (facialVideo.videoWidth || 640) * scaleFactor;
                        facialCanvas.height = (facialVideo.videoHeight || 480) * scaleFactor;
                        facialCanvas.style.width = '100%'; // Garder la taille d'affichage à 100%
                        facialCanvas.style.height = '100%';
                        const context = facialCanvas.getContext('2d');

                        // Variable pour suivre si un visage a été détecté
                        let faceDetected = false;

                        // Variable pour contrôler la fréquence de détection
                        let lastDetectionTime = 0;
                        const detectionInterval = 100; // Vérifier toutes les 100ms au lieu d'à chaque frame

                        // Fonction pour détecter le visage
                        async function detectFace() {
                            if (!facialVideo.srcObject) return;

                            const now = Date.now();

                            // Dessiner l'image vidéo sur le canvas avec mise à l'échelle optimisée
                            context.clearRect(0, 0, facialCanvas.width, facialCanvas.height);

                            // Dessiner l'image vidéo à la résolution réduite pour une meilleure performance
                            // Utiliser drawImage avec les paramètres de mise à l'échelle pour une meilleure qualité
                            context.drawImage(
                                facialVideo,
                                0, 0, facialVideo.videoWidth, facialVideo.videoHeight,  // Source rectangle
                                0, 0, facialCanvas.width, facialCanvas.height           // Destination rectangle (scaled)
                            );

                            // N'exécuter la détection que si suffisamment de temps s'est écoulé depuis la dernière détection
                            const shouldDetect = now - lastDetectionTime >= detectionInterval;

                            try {
                                // N'exécuter la détection que si nécessaire
                                let detections = [];

                                if (shouldDetect && !faceDetected) {
                                    // Mettre à jour le temps de la dernière détection
                                    lastDetectionTime = now;

                                    // Détecter les visages dans l'image avec des paramètres optimisés pour la vitesse
                                    detections = await faceapi.detectAllFaces(
                                        facialCanvas,
                                        new faceapi.TinyFaceDetectorOptions({
                                            scoreThreshold: 0.3,  // Seuil de confiance plus bas pour détecter plus facilement
                                            inputSize: 320       // Taille d'entrée plus petite pour une détection plus rapide
                                        })
                                    );
                                }

                                // Si un visage est détecté
                                if (detections && detections.length > 0) {
                                    // Dessiner un rectangle autour du visage détecté
                                    const detection = detections[0]; // Prendre le premier visage détecté

                                    // Dessiner le rectangle en tenant compte du facteur d'échelle
                                    context.beginPath();
                                    context.lineWidth = 2; // Ligne plus fine pour un rendu plus rapide
                                    context.strokeStyle = "#e6c78b";

                                    // Dessiner le rectangle autour du visage
                                    context.rect(
                                        detection.box.x,
                                        detection.box.y,
                                        detection.box.width,
                                        detection.box.height
                                    );
                                    context.stroke();

                                    // Ajouter un texte de confirmation
                                    context.font = "16px Arial";
                                    context.fillStyle = "#e6c78b";
                                    context.textAlign = "center";
                                    context.fillText(
                                        "Visage détecté!",
                                        facialCanvas.width / 2,
                                        facialCanvas.height * 0.15
                                    );

                                    // Si c'est la première détection de visage
                                    if (!faceDetected) {
                                        faceDetected = true;
                                        facialStatus.textContent = 'Visage détecté! Authentification en cours...';

                                        // Authentifier immédiatement avec un délai minimal
                                        setTimeout(() => {
                                            // Capturer l'image et authentifier
                                            captureAndAuthenticate();
                                        }, 300); // Réduit à 300ms pour une réponse plus rapide
                                    }
                                } else {
                                    // Aucun visage détecté, dessiner un cadre de guidage
                                    context.beginPath();
                                    context.lineWidth = 2;
                                    context.strokeStyle = "rgba(230, 199, 139, 0.5)";
                                    context.rect(
                                        facialCanvas.width * 0.2,
                                        facialCanvas.height * 0.2,
                                        facialCanvas.width * 0.6,
                                        facialCanvas.height * 0.6
                                    );
                                    context.stroke();

                                    // Ajouter un texte d'aide
                                    context.font = "16px Arial";
                                    context.fillStyle = "white";
                                    context.textAlign = "center";
                                    context.fillText(
                                        "Placez votre visage dans ce cadre",
                                        facialCanvas.width / 2,
                                        facialCanvas.height * 0.15
                                    );
                                }

                                // Continuer la détection si la vidéo est toujours active et qu'aucun visage n'a été détecté
                                if (facialVideo.srcObject && !faceDetected) {
                                    requestAnimationFrame(detectFace);
                                }
                            } catch (error) {
                                console.error('Erreur lors de la détection faciale:', error);
                                facialStatus.textContent = 'Erreur lors de la détection faciale. Veuillez réessayer.';

                                // Continuer malgré l'erreur
                                if (facialVideo.srcObject && !faceDetected) {
                                    requestAnimationFrame(detectFace);
                                }
                            }
                        }

                        // Démarrer la détection faciale une fois que la vidéo est prête
                        facialVideo.onloadedmetadata = () => {
                            facialCanvas.width = facialVideo.videoWidth;
                            facialCanvas.height = facialVideo.videoHeight;
                            detectFace();
                        };

                        // Si la vidéo est déjà chargée
                        if (facialVideo.readyState >= 2) {
                            facialCanvas.width = facialVideo.videoWidth;
                            facialCanvas.height = facialVideo.videoHeight;
                            detectFace();
                        }
                    })
                    .catch(function(err) {
                        facialStatus.textContent = 'Erreur d\'accès à la caméra: ' + err.message;
                        console.error('Erreur d\'accès à la caméra:', err);

                        if (err.name === 'NotAllowedError') {
                            facialStatus.textContent += ' Veuillez autoriser l\'accès à la caméra dans les paramètres de votre navigateur.';
                        } else if (err.name === 'NotFoundError') {
                            facialStatus.textContent += ' Aucune caméra n\'a été détectée sur votre appareil.';
                        }
                    });
                } catch (error) {
                    console.error('Erreur lors du chargement des modèles de détection faciale:', error);
                    facialStatus.textContent = 'Utilisation du mode de secours sans détection faciale...';

                    // Solution de secours : utiliser un mode simplifié sans détection faciale
                    setTimeout(() => {
                        try {
                            // Accéder à la caméra en mode de secours
                            navigator.mediaDevices.getUserMedia({
                                video: {
                                    facingMode: "user",
                                    width: { ideal: 640 },
                                    height: { ideal: 480 }
                                }
                            })
                            .then(function(mediaStream) {
                                stream = mediaStream;
                                facialVideo.srcObject = mediaStream;
                                facialVideo.setAttribute('playsinline', true);
                                facialVideo.play();

                                facialStatus.textContent = 'Mode de secours activé. Cliquez sur le bouton ci-dessous pour vous authentifier.';

                                // Créer un bouton d'authentification de secours
                                const fallbackButton = document.createElement('button');
                                fallbackButton.className = 'auth-btn qr-btn';
                                fallbackButton.style.marginTop = '15px';
                                fallbackButton.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>S\'authentifier';
                                fallbackButton.onclick = function() {
                                    facialStatus.textContent = 'Authentification en cours...';
                                    captureAndAuthenticate();
                                };

                                // Ajouter le bouton au conteneur de statut
                                facialStatus.parentNode.appendChild(fallbackButton);

                                // Dessiner un cadre de guidage simple
                                const context = facialCanvas.getContext('2d');
                                facialCanvas.width = facialVideo.videoWidth || 640;
                                facialCanvas.height = facialVideo.videoHeight || 480;

                                function drawFallbackGuide() {
                                    if (!facialVideo.srcObject) return;

                                    context.clearRect(0, 0, facialCanvas.width, facialCanvas.height);
                                    context.drawImage(facialVideo, 0, 0, facialCanvas.width, facialCanvas.height);

                                    // Dessiner un cadre de guidage
                                    context.beginPath();
                                    context.lineWidth = 2;
                                    context.strokeStyle = "rgba(230, 199, 139, 0.5)";
                                    context.rect(
                                        facialCanvas.width * 0.2,
                                        facialCanvas.height * 0.2,
                                        facialCanvas.width * 0.6,
                                        facialCanvas.height * 0.6
                                    );
                                    context.stroke();

                                    // Ajouter un texte d'aide
                                    context.font = "16px Arial";
                                    context.fillStyle = "white";
                                    context.textAlign = "center";
                                    context.fillText(
                                        "Placez votre visage dans ce cadre et cliquez sur le bouton",
                                        facialCanvas.width / 2,
                                        facialCanvas.height * 0.15
                                    );

                                    if (facialVideo.srcObject) {
                                        requestAnimationFrame(drawFallbackGuide);
                                    }
                                }

                                // Démarrer le dessin du cadre de guidage
                                requestAnimationFrame(drawFallbackGuide);
                            })
                            .catch(function(err) {
                                facialStatus.textContent = 'Erreur d\'accès à la caméra: ' + err.message;
                                console.error('Erreur d\'accès à la caméra en mode de secours:', err);
                            });
                        } catch (fallbackError) {
                            console.error('Erreur lors de l\'initialisation du mode de secours:', fallbackError);
                            facialStatus.textContent = 'Impossible d\'initialiser le mode de secours. Veuillez réessayer plus tard.';
                        }
                    }, 1000);
                }
            }

            // Arrêter la reconnaissance faciale
            function stopFacialRecognition() {
                if (stream) {
                    stream.getTracks().forEach(track => {
                        track.stop();
                    });
                    stream = null;
                }
                facialVideo.srcObject = null;
            }

            // Capturer l'image et authentifier
            function captureAndAuthenticate() {
                if (!facialVideo.srcObject) return;

                try {
                    // Préparer le canvas pour capturer l'image
                    facialCanvas.width = facialVideo.videoWidth;
                    facialCanvas.height = facialVideo.videoHeight;
                    const context = facialCanvas.getContext('2d');

                    // Dessiner l'image de la caméra sur le canvas
                    context.drawImage(facialVideo, 0, 0, facialCanvas.width, facialCanvas.height);

                    // Convertir l'image en base64
                    const imageData = facialCanvas.toDataURL('image/jpeg', 0.8);

                    // Envoyer les données au serveur
                    facialStatus.textContent = 'Authentification en cours...';

                    fetch('{{ url_for("login_facial") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            facial_data: imageData
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Erreur réseau: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            facialStatus.textContent = 'Authentification réussie! Redirection...';

                            // Ajouter un effet visuel de succès
                            const context = facialCanvas.getContext('2d');
                            context.beginPath();
                            context.lineWidth = 5;
                            context.strokeStyle = "#28a745"; // Vert pour indiquer le succès
                            context.rect(0, 0, facialCanvas.width, facialCanvas.height);
                            context.stroke();

                            // Ajouter un texte de confirmation
                            context.font = "24px Arial";
                            context.fillStyle = "#28a745";
                            context.textAlign = "center";
                            context.fillText(
                                "Authentification réussie!",
                                facialCanvas.width / 2,
                                facialCanvas.height / 2
                            );

                            setTimeout(function() {
                                window.location.href = "{{ url_for('liste_vehicules') }}";
                            }, 800); // Réduit à 800ms pour une redirection plus rapide
                        } else {
                            facialStatus.textContent = 'Erreur d\'authentification: ' + data.message;

                            // Ajouter un effet visuel d'échec
                            const context = facialCanvas.getContext('2d');
                            context.beginPath();
                            context.lineWidth = 5;
                            context.strokeStyle = "#dc3545"; // Rouge pour indiquer l'échec
                            context.rect(0, 0, facialCanvas.width, facialCanvas.height);
                            context.stroke();

                            // Ajouter un texte d'erreur
                            context.font = "24px Arial";
                            context.fillStyle = "#dc3545";
                            context.textAlign = "center";
                            context.fillText(
                                "Échec de l'authentification",
                                facialCanvas.width / 2,
                                facialCanvas.height / 2
                            );

                            setTimeout(function() {
                                stopFacialRecognition();
                                facialModal.style.display = 'none';
                            }, 2000);
                        }
                    })
                    .catch(error => {
                        console.error('Erreur lors de l\'authentification:', error);
                        facialStatus.textContent = 'Erreur de connexion au serveur: ' + error.message;

                        // Ajouter un effet visuel d'erreur
                        const context = facialCanvas.getContext('2d');
                        context.beginPath();
                        context.lineWidth = 5;
                        context.strokeStyle = "#dc3545"; // Rouge pour indiquer l'erreur
                        context.rect(0, 0, facialCanvas.width, facialCanvas.height);
                        context.stroke();

                        // Ajouter un texte d'erreur
                        context.font = "24px Arial";
                        context.fillStyle = "#dc3545";
                        context.textAlign = "center";
                        context.fillText(
                            "Erreur de connexion",
                            facialCanvas.width / 2,
                            facialCanvas.height / 2
                        );

                        setTimeout(function() {
                            stopFacialRecognition();
                            facialModal.style.display = 'none';
                        }, 2000);
                    });

                } catch (error) {
                    console.error('Erreur lors de la capture faciale:', error);
                    facialStatus.textContent = 'Erreur lors de la capture faciale.';

                    setTimeout(function() {
                        stopFacialRecognition();
                        facialModal.style.display = 'none';
                    }, 2000);
                }
            }



            // Analyser les images de la caméra pour détecter un code QR
            function tick() {
                try {
                    if (qrVideo.readyState === qrVideo.HAVE_ENOUGH_DATA) {
                        qrCanvas.height = qrVideo.videoHeight;
                        qrCanvas.width = qrVideo.videoWidth;
                        const context = qrCanvas.getContext('2d');
                        context.drawImage(qrVideo, 0, 0, qrCanvas.width, qrCanvas.height);

                        try {
                            const imageData = context.getImageData(0, 0, qrCanvas.width, qrCanvas.height);

                            // Vérifier si jsQR est disponible
                            if (typeof jsQR !== 'function') {
                                console.error('La bibliothèque jsQR n\'est pas chargée correctement');
                                qrStatus.textContent = 'Erreur: La bibliothèque de lecture QR n\'est pas disponible';
                                return;
                            }

                            console.log('Analyse de l\'image pour détecter un QR code...');

                            // Utiliser jsQR pour détecter un code QR avec des options plus permissives
                            const code = jsQR(imageData.data, imageData.width, imageData.height, {
                                inversionAttempts: "attemptBoth", // Essayer les deux inversions
                            });

                            if (code) {
                                // Code QR détecté
                                qrStatus.textContent = 'Code QR détecté! Authentification en cours...';
                                console.log('Code QR détecté:', code.data);

                                // Afficher un indicateur visuel de détection
                                context.beginPath();
                                context.lineWidth = 4;
                                context.strokeStyle = "#e6c78b"; // Couleur militaire/sable pour correspondre au thème
                                context.moveTo(code.location.topLeftCorner.x, code.location.topLeftCorner.y);
                                context.lineTo(code.location.topRightCorner.x, code.location.topRightCorner.y);
                                context.lineTo(code.location.bottomRightCorner.x, code.location.bottomRightCorner.y);
                                context.lineTo(code.location.bottomLeftCorner.x, code.location.bottomLeftCorner.y);
                                context.lineTo(code.location.topLeftCorner.x, code.location.topLeftCorner.y);
                                context.stroke();

                                // Afficher le contenu du QR code pour le débogage
                                console.log('Contenu du QR code:', code.data);

                                // Utiliser directement le contenu du QR code scanné
                                // Seul le QR code officiel sera accepté par le serveur
                                let qrDataToSend = code.data;

                                // Créer une requête pour se connecter via QR code
                                fetch('{{ url_for("login_qr") }}', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    body: JSON.stringify({
                                        qr_data: qrDataToSend
                                    })
                                })
                                .then(response => {
                                    console.log('Réponse du serveur reçue:', response.status);
                                    if (!response.ok) {
                                        throw new Error('Erreur réseau: ' + response.status);
                                    }
                                    return response.json();
                                })
                                .then(data => {
                                    console.log('Données reçues du serveur:', data);
                                    if (data.success) {
                                        qrStatus.textContent = 'Authentification réussie! Redirection...';
                                        setTimeout(function() {
                                            window.location.href = "{{ url_for('liste_vehicules') }}";
                                        }, 1000);
                                    } else {
                                        qrStatus.textContent = 'Erreur d\'authentification: ' + data.message;
                                        qrStatus.style.color = '#ff6b6b';
                                        qrStatus.style.borderLeft = '4px solid #ff6b6b';

                                        // Afficher un message plus détaillé
                                        const errorDetails = document.createElement('div');
                                        errorDetails.className = 'error-details';
                                        errorDetails.innerHTML = 'Veuillez utiliser uniquement le QR code officiel fourni par l\'administrateur système.';
                                        qrStatus.appendChild(errorDetails);

                                        setTimeout(function() {
                                            stopQRScan();
                                            qrModal.style.display = 'none';
                                        }, 4000); // Augmenter le délai pour laisser le temps de lire le message
                                    }
                                })
                                .catch(error => {
                                    console.error('Erreur lors de l\'authentification:', error);
                                    qrStatus.textContent = 'Erreur de connexion au serveur: ' + error.message;
                                    setTimeout(function() {
                                        stopQRScan();
                                        qrModal.style.display = 'none';
                                    }, 2000);
                                });

                                // Arrêter le scan après un court délai pour permettre l'affichage du cadre
                                setTimeout(function() {
                                    stopQRScan();
                                }, 1000); // Augmenté à 1000ms pour donner plus de temps
                                return;
                            } else {
                                // Pas de QR code détecté dans cette frame
                                // Ajouter un indicateur visuel pour montrer que la détection est active
                                context.beginPath();
                                context.lineWidth = 2;
                                context.strokeStyle = "rgba(0, 255, 0, 0.5)";
                                context.rect(
                                    qrCanvas.width * 0.2,
                                    qrCanvas.height * 0.2,
                                    qrCanvas.width * 0.6,
                                    qrCanvas.height * 0.6
                                );
                                context.stroke();

                                // Ajouter un texte d'aide
                                context.font = "16px Arial";
                                context.fillStyle = "white";
                                context.textAlign = "center";
                                context.fillText(
                                    "Placez le QR code dans ce cadre",
                                    qrCanvas.width / 2,
                                    qrCanvas.height * 0.15
                                );
                            }
                        } catch (imageError) {
                            console.error('Erreur lors du traitement de l\'image:', imageError);
                            // Ne pas afficher d'erreur à l'utilisateur, continuer à essayer
                        }
                    }

                    if (stream) {
                        requestAnimationFrame(tick);
                    }
                } catch (error) {
                    console.error('Erreur dans la fonction tick:', error);
                    qrStatus.textContent = 'Une erreur est survenue lors de la lecture du code QR';
                    stopQRScan();
                }
            }
        });
    </script>
</body>
</html>
