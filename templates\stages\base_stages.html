<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Stages{% endblock %}</title>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/details-sidebar.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/digital-clock.css', v='1.0') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/stages-navbar-fix.css', v='1.0') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500;700&display=swap" rel="stylesheet">
    {% block extra_css %}{% endblock %}
    <style>
        .navbar {
            position: relative;
            z-index: 1051;
        }
        .navbar-nav .nav-link {
            white-space: nowrap;
            padding-left: 24px;
        }
    </style>
</head>
<body>
    <!-- Navigation pour les Stages -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard_stages') }}">
                <i class="fas fa-graduation-cap me-2"></i>
                Gestion des Stages
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown" aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavDropdown">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard_stages' %}active{% endif %}" href="{{ url_for('dashboard_stages') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('stages_main') }}">
                            <i class="fas fa-users me-1"></i> Stagiaires & Stages
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('liste_promotions') }}">
                            <i class="fas fa-layer-group me-1"></i> Promotions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('liste_inscriptions') }}">
                            <i class="fas fa-clipboard-list me-1"></i> Inscriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('liste_notifications') }}">
                            <i class="fas fa-bell me-1"></i> Suivi & Alertes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('rapports_analytiques') }}">
                            <i class="fas fa-file-alt me-1"></i> Documents & Rapports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('chatbot_stages') }}">
                            <i class="fas fa-robot me-1"></i> Assistant IA
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'stages.courrier' %}active{% endif %}" href="{{ url_for('stages.courrier') }}">
                            <i class="fas fa-envelope me-1"></i> Gestion du Courrier
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-info" href="{{ url_for('logout_stages') }}">
                            <i class="fas fa-sign-out-alt me-1"></i> Déconnexion
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-dark">
        <div class="container text-center">
            <span class="text-muted">© 2024 Gestion des Stages. Tous droits réservés.</span>
        </div>
    </footer>
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 