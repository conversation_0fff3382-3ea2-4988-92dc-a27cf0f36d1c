{% extends "rh/base_rh.html" %}

{% block title %}Gestion des Absences - RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-calendar-times"></i>
                                Gestion des Absences et Permissions
                            </h2>
                            <small class="text-muted">Suivi des absences, permissions et congés du personnel</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouvelle_absence') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouvelle Absence
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Nom, prénom, motif..." value="{{ search }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label-military">Type d'Absence</label>
                            <select name="type" class="form-control form-control-military">
                                <option value="">Tous les types</option>
                                {% for type_abs in types_absences %}
                                <option value="{{ type_abs }}" {% if type_abs == type_filter %}selected{% endif %}>
                                    {{ type_abs }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label-military">Statut</label>
                            <select name="statut" class="form-control form-control-military">
                                <option value="">Tous les statuts</option>
                                {% for statut in statuts %}
                                <option value="{{ statut }}" {% if statut == statut_filter %}selected{% endif %}>
                                    {{ statut }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-calendar-times stat-icon"></i>
                <div class="stat-number">{{ absences.total }}</div>
                <div class="stat-label">Total Absences</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-clock stat-icon"></i>
                <div class="stat-number">{{ absences.items|selectattr('statut', 'equalto', 'En attente')|list|length }}</div>
                <div class="stat-label">En Attente</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-check stat-icon"></i>
                <div class="stat-number">{{ absences.items|selectattr('statut', 'equalto', 'Approuvé')|list|length }}</div>
                <div class="stat-label">Approuvées</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-times stat-icon"></i>
                <div class="stat-number">{{ absences.items|selectattr('statut', 'equalto', 'Refusé')|list|length }}</div>
                <div class="stat-label">Refusées</div>
            </div>
        </div>
    </div>

    <!-- Liste des Absences -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Liste des Absences
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if absences.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Militaire</th>
                                    <th>Type</th>
                                    <th>Période</th>
                                    <th>Durée</th>
                                    <th>Motif</th>
                                    <th>Statut</th>
                                    <th>Date Demande</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for absence in absences.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ absence.militaire.nom }} {{ absence.militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ absence.militaire.grade_actuel or 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-military">{{ absence.type_absence }}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ absence.date_debut.strftime('%d/%m/%Y') }}</strong>
                                            <br><small class="text-muted">au {{ absence.date_fin.strftime('%d/%m/%Y') }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info-military">{{ absence.duree_jours }} jour(s)</span>
                                    </td>
                                    <td>
                                        <div title="{{ absence.motif }}">
                                            {{ absence.motif[:50] }}{% if absence.motif|length > 50 %}...{% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if absence.statut == 'Approuvé' %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-check"></i> {{ absence.statut }}
                                        </span>
                                        {% elif absence.statut == 'Refusé' %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-times"></i> {{ absence.statut }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-clock"></i> {{ absence.statut }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ absence.date_creation.strftime('%d/%m/%Y') }}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info-military btn-sm" 
                                                    onclick="voirAbsence({{ absence.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            {% if absence.statut == 'En attente' %}
                                            <button class="btn btn-success-military btn-sm" 
                                                    onclick="approuverAbsence({{ absence.id }})" title="Approuver">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-danger-military btn-sm" 
                                                    onclick="refuserAbsence({{ absence.id }})" title="Refuser">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button class="btn btn-warning-military btn-sm" 
                                                    onclick="modifierAbsence({{ absence.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune absence trouvée</h5>
                        <p class="text-muted">Aucune absence ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouvelle_absence') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Créer une Absence
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if absences.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if absences.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_absences', page=absences.prev_num, search=search, type=type_filter, statut=statut_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in absences.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != absences.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.liste_absences', page=page_num, search=search, type=type_filter, statut=statut_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if absences.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.liste_absences', page=absences.next_num, search=search, type=type_filter, statut=statut_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Affichage de {{ absences.per_page * (absences.page - 1) + 1 }} à 
                            {{ absences.per_page * (absences.page - 1) + absences.items|length }} 
                            sur {{ absences.total }} résultats
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modals pour les actions -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: var(--card-bg); border: 1px solid var(--border-color);">
            <div class="modal-header" style="border-bottom: 1px solid var(--border-color);">
                <h5 class="modal-title text-warning">
                    <i class="fas fa-eye"></i> Détails de l'Absence
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function voirAbsence(id) {
    // Charger les détails de l'absence
    fetch(`/rh/absences/${id}/details`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('detailsContent').innerHTML = data.html;
            const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du chargement des détails');
        });
}

function approuverAbsence(id) {
    if (confirm('Êtes-vous sûr de vouloir approuver cette absence ?')) {
        fetch(`/rh/absences/${id}/approuver`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors de l\'approbation');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'approbation');
        });
    }
}

function refuserAbsence(id) {
    const motif = prompt('Motif du refus (optionnel):');
    if (motif !== null) {
        fetch(`/rh/absences/${id}/refuser`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ motif: motif })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Erreur lors du refus');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors du refus');
        });
    }
}

function modifierAbsence(id) {
    window.location.href = `/rh/absences/${id}/modifier`;
}

function exportToExcel() {
    window.location.href = '/rh/absences/export/excel?' + new URLSearchParams(window.location.search);
}

function exportToPDF() {
    window.location.href = '/rh/absences/export/pdf?' + new URLSearchParams(window.location.search);
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
