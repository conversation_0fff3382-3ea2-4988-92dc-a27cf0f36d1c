from flask import render_template, request, redirect, url_for, flash, jsonify, session
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_, func, desc
from . import rh_bp
from db import db
from rh_models import *

@rh_bp.route('/')
def dashboard():
    """Dashboard principal de l'application RH"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        total_officiers = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier'
        ).count()
        total_officiers_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier du rang'
        ).count()
        total_militaires_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Militaire du rang'
        ).count()
        
        # Répartition par arme
        repartition_arme = db.session.query(
            ReferentielArme.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielArme.libelle).all()
        
        # Répartition par unité (top 10)
        repartition_unite = db.session.query(
            ReferentielUnite.libelle,
            func.count(Personnel.matricule).label('count')
        ).join(Personnel).group_by(ReferentielUnite.libelle).order_by(
            desc(func.count(Personnel.matricule))
        ).limit(10).all()
        
        # Personnel en situation médicale inapte
        personnel_inapte = SituationMedicale.query.filter_by(aptitude='inapte').count()
        
        # Sanctions récentes (30 derniers jours)
        date_limite = datetime.now().date() - timedelta(days=30)
        sanctions_recentes = Sanction.query.filter(
            Sanction.date_sanction >= date_limite
        ).count()
        
        # Permissions en cours
        aujourd_hui = datetime.now().date()
        permissions_en_cours = Permission.query.filter(
            and_(Permission.date_debut <= aujourd_hui, Permission.date_fin >= aujourd_hui)
        ).count()
        
        # Détachements en cours
        detachements_en_cours = Detachement.query.filter(
            and_(Detachement.date_debut <= aujourd_hui, Detachement.date_fin >= aujourd_hui)
        ).count()
        
        stats = {
            'total_personnel': total_personnel,
            'total_officiers': total_officiers,
            'total_officiers_rang': total_officiers_rang,
            'total_militaires_rang': total_militaires_rang,
            'repartition_arme': repartition_arme,
            'repartition_unite': repartition_unite,
            'personnel_inapte': personnel_inapte,
            'sanctions_recentes': sanctions_recentes,
            'permissions_en_cours': permissions_en_cours,
            'detachements_en_cours': detachements_en_cours
        }
        
        # Adapter les données pour le nouveau template
        stats_categories = db.session.query(ReferentielCategorie.libelle, func.count(Personnel.matricule))\
                          .join(Personnel).group_by(ReferentielCategorie.libelle).all()

        stats_grades = db.session.query(ReferentielGrade.libelle, func.count(Personnel.matricule))\
                      .join(Personnel).group_by(ReferentielGrade.libelle)\
                      .order_by(func.count(Personnel.matricule).desc()).limit(5).all()

        stats_unites = db.session.query(ReferentielUnite.libelle, func.count(Personnel.matricule))\
                       .join(Personnel).group_by(ReferentielUnite.libelle)\
                       .order_by(func.count(Personnel.matricule).desc()).limit(5).all()

        total_aptes = SituationMedicale.query.filter_by(aptitude='apte').count()
        total_inaptes = personnel_inapte
        permissions_recentes = permissions_en_cours

        return render_template('rh/dashboard.html',
                             total_personnel=total_personnel,
                             stats_categories=stats_categories,
                             stats_grades=stats_grades,
                             stats_unites=stats_unites,
                             total_aptes=total_aptes,
                             total_inaptes=total_inaptes,
                             permissions_recentes=permissions_recentes,
                             detachements_en_cours=detachements_en_cours)
        
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('rh/dashboard.html',
                             total_personnel=0,
                             stats_categories=[],
                             stats_grades=[],
                             stats_unites=[],
                             total_aptes=0,
                             total_inaptes=0,
                             permissions_recentes=0,
                             detachements_en_cours=0)

@rh_bp.route('/recherche', methods=['GET', 'POST'])
def recherche_personnel():
    """Interface de recherche unifiée (simple et avancée)"""
    try:
        # Charger les référentiels pour les filtres avancés
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        categories = ReferentielCategorie.query.all()

        # Si c'est une recherche (GET avec paramètres ou POST)
        if request.method == 'POST' or any(request.args.get(param) for param in ['search', 'matricule', 'cin', 'gsm', 'categorie_id', 'grade_id', 'arme_id', 'unite_id', 'specialite_id', 'age_min', 'age_max']):
            return recherche_resultats()

        # Affichage par défaut : liste des 20 premiers militaires
        personnel_defaut = Personnel.query.join(Personnel.arme)\
                                         .join(Personnel.unite)\
                                         .join(Personnel.grade_actuel)\
                                         .join(Personnel.categorie)\
                                         .order_by(Personnel.nom, Personnel.prenom)\
                                         .limit(20).all()

        return render_template('rh/recherche_personnel.html',
                             armes=armes,
                             unites=unites,
                             grades=grades,
                             categories=categories,
                             personnel_defaut=personnel_defaut,
                             affichage_defaut=True)
    except Exception as e:
        flash(f'Erreur lors du chargement de la page de recherche: {str(e)}', 'error')
        return render_template('rh/recherche_personnel.html',
                             armes=[],
                             unites=[],
                             grades=[],
                             categories=[],
                             personnel_defaut=[],
                             affichage_defaut=False)

def recherche_resultats():
    """Traitement des recherches simple et avancée"""
    try:
        # Récupération des paramètres (GET ou POST)
        if request.method == 'POST':
            data = request.form
        else:
            data = request.args

        # Paramètres de recherche
        search = data.get('search', '').strip()  # Nom/Prénom
        matricule = data.get('matricule', '').strip()
        cin = data.get('cin', '').strip()
        gsm = data.get('gsm', '').strip()
        categorie_id = data.get('categorie_id')
        grade_id = data.get('grade_id')
        arme_id = data.get('arme_id')
        unite_id = data.get('unite_id')
        specialite_id = data.get('specialite_id')
        age_min = data.get('age_min')
        age_max = data.get('age_max')

        query = Personnel.query

        # Application des filtres
        if search:
            # Recherche par nom, prénom ou nom/prénom arabe
            query = query.filter(
                or_(
                    Personnel.nom.ilike(f'%{search}%'),
                    Personnel.prenom.ilike(f'%{search}%'),
                    Personnel.nom_arabe.ilike(f'%{search}%'),
                    Personnel.prenom_arabe.ilike(f'%{search}%')
                )
            )

        if matricule:
            query = query.filter(Personnel.matricule.ilike(f'%{matricule}%'))

        if cin:
            query = query.filter(Personnel.numero_cin.ilike(f'%{cin}%'))

        if gsm:
            query = query.filter(Personnel.gsm.ilike(f'%{gsm}%'))

        if arme_id:
            query = query.filter(Personnel.arme_id == arme_id)

        if unite_id:
            query = query.filter(Personnel.unite_id == unite_id)

        if grade_id:
            query = query.filter(Personnel.grade_actuel_id == grade_id)

        if categorie_id:
            query = query.filter(Personnel.categorie_id == categorie_id)

        if specialite_id:
            query = query.filter(Personnel.specialite_id == specialite_id)

        if age_min or age_max:
            aujourd_hui = date.today()
            if age_min:
                date_max_naissance = aujourd_hui.replace(year=aujourd_hui.year - int(age_min))
                query = query.filter(Personnel.date_naissance <= date_max_naissance)
            if age_max:
                date_min_naissance = aujourd_hui.replace(year=aujourd_hui.year - int(age_max))
                query = query.filter(Personnel.date_naissance >= date_min_naissance)

        # Exécution de la requête avec jointures pour optimiser
        resultats = query.join(Personnel.arme).join(Personnel.unite).join(Personnel.grade_actuel).all()

        return render_template('rh/resultats_recherche.html',
                             resultats=resultats,
                             search_term=search,
                             total_resultats=len(resultats))

    except Exception as e:
        flash(f'Erreur lors de la recherche: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/liste')
def liste_personnel():
    """Affichage de la liste complète du personnel"""
    try:
        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = 50  # 50 militaires par page

        # Récupération avec pagination
        personnel_paginated = Personnel.query.join(Personnel.arme)\
                                           .join(Personnel.unite)\
                                           .join(Personnel.grade_actuel)\
                                           .join(Personnel.categorie)\
                                           .order_by(Personnel.nom, Personnel.prenom)\
                                           .paginate(page=page, per_page=per_page, error_out=False)

        return render_template('rh/personnel/liste.html',
                             personnel=personnel_paginated.items,
                             pagination=personnel_paginated,
                             total=personnel_paginated.total)

    except Exception as e:
        flash(f'Erreur lors du chargement de la liste: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))

@rh_bp.route('/personnel/<matricule>')
def fiche_personnel(matricule):
    """Affichage de la fiche complète d'un personnel"""
    try:
        personnel = Personnel.query.filter_by(matricule=matricule).first()
        if not personnel:
            flash('Personnel non trouvé', 'error')
            return redirect(url_for('rh.recherche_personnel'))
        
        # Charger toutes les données associées
        conjoint = Conjoint.query.filter_by(matricule=matricule).first()
        enfants = Enfant.query.filter_by(matricule=matricule).all()
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(desc(Vaccination.date_vaccination)).all()
        ptcs = PTC.query.filter_by(matricule=matricule).order_by(desc(PTC.date_ptc)).all()
        permissions = Permission.query.filter_by(matricule=matricule).order_by(desc(Permission.date_debut)).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(desc(Desertion.date_desertion)).all()
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(desc(Detachement.date_debut)).all()
        mutations = MutationFonction.query.filter_by(matricule=matricule).order_by(desc(MutationFonction.date_debut)).all()
        sejours_ops = SejourOps.query.filter_by(matricule=matricule).order_by(desc(SejourOps.date_debut)).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(desc(Liberation.date_liberation)).all()
        avancements = Avancement.query.filter_by(matricule=matricule).order_by(desc(Avancement.date_avancement)).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(desc(Sanction.date_sanction)).all()
        
        return render_template('rh/fiche_personnel.html',
                             personnel=personnel,
                             conjoint=conjoint,
                             enfants=enfants,
                             situation_medicale=situation_medicale,
                             vaccinations=vaccinations,
                             ptcs=ptcs,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             avancements=avancements,
                             sanctions=sanctions)
        
    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/personnel/<matricule>/complete')
def fiche_personnel_complete(matricule):
    """Affichage de la fiche complète détaillée d'un personnel"""
    try:
        personnel = Personnel.query.filter_by(matricule=matricule).first()
        if not personnel:
            flash('Personnel non trouvé', 'error')
            return redirect(url_for('rh.recherche_personnel'))

        # Charger toutes les données associées avec plus de détails
        conjoint = Conjoint.query.filter_by(matricule=matricule).first()
        enfants = Enfant.query.filter_by(matricule=matricule).all()
        situation_medicale = SituationMedicale.query.filter_by(matricule=matricule).first()
        vaccinations = Vaccination.query.filter_by(matricule=matricule).order_by(desc(Vaccination.date_vaccination)).all()
        ptcs = PTC.query.filter_by(matricule=matricule).order_by(desc(PTC.date_ptc)).all()
        permissions = Permission.query.filter_by(matricule=matricule).order_by(desc(Permission.date_debut)).all()
        desertions = Desertion.query.filter_by(matricule=matricule).order_by(desc(Desertion.date_desertion)).all()
        detachements = Detachement.query.filter_by(matricule=matricule).order_by(desc(Detachement.date_debut)).all()
        mutations = Mutation.query.filter_by(matricule=matricule).order_by(desc(Mutation.date_mutation)).all()
        sejours_ops = SejourOperationnel.query.filter_by(matricule=matricule).order_by(desc(SejourOperationnel.date_debut)).all()
        liberations = Liberation.query.filter_by(matricule=matricule).order_by(desc(Liberation.date_liberation)).all()
        avancements = Avancement.query.filter_by(matricule=matricule).order_by(desc(Avancement.date_avancement)).all()
        sanctions = Sanction.query.filter_by(matricule=matricule).order_by(desc(Sanction.date_sanction)).all()

        # Calcul de l'âge
        from datetime import date
        age = None
        if personnel.date_naissance:
            today = date.today()
            age = today.year - personnel.date_naissance.year - ((today.month, today.day) < (personnel.date_naissance.month, personnel.date_naissance.day))

        return render_template('rh/fiche_personnel_complete.html',
                             militaire=personnel,
                             personnel=personnel,
                             age=age,
                             conjoint=conjoint,
                             enfants=enfants,
                             situation_medicale=situation_medicale,
                             vaccinations=vaccinations,
                             ptcs=ptcs,
                             permissions=permissions,
                             desertions=desertions,
                             detachements=detachements,
                             mutations=mutations,
                             sejours_ops=sejours_ops,
                             liberations=liberations,
                             avancements=avancements,
                             sanctions=sanctions)

    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche complète: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/nouveau')
def nouveau_militaire():
    """Formulaire d'ajout d'un nouveau militaire"""
    try:
        # Charger tous les référentiels nécessaires pour le nouveau template
        genres = ReferentielGenre.query.all()
        categories = ReferentielCategorie.query.all()
        groupes_sanguins = ReferentielGroupeSanguin.query.all()
        services = ReferentielArme.query.all()  # Services/Armes
        specialites = ReferentielSpecialite.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        etats_matrimoniaux = ReferentielSituationFamiliale.query.all()
        liens_parente = ReferentielDegreParente.query.all()

        return render_template('rh/nouveau_militaire.html',
                             genres=genres,
                             categories=categories,
                             groupes_sanguins=groupes_sanguins,
                             services=services,
                             specialites=specialites,
                             unites=unites,
                             grades=grades,
                             etats_matrimoniaux=etats_matrimoniaux,
                             liens_parente=liens_parente)

    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))

@rh_bp.route('/ajouter')
def ajouter_personnel():
    """Redirection vers nouveau_militaire pour compatibilité"""
    return redirect(url_for('rh.nouveau_militaire'))

# API pour récupérer les spécialités selon le service/arme sélectionné
@rh_bp.route('/api/specialites/<int:service_id>')
def get_specialites(service_id):
    """API pour récupérer les spécialités d'un service/arme"""
    try:
        specialites = ReferentielSpecialite.query.filter_by(id_arme=service_id).all()
        return jsonify([{'id': s.id_specialite, 'libelle': s.libelle} for s in specialites])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@rh_bp.route('/nouveau', methods=['POST'])
def traiter_nouveau_militaire():
    """Traitement du formulaire d'ajout de nouveau militaire"""
    try:
        # Récupération des données du formulaire
        matricule = request.form.get('matricule')

        # Vérifier si le matricule existe déjà
        if Personnel.query.filter_by(matricule=matricule).first():
            flash('Ce matricule existe déjà', 'error')
            return redirect(url_for('rh.nouveau_militaire'))

        # Création du personnel avec les noms de champs du nouveau template
        personnel = Personnel(
            matricule=matricule,
            nom=request.form.get('nom'),
            prenom=request.form.get('prenom'),
            nom_arabe=request.form.get('nom_ar'),
            prenom_arabe=request.form.get('prenom_ar'),
            date_naissance=datetime.strptime(request.form.get('date_naissance'), '%Y-%m-%d').date(),
            lieu_naissance=request.form.get('lieu_naissance'),
            sexe_id=int(request.form.get('genre_id')),
            categorie_id=int(request.form.get('categorie_id')),
            groupe_sanguin_id=int(request.form.get('groupe_sanguin_id')),
            numero_cin=request.form.get('cin_numero'),
            date_delivrance_cin=datetime.strptime(request.form.get('cin_date_delivrance'), '%Y-%m-%d').date(),
            date_expiration_cin=datetime.strptime(request.form.get('cin_date_expiration'), '%Y-%m-%d').date(),
            gsm=request.form.get('gsm'),
            telephone_domicile=request.form.get('telephone_domicile') or None,
            taille=float(request.form.get('taille_cm')),
            lieu_residence=request.form.get('lieu_residence'),
            arme_id=int(request.form.get('service_id')),
            specialite_id=int(request.form.get('specialite_id')) if request.form.get('specialite_id') else None,
            unite_id=int(request.form.get('unite_id')),
            grade_actuel_id=int(request.form.get('grade_actuel_id')),
            fonction=request.form.get('fonction'),
            date_prise_fonction=datetime.strptime(request.form.get('date_prise_fonction'), '%Y-%m-%d').date(),
            ccp=request.form.get('ccp_numero'),
            compte_bancaire=request.form.get('compte_bancaire_numero') or None,
            numero_somme=request.form.get('somme_numero'),
            date_engagement=datetime.strptime(request.form.get('date_engagement'), '%Y-%m-%d').date(),
            nom_pere=request.form.get('nom_pere'),
            prenom_pere=request.form.get('prenom_pere'),
            nom_mere=request.form.get('nom_mere'),
            prenom_mere=request.form.get('prenom_mere'),
            adresse_parents=request.form.get('adresse_parents'),
            situation_fam_id=int(request.form.get('etat_matrimonial_id')),
            nombre_enfants=int(request.form.get('nombre_enfants')) if request.form.get('nombre_enfants') else None,
            numero_passport=request.form.get('passeport_numero') or None,
            date_delivrance_passport=datetime.strptime(request.form.get('passeport_date_delivrance'), '%Y-%m-%d').date() if request.form.get('passeport_date_delivrance') else None,
            date_expiration_passport=datetime.strptime(request.form.get('passeport_date_expiration'), '%Y-%m-%d').date() if request.form.get('passeport_date_expiration') else None,
            gsm_urgence=request.form.get('gsm_urgence'),
            degre_parente_id=int(request.form.get('lien_parente_id'))
        )

        db.session.add(personnel)
        db.session.flush()  # Pour obtenir l'ID avant commit

        # Gestion des langues
        langues_ids = request.form.getlist('langues_ids')
        for langue_id in langues_ids:
            if langue_id:
                personnel_langue = PersonnelLangue(
                    matricule=matricule,
                    langue_id=int(langue_id)
                )
                db.session.add(personnel_langue)

        # Si marié(e), ajouter les informations du conjoint
        situation_fam = ReferentielSituationFamiliale.query.get(personnel.situation_fam_id)
        if situation_fam and situation_fam.libelle == 'Marié(e)':
            conjoint = Conjoint(
                matricule=matricule,
                nom=request.form.get('conjoint_nom'),
                prenom=request.form.get('conjoint_prenom'),
                nom_arabe=request.form.get('conjoint_nom_arabe'),
                prenom_arabe=request.form.get('conjoint_prenom_arabe'),
                date_naissance=datetime.strptime(request.form.get('conjoint_date_naissance'), '%Y-%m-%d').date(),
                lieu_naissance=request.form.get('conjoint_lieu_naissance'),
                lieu_naissance_arabe=request.form.get('conjoint_lieu_naissance_arabe'),
                adresse=request.form.get('conjoint_adresse'),
                adresse_arabe=request.form.get('conjoint_adresse_arabe'),
                date_mariage=datetime.strptime(request.form.get('conjoint_date_mariage'), '%Y-%m-%d').date(),
                lieu_mariage=request.form.get('conjoint_lieu_mariage'),
                profession=request.form.get('conjoint_profession'),
                profession_arabe=request.form.get('conjoint_profession_arabe'),
                numero_cin=request.form.get('conjoint_numero_cin'),
                gsm=request.form.get('conjoint_gsm'),
                nom_pere=request.form.get('conjoint_nom_pere'),
                prenom_pere=request.form.get('conjoint_prenom_pere'),
                nom_arabe_pere=request.form.get('conjoint_nom_arabe_pere'),
                prenom_arabe_pere=request.form.get('conjoint_prenom_arabe_pere'),
                nom_mere=request.form.get('conjoint_nom_mere'),
                prenom_mere=request.form.get('conjoint_prenom_mere'),
                nom_arabe_mere=request.form.get('conjoint_nom_arabe_mere'),
                prenom_arabe_mere=request.form.get('conjoint_prenom_arabe_mere'),
                profession_pere=request.form.get('conjoint_profession_pere'),
                profession_mere=request.form.get('conjoint_profession_mere')
            )
            db.session.add(conjoint)

        db.session.commit()
        flash('Personnel ajouté avec succès', 'success')
        return redirect(url_for('rh.fiche_personnel', matricule=matricule))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout: {str(e)}', 'error')
        return redirect(url_for('rh.nouveau_militaire'))

@rh_bp.route('/init_rh_database')
def init_rh_database():
    """Route pour initialiser/réinitialiser la base de données RH"""
    try:
        # Cette route peut être utilisée pour des opérations de maintenance
        flash('Base de données RH initialisée avec succès!', 'success')
        return redirect(url_for('rh.dashboard'))
    except Exception as e:
        flash(f'Erreur lors de l\'initialisation: {str(e)}', 'error')
        return redirect(url_for('rh.dashboard'))
