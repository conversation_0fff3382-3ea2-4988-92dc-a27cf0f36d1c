/* Styles pour la barre latérale des détails */
.details-sidebar {
    position: fixed;
    top: 0;
    right: -450px;
    width: 450px;
    max-width: 90vw;
    height: 100vh;
    background-color: #fff;
    box-shadow: -10px 0 30px rgba(0,0,0,0.15);
    z-index: 1050;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow-y: auto !important;
    overflow-x: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
    border-left: 4px solid #198754;
    border-radius: 20px 0 0 20px;
    box-sizing: border-box;
}

.details-sidebar.show {
    right: 0;
}

/* Animation de fermeture */
.details-sidebar.closing {
    right: -450px;
    transition: all 0.3s ease-out;
}

.details-sidebar-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 25px 20px;
    position: sticky;
    top: 0;
    z-index: 1055;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

/* Motif rayé pour l'en-tête */
.details-sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(45deg,
                      rgba(255, 255, 255, 0.1),
                      rgba(255, 255, 255, 0.1) 10px,
                      transparent 10px,
                      transparent 20px);
    z-index: 0;
}

/* Style du titre */
.details-sidebar-header h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    display: inline-block;
}

.details-sidebar-body {
    padding: 25px;
    flex: 1;
    overflow-y: visible;
    min-height: calc(100vh - 80px);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    background-color: #f8f9fa;
    position: relative;
}

/* Effet d'ombre en haut du corps */
.details-sidebar-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 15px;
    background: linear-gradient(to bottom, rgba(0,0,0,0.05), transparent);
    pointer-events: none;
}

/* Styles pour le bouton de fermeture en bas */
.btn-close-sidebar {
    display: block;
    width: 100%;
    padding: 10px;
    margin-top: 20px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    color: #495057;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-close-sidebar:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.details-sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0; /* Couvrir toute la page */
    width: 100vw; /* Largeur de la fenêtre entière */
    height: 100vh;
    background-color: transparent; /* Complètement transparent */
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out;
    cursor: default; /* Curseur normal pour ne pas indiquer qu'il est cliquable */
}

.details-sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.details-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    border: none;
}

.details-card-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.details-card-body {
    padding: 20px;
}

.details-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.details-list-item {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.details-list-item:last-child {
    border-bottom: none;
}

.details-badge {
    padding: 6px 12px;
    border-radius: 50px;
    font-weight: 500;
    font-size: 0.85rem;
}

.details-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

/* Style personnalisé pour la barre de défilement */
.details-sidebar::-webkit-scrollbar {
    width: 10px;
}

.details-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 5px;
}

.details-sidebar::-webkit-scrollbar-thumb {
    background: #0d6efd;
    border-radius: 5px;
}

.details-sidebar::-webkit-scrollbar-thumb:hover {
    background: #0b5ed7;
}

/* Styles pour les cartes d'information */
.details-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.details-card:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.details-card-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    font-weight: 600;
    color: #343a40;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
}

.details-card-header i {
    margin-right: 10px;
    color: #198754;
    font-size: 1.1rem;
}

.details-card-body {
    padding: 15px 20px;
}

/* Styles pour le tableau d'historique */
.history-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 10px;
}

.history-table thead th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 15px;
    text-align: left;
    border-bottom: 2px solid #e9ecef;
    font-size: 0.9rem;
}

.history-table tbody tr {
    transition: all 0.2s ease;
}

.history-table tbody tr:hover {
    background-color: rgba(25, 135, 84, 0.05);
}

.history-table tbody td {
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

/* Animation pour les éléments de la barre latérale */
@keyframes slideIn {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Animation pour les éléments qui apparaissent avec un délai */
.animate-item {
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation d'échelle pour les cartes */
.animate-scale {
    animation: scaleIn 0.4s ease forwards;
    transform-origin: center;
    opacity: 0;
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Animation de fondu pour les éléments */
.animate-fade {
    animation: fadeIn 0.5s ease forwards;
    opacity: 0;
}

/* Délais pour les animations */
.delay-1 {
    animation-delay: 0.1s;
}

.delay-2 {
    animation-delay: 0.2s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-item {
    animation: slideIn 0.3s ease-out forwards;
    opacity: 0;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* Styles pour la timeline */
.timeline {
    position: relative;
    padding: 0;
    margin: 0 0 50px 0; /* Ajouter une marge en bas pour éviter que le contenu soit coupé */
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    left: 35px;
    height: 100%;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
    padding-left: 70px;
    opacity: 0;
    animation: fadeIn 0.5s forwards;
}

.timeline-badge {
    position: absolute;
    top: 0;
    left: 0;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    text-align: center;
    line-height: 70px;
    color: white;
    z-index: 1;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.timeline-badge i {
    font-size: 28px;
}

.timeline-content {
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.timeline-date {
    font-weight: 500;
    font-size: 1.1rem;
    color: #6c757d;
}

.timeline-body {
    color: #495057;
    font-size: 1rem;
    line-height: 1.5;
    background-color: #f1f3f5;
    padding: 15px;
    border-radius: 6px;
    margin-top: 10px;
}
