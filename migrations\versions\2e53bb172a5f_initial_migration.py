"""Initial migration

Revision ID: 2e53bb172a5f
Revises: 
Create Date: 2025-05-20 13:06:14.575853

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '2e53bb172a5f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('type_vehicule', schema=None) as batch_op:
        batch_op.drop_index('nom')

    op.drop_table('type_vehicule')
    op.drop_table('marque_vehicule')
    with op.batch_alter_table('entretien', schema=None) as batch_op:
        batch_op.add_column(sa.Column('prochain_entretien', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('statut', sa.String(length=20), nullable=False))
        batch_op.drop_column('unité')

    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.create_unique_constraint(None, ['matricule'])

    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=mysql.VARCHAR(length=10),
               type_=sa.String(length=20),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=sa.String(length=20),
               type_=mysql.VARCHAR(length=10),
               nullable=False)

    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')

    with op.batch_alter_table('entretien', schema=None) as batch_op:
        batch_op.add_column(sa.Column('unité', mysql.VARCHAR(length=50), nullable=True))
        batch_op.drop_column('statut')
        batch_op.drop_column('prochain_entretien')

    op.create_table('marque_vehicule',
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('nom', mysql.VARCHAR(length=50), nullable=False),
    sa.Column('type_vehicule_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['type_vehicule_id'], ['type_vehicule.id'], name='marque_vehicule_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('type_vehicule',
    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
    sa.Column('nom', mysql.VARCHAR(length=20), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_general_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    with op.batch_alter_table('type_vehicule', schema=None) as batch_op:
        batch_op.create_index('nom', ['nom'], unique=True)

    # ### end Alembic commands ###
