#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ajout final des situations manquantes avec les bonnes structures
"""

import random
from datetime import date, datetime, timedelta
from rh_models import *
from app import app

# Données réalistes
NOMS_CONJOINTS_F = [
    ("ALAMI", "FATIMA", "العلمي", "فاطمة"),
    ("BENALI", "AICHA", "بن علي", "عائشة"),
    ("CHERKAOUI", "KHADIJA", "الشرقاوي", "خديجة"),
    ("DOUIRI", "ZINEB", "الدويري", "زينب"),
    ("FASSI", "MALIKA", "الفاسي", "مليكة"),
    ("GHAZI", "SAMIRA", "الغازي", "سميرة"),
    ("HAJJI", "NAIMA", "الحاجي", "نعيمة"),
    ("IDRISSI", "RACHIDA", "الإدريسي", "رشيدة"),
    ("JAMAL", "LATIFA", "جمال", "لطيفة"),
    ("KABBAJ", "AMINA", "القباج", "أمينة")
]

PRENOMS_ENFANTS = [
    "ADAM", "YOUSSEF", "MOHAMMED", "OMAR", "HASSAN", "AMINE", "ZAKARIA", "MEHDI",
    "FATIMA", "AICHA", "KHADIJA", "ZINEB", "MALIKA", "SAMIRA", "NAIMA", "RACHIDA"
]

VILLES_MAROCAINES = [
    "Rabat", "Casablanca", "Fès", "Marrakech", "Agadir", "Tanger", 
    "Meknès", "Oujda", "Kenitra", "Tétouan", "Safi", "Mohammedia"
]

VILLES_ARABES = [
    "الرباط", "الدار البيضاء", "فاس", "مراكش", "أكادير", "طنجة",
    "مكناس", "وجدة", "القنيطرة", "تطوان", "آسفي", "المحمدية"
]

PROFESSIONS_CONJOINTS = [
    "Enseignante", "Infirmière", "Employée de bureau", "Commerçante", 
    "Fonctionnaire", "Secrétaire", "Comptable", "Pharmacienne",
    "Sage-femme", "Institutrice", "Sans profession", "Femme au foyer"
]

PROFESSIONS_ARABES = [
    "معلمة", "ممرضة", "موظفة مكتب", "تاجرة",
    "موظفة", "سكرتيرة", "محاسبة", "صيدلانية",
    "قابلة", "معلمة", "بدون مهنة", "ربة بيت"
]

def random_date_between(start_date, end_date):
    """Génère une date aléatoire entre deux dates"""
    time_between = end_date - start_date
    days_between = time_between.days
    if days_between <= 0:
        return start_date
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def add_family_data_correct():
    """Ajoute les données familiales avec les bonnes structures"""
    print("\n👨‍👩‍👧‍👦 Ajout des données familiales (structure corrigée)...")
    
    with app.app_context():
        try:
            personnels = Personnel.query.all()
            conjoints_count = 0
            enfants_count = 0
            
            # Conjoints pour 40% des militaires
            militaires_maries = random.sample(personnels, int(len(personnels) * 0.4))
            
            for personnel in militaires_maries:
                nom_c, prenom_c, nom_arabe_c, prenom_arabe_c = random.choice(NOMS_CONJOINTS_F)
                ville_fr = random.choice(VILLES_MAROCAINES)
                ville_ar = random.choice(VILLES_ARABES)
                prof_fr = random.choice(PROFESSIONS_CONJOINTS)
                prof_ar = random.choice(PROFESSIONS_ARABES)
                
                conjoint = Conjoint(
                    matricule=personnel.matricule,
                    nom=nom_c,
                    prenom=prenom_c,
                    nom_arabe=nom_arabe_c,
                    prenom_arabe=prenom_arabe_c,
                    date_naissance=random_date_between(date(1985, 1, 1), date(2000, 12, 31)),
                    lieu_naissance=ville_fr,
                    lieu_naissance_arabe=ville_ar,  # Champ obligatoire
                    adresse=personnel.lieu_residence,
                    adresse_arabe=personnel.lieu_residence,  # Même adresse en arabe
                    date_mariage=random_date_between(date(2010, 1, 1), date(2024, 12, 31)),
                    lieu_mariage=ville_fr,
                    profession=prof_fr,
                    profession_arabe=prof_ar,
                    numero_cin=f"{random.choice(['A','B','C','D','E','F'])}{random.randint(100000, 999999)}",
                    gsm=f"06{random.randint(10000000, 99999999)}",
                    nom_pere=random.choice([n[1] for n in NOMS_CONJOINTS_F]),
                    prenom_pere=random.choice([n[0] for n in NOMS_CONJOINTS_F]),
                    nom_arabe_pere=random.choice([n[3] for n in NOMS_CONJOINTS_F]),
                    prenom_arabe_pere=random.choice([n[2] for n in NOMS_CONJOINTS_F]),
                    nom_mere=random.choice([n[1] for n in NOMS_CONJOINTS_F]),
                    prenom_mere=random.choice([n[0] for n in NOMS_CONJOINTS_F]),
                    nom_arabe_mere=random.choice([n[3] for n in NOMS_CONJOINTS_F]),
                    prenom_arabe_mere=random.choice([n[2] for n in NOMS_CONJOINTS_F]),
                    profession_pere=random.choice(["Commerçant", "Fonctionnaire", "Retraité", "Agriculteur"]),
                    profession_mere=random.choice(["Femme au foyer", "Enseignante", "Retraitée", "Commerçante"])
                )
                db.session.add(conjoint)
                conjoints_count += 1
            
            # Enfants pour 50% des militaires
            militaires_avec_enfants = random.sample(personnels, int(len(personnels) * 0.5))
            
            for personnel in militaires_avec_enfants:
                nb_enfants = random.randint(1, 4)
                for i in range(nb_enfants):
                    prenom_enfant = random.choice(PRENOMS_ENFANTS)
                    
                    enfant = Enfant(
                        matricule=personnel.matricule,
                        nom=personnel.nom,
                        prenom=prenom_enfant,
                        sexe_id=random.choice([1, 2]),
                        date_naissance=random_date_between(date(2005, 1, 1), date(2024, 12, 31)),
                        lieu_naissance=random.choice(VILLES_MAROCAINES),
                        date_deces=None if random.random() > 0.02 else random_date_between(date(2020, 1, 1), date(2024, 12, 31))
                    )
                    db.session.add(enfant)
                    enfants_count += 1
            
            db.session.commit()
            print(f"✅ Données familiales ajoutées: {conjoints_count} conjoints, {enfants_count} enfants")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur données familiales: {e}")

def add_movements_correct():
    """Ajoute les mouvements avec les bonnes structures"""
    print("\n🔄 Ajout des mouvements (structure corrigée)...")
    
    with app.app_context():
        try:
            personnels = Personnel.query.all()
            armes = ReferentielArme.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()
            
            mutations_count = 0
            avancements_count = 0
            sanctions_count = 0
            liberations_count = 0
            sejours_count = 0
            
            # Mutations selon structure réelle
            for personnel in random.sample(personnels, int(len(personnels) * 0.25)):
                mutation = MutationFonction(
                    matricule=personnel.matricule,
                    service_id=random.choice(armes).id_arme,
                    fonction=random.choice(["Chef d'équipe", "Instructeur", "Responsable matériel", "Secrétaire"]),
                    date_debut=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                    date_fin=random_date_between(date(2024, 6, 1), date(2025, 12, 31)) if random.choice([True, False]) else None
                )
                db.session.add(mutation)
                mutations_count += 1
            
            # Avancements selon structure réelle (grade_precedent_id et grade_suivant_id)
            for personnel in random.sample(personnels, int(len(personnels) * 0.2)):
                avancement = Avancement(
                    matricule=personnel.matricule,
                    grade_precedent_id=personnel.grade_actuel_id,
                    grade_suivant_id=random.choice(grades).id_grade,
                    date_avancement=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                    conditions=random.choice(["Ancienneté", "Au choix", "Mérite exceptionnel", "Formation complétée"])
                )
                db.session.add(avancement)
                avancements_count += 1
            
            # Sanctions selon structure réelle (type_sanction est Enum)
            for personnel in random.sample(personnels, int(len(personnels) * 0.08)):
                sanction = Sanction(
                    matricule=personnel.matricule,
                    date_sanction=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    type_sanction=random.choice(["sanction", "punition"]),  # Enum values
                    motif=random.choice(["Retard répété", "Négligence", "Insubordination mineure", "Absence non justifiée"]),
                    duree=random.randint(1, 15) if random.choice([True, False]) else None,
                    observation=f"Sanction prononcée par décision disciplinaire n°{random.randint(10, 99)}/2024"
                )
                db.session.add(sanction)
                sanctions_count += 1
            
            # Libérations selon structure réelle
            for personnel in random.sample(personnels, int(len(personnels) * 0.03)):
                liberation = Liberation(
                    matricule=personnel.matricule,
                    motif=random.choice(["Fin de contrat", "Démission", "Limite d'âge", "Convenance personnelle"]),
                    date_liberation=random_date_between(date(2024, 6, 1), date(2025, 12, 31)),
                    observation=f"Libération autorisée par décision n°{random.randint(100, 999)}/2024"
                )
                db.session.add(liberation)
                liberations_count += 1
            
            # Séjours opérationnels selon structure réelle
            for personnel in random.sample(personnels, int(len(personnels) * 0.4)):
                sejour = SejourOps(
                    matricule=personnel.matricule,
                    unite_id=random.choice(unites).id_unite,
                    date_debut=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    date_fin=random_date_between(date(2024, 1, 1), date(2024, 12, 31))
                )
                db.session.add(sejour)
                sejours_count += 1
            
            db.session.commit()
            print(f"✅ Mouvements ajoutés: {mutations_count} mutations, {avancements_count} avancements, {sanctions_count} sanctions, {liberations_count} libérations, {sejours_count} séjours")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur mouvements: {e}")

if __name__ == "__main__":
    print("🚀 Ajout final des situations avec structures correctes...")
    add_family_data_correct()
    add_movements_correct()
    
    # Statistiques finales complètes
    with app.app_context():
        print(f"\n📊 STATISTIQUES FINALES COMPLÈTES:")
        print(f"   👥 Personnel: {Personnel.query.count()}")
        print(f"   💑 Conjoints: {Conjoint.query.count()}")
        print(f"   👶 Enfants: {Enfant.query.count()}")
        print(f"   🏥 Situations médicales: {SituationMedicale.query.count()}")
        print(f"   💉 Vaccinations: {Vaccination.query.count()}")
        print(f"   📅 Permissions: {Permission.query.count()}")
        print(f"   🎓 PTC: {Ptc.query.count()}")
        print(f"   📍 Détachements: {Detachement.query.count()}")
        print(f"   ⚠️ Désertions: {Desertion.query.count()}")
        print(f"   🔄 Mutations: {MutationFonction.query.count()}")
        print(f"   ⬆️ Avancements: {Avancement.query.count()}")
        print(f"   ⚖️ Sanctions: {Sanction.query.count()}")
        print(f"   🚪 Libérations: {Liberation.query.count()}")
        print(f"   🎯 Séjours ops: {SejourOps.query.count()}")
    
    print("\n🎉 TOUTES LES SITUATIONS AJOUTÉES AVEC SUCCÈS!")
