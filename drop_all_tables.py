#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour supprimer toutes les tables de la base de données gestion_vehicules
"""

import mysql.connector
from mysql.connector import Error
import sys

def drop_all_tables():
    """Supprimer toutes les tables de la base de données"""
    try:
        # Connexion à MySQL
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',  # Modifiez si vous avez un mot de passe
            database='gestion_vehicules',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        
        cursor = connection.cursor()
        
        # Désactiver les contraintes de clés étrangères temporairement
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # Obtenir la liste de toutes les tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        if not tables:
            print("✅ Aucune table trouvée dans la base de données")
            return True
        
        print(f"🗑️ Suppression de {len(tables)} tables...")
        
        # Supprimer chaque table
        for (table_name,) in tables:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
                print(f"✅ Table '{table_name}' supprimée")
            except Error as e:
                print(f"❌ Erreur lors de la suppression de '{table_name}': {e}")
        
        # Réactiver les contraintes de clés étrangères
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("🎉 Toutes les tables ont été supprimées avec succès !")
        return True
        
    except Error as e:
        print(f"❌ Erreur de connexion à la base de données: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔄 Suppression de toutes les tables...")
    
    if drop_all_tables():
        print("✅ Opération terminée avec succès")
    else:
        print("❌ Échec de l'opération")
        sys.exit(1)

if __name__ == "__main__":
    main()
