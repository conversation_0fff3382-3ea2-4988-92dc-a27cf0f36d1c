<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Interface Courrier</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Diagnostic Interface Courrier</h1>
    
    <div class="test-section">
        <h2>1. Test de Connectivité API</h2>
        <button onclick="testAPI()">Tester les API</button>
        <div id="api-results"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test JavaScript</h2>
        <button onclick="testJavaScript()">Tester JavaScript</button>
        <div id="js-results"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Simulation Interface Courriers Arrivés</h2>
        <button onclick="simulateCourrierArrive()">Simuler Interface</button>
        <div id="simulation-results"></div>
        <table id="test-table" style="width: 100%; border-collapse: collapse; margin-top: 10px;">
            <thead>
                <tr style="background: #f0f0f0;">
                    <th style="border: 1px solid #ddd; padding: 8px;">ID</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Date</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Urgence</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Objet</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Expéditeur</th>
                </tr>
            </thead>
            <tbody id="test-tbody">
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>4. Logs de Débogage</h2>
        <div id="debug-logs" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const debugLogs = document.getElementById('debug-logs');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            debugLogs.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            console.log(`[${timestamp}] ${message}`);
        }

        async function testAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>🔄 Test en cours...</p>';
            
            const tests = [
                { url: '/api/courriers/arrives', name: 'Courriers Arrivés' },
                { url: '/api/courriers/envoyes', name: 'Courriers Envoyés' },
                { url: '/api/courriers', name: 'Courriers Généraux' }
            ];
            
            let results = '';
            
            for (const test of tests) {
                try {
                    log(`Test API: ${test.name} (${test.url})`);
                    const response = await fetch(test.url);
                    
                    if (response.ok) {
                        const data = await response.json();
                        results += `<p class="success">✅ ${test.name}: ${data.length} éléments</p>`;
                        log(`✅ ${test.name}: ${data.length} éléments`, 'success');
                        
                        if (data.length > 0) {
                            results += `<div class="log">Premier élément: ${JSON.stringify(data[0], null, 2)}</div>`;
                        }
                    } else {
                        results += `<p class="error">❌ ${test.name}: Erreur ${response.status}</p>`;
                        log(`❌ ${test.name}: Erreur ${response.status}`, 'error');
                    }
                } catch (error) {
                    results += `<p class="error">❌ ${test.name}: ${error.message}</p>`;
                    log(`❌ ${test.name}: ${error.message}`, 'error');
                }
            }
            
            resultsDiv.innerHTML = results;
        }

        function testJavaScript() {
            const resultsDiv = document.getElementById('js-results');
            let results = '';
            
            // Test des fonctions de base
            try {
                log('Test JavaScript: Fonctions de base');
                
                // Test fetch
                if (typeof fetch !== 'undefined') {
                    results += '<p class="success">✅ fetch() disponible</p>';
                } else {
                    results += '<p class="error">❌ fetch() non disponible</p>';
                }
                
                // Test async/await
                const testAsync = async () => {
                    return 'async/await fonctionne';
                };
                
                testAsync().then(result => {
                    results += '<p class="success">✅ async/await fonctionne</p>';
                    log('✅ async/await fonctionne', 'success');
                }).catch(error => {
                    results += '<p class="error">❌ async/await ne fonctionne pas</p>';
                    log('❌ async/await ne fonctionne pas', 'error');
                });
                
                // Test DOM
                const testElement = document.createElement('div');
                if (testElement) {
                    results += '<p class="success">✅ Manipulation DOM disponible</p>';
                    log('✅ Manipulation DOM disponible', 'success');
                } else {
                    results += '<p class="error">❌ Manipulation DOM non disponible</p>';
                    log('❌ Manipulation DOM non disponible', 'error');
                }
                
                // Test JSON
                try {
                    const testObj = { test: 'value' };
                    const jsonStr = JSON.stringify(testObj);
                    const parsedObj = JSON.parse(jsonStr);
                    results += '<p class="success">✅ JSON disponible</p>';
                    log('✅ JSON disponible', 'success');
                } catch (e) {
                    results += '<p class="error">❌ JSON non disponible</p>';
                    log('❌ JSON non disponible', 'error');
                }
                
            } catch (error) {
                results += `<p class="error">❌ Erreur JavaScript: ${error.message}</p>`;
                log(`❌ Erreur JavaScript: ${error.message}`, 'error');
            }
            
            resultsDiv.innerHTML = results;
        }

        async function simulateCourrierArrive() {
            const resultsDiv = document.getElementById('simulation-results');
            const tbody = document.getElementById('test-tbody');
            
            resultsDiv.innerHTML = '<p>🔄 Simulation en cours...</p>';
            tbody.innerHTML = '';
            
            try {
                log('Simulation: Chargement des courriers arrivés');
                
                const response = await fetch('/api/courriers/arrives');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const courriers = await response.json();
                log(`Simulation: ${courriers.length} courriers récupérés`, 'success');
                
                if (courriers.length === 0) {
                    resultsDiv.innerHTML = '<p class="info">ℹ️ Aucun courrier trouvé</p>';
                    return;
                }
                
                // Remplir le tableau
                courriers.forEach((courrier, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td style="border: 1px solid #ddd; padding: 8px;">${courrier.id}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${courrier.date_arrivee}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">
                            <span style="background: ${getUrgenceColor(courrier.urgence)}; color: white; padding: 2px 6px; border-radius: 3px;">
                                ${courrier.urgence}
                            </span>
                        </td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${courrier.objet}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">${courrier.expediteur}</td>
                    `;
                    tbody.appendChild(row);
                });
                
                resultsDiv.innerHTML = `<p class="success">✅ ${courriers.length} courriers affichés dans le tableau</p>`;
                log(`Simulation: ${courriers.length} courriers affichés dans le tableau`, 'success');
                
            } catch (error) {
                resultsDiv.innerHTML = `<p class="error">❌ Erreur: ${error.message}</p>`;
                log(`Simulation: Erreur - ${error.message}`, 'error');
            }
        }
        
        function getUrgenceColor(urgence) {
            const colors = {
                'extreme': '#dc3545',
                'extreme_urgent': '#fd7e14',
                'urgent': '#ffc107',
                'routine': '#28a745'
            };
            return colors[urgence] || '#6c757d';
        }

        // Auto-test au chargement
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page de diagnostic chargée');
            log('Cliquez sur les boutons pour lancer les tests');
        });
    </script>
</body>
</html>
