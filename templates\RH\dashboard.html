{% extends "rh/base_rh.html" %}

{% block title %}Dashboard RH - Forces Armées Royales{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête du Dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-0">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard RH
                            </h1>
                            <p class="mb-0 mt-2" style="color: var(--text-light);">
                                Division des Ressources Humaines - Inspection de l'Artillerie
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military">
                                    <i class="fas fa-user-plus"></i> Nouveau Militaire
                                </a>
                                <a href="{{ url_for('init_rh_database') }}" class="btn btn-warning" style="margin-left: 10px;">
                                    <i class="fas fa-database"></i> Init DB
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertes -->
    {% if alertes %}
    <div class="row mb-4">
        <div class="col-12">
            {% for alerte in alertes %}
            <div class="alert alert-{{ alerte.type }} alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Alerte :</strong> {{ alerte.message }}
                {% if alerte.url %}
                <a href="{{ alerte.url }}" class="btn btn-sm btn-outline-{{ alerte.type }} ms-2">
                    <i class="fas fa-eye"></i> Voir
                </a>
                {% endif %}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Statistiques Principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-users stat-icon"></i>
                <div class="stat-number">{{ total_personnel }}</div>
                <div class="stat-label">Total Personnel</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-heartbeat stat-icon"></i>
                <div class="stat-number">{{ total_aptes }}</div>
                <div class="stat-label">Aptes au Service</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-calendar-alt stat-icon"></i>
                <div class="stat-number">{{ permissions_recentes }}</div>
                <div class="stat-label">Permissions (30j)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-exchange-alt stat-icon"></i>
                <div class="stat-number">{{ detachements_en_cours }}</div>
                <div class="stat-label">Détachements en cours</div>
            </div>
        </div>
    </div>

    <!-- Graphiques et Statistiques Détaillées -->
    <div class="row mb-4">
        <!-- Répartition par Catégorie -->
        <div class="col-lg-6 mb-4">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        Répartition par Catégorie
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats_categories %}
                    <div class="row">
                        {% for categorie, count in stats_categories %}
                        <div class="col-12 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold">{{ categorie }}</span>
                                <span class="badge badge-military">{{ count }}</span>
                            </div>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: {{ (count / total_personnel * 100) if total_personnel > 0 else 0 }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune donnée disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top 5 Grades -->
        <div class="col-lg-6 mb-4">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-star"></i>
                        Top 5 Grades
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats_grades %}
                    <div class="row">
                        {% for grade, count in stats_grades %}
                        <div class="col-12 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold">{{ grade }}</span>
                                <span class="badge badge-military">{{ count }}</span>
                            </div>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: {{ (count / total_personnel * 100) if total_personnel > 0 else 0 }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune donnée disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Top 5 Unités -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-building"></i>
                        Top 5 Unités par Effectif
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats_unites %}
                    <div class="row">
                        {% for unite, count in stats_unites %}
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold">{{ unite[:30] }}{% if unite|length > 30 %}...{% endif %}</span>
                                <span class="badge badge-military">{{ count }}</span>
                            </div>
                            <div class="progress mt-2" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: {{ (count / total_personnel * 100) if total_personnel > 0 else 0 }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Aucune donnée disponible</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Situation Médicale -->
        <div class="col-lg-4">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-heartbeat"></i>
                        Situation Médicale
                    </h5>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <div class="stat-number text-success">{{ total_aptes }}</div>
                            <div class="stat-label">Aptes</div>
                        </div>
                        <div class="mb-3">
                            <div class="stat-number text-danger">{{ total_inaptes }}</div>
                            <div class="stat-label">Inaptes</div>
                        </div>
                        {% if total_aptes + total_inaptes > 0 %}
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar bg-success" style="width: {{ (total_aptes / (total_aptes + total_inaptes) * 100) }}%">
                                {{ "%.1f"|format(total_aptes / (total_aptes + total_inaptes) * 100) }}%
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-military w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-search fa-2x mb-2"></i>
                                <span>Rechercher Personnel</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('rh.nouveau_militaire') }}" class="btn btn-success-military w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <span>Nouveau Militaire</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('rh.recherche_personnel', alerte='cin') }}" class="btn btn-warning w-100 h-100 d-flex flex-column justify-content-center">
                                <i class="fas fa-id-card fa-2x mb-2"></i>
                                <span>CIN à Renouveler</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-info w-100 h-100 d-flex flex-column justify-content-center" onclick="alert('Fonctionnalité en développement')">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Rapports</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Animation des statistiques
document.addEventListener('DOMContentLoaded', function() {
    // Animation des nombres
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 30);
    });
    
    // Animation des barres de progression
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach((bar, index) => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.transition = 'width 1s ease-in-out';
            bar.style.width = width;
        }, index * 200);
    });
});
</script>
{% endblock %}
