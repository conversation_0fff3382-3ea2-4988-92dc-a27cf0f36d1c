{% extends "base.html" %}

{% block title %}Dashboard RH - Gestion du Personnel{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête du dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt text-primary"></i>
                    Dashboard RH - Gestion du Personnel
                </h1>
                <div class="text-muted">
                    <i class="fas fa-calendar-alt"></i>
                    {{ date_actuelle }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Personnel
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_personnel or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Officiers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_officiers or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Officiers du Rang
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_officiers_rang or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-medal fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Militaires du Rang
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_militaires_rang or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et statistiques -->
    <div class="row mb-4">
        <!-- Répartition par armes -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i>
                        Répartition par Armes
                    </h6>
                </div>
                <div class="card-body">
                    {% if stats_armes %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Arme</th>
                                        <th class="text-right">Effectif</th>
                                        <th class="text-right">%</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in stats_armes %}
                                    <tr>
                                        <td>{{ stat.arme }}</td>
                                        <td class="text-right">{{ stat.effectif }}</td>
                                        <td class="text-right">
                                            {% if total_personnel > 0 %}
                                                {{ "%.1f"|format((stat.effectif / total_personnel) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Répartition par unités -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i>
                        Répartition par Unités (Top 10)
                    </h6>
                </div>
                <div class="card-body">
                    {% if stats_unites %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Unité</th>
                                        <th class="text-right">Effectif</th>
                                        <th class="text-right">%</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in stats_unites[:10] %}
                                    <tr>
                                        <td>{{ stat.unite }}</td>
                                        <td class="text-right">{{ stat.effectif }}</td>
                                        <td class="text-right">
                                            {% if total_personnel > 0 %}
                                                {{ "%.1f"|format((stat.effectif / total_personnel) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Activités récentes et alertes -->
    <div class="row">
        <!-- Nouveaux engagés -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-user-plus"></i>
                        Nouveaux Engagés (30 derniers jours)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="h4 mb-0 font-weight-bold text-success">
                        {{ nouveaux_engages or 0 }}
                    </div>
                    <div class="text-muted">nouveaux militaires</div>
                </div>
            </div>
        </div>

        <!-- Situations en cours -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-clock"></i>
                        Situations en cours
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Permissions
                            </div>
                            <div class="h6 mb-2 font-weight-bold text-gray-800">
                                {{ permissions_en_cours or 0 }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Détachements
                            </div>
                            <div class="h6 mb-2 font-weight-bold text-gray-800">
                                {{ detachements_en_cours or 0 }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                PTC en cours
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                {{ ptc_en_cours or 0 }}
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Aptes service
                            </div>
                            <div class="h6 mb-0 font-weight-bold text-gray-800">
                                {{ total_aptes or 0 }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt"></i>
                        Actions rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.ajouter_personnel') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus"></i>
                                Ajouter Personnel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-info btn-block">
                                <i class="fas fa-search"></i>
                                Rechercher
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-success btn-block">
                                <i class="fas fa-list"></i>
                                Liste Personnel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.rapports') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-line"></i>
                                Rapports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
{% endblock %}
