{% extends "base.html" %}

{% block title %}Dashboard RH - Gestion du Personnel{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête du dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white">
                <h1 class="display-4">
                    <i class="fas fa-tachometer-alt"></i>
                    Tableau de Bord RH
                </h1>
                <p class="lead">Gestion des Ressources Humaines - Personnel Militaire</p>
                <div class="text-muted">
                    <i class="fas fa-calendar-alt"></i>
                    {{ date_actuelle }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <div class="text-primary">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                    <h3 class="mt-3">{{ total_personnel }}</h3>
                    <p class="text-muted">Total Personnel</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <div class="text-success">
                        <i class="fas fa-star fa-3x"></i>
                    </div>
                    <h3 class="mt-3">{{ total_officiers }}</h3>
                    <p class="text-muted">Officiers</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <div class="text-info">
                        <i class="fas fa-medal fa-3x"></i>
                    </div>
                    <h3 class="mt-3">{{ total_officiers_rang }}</h3>
                    <p class="text-muted">Officiers du Rang</p>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <div class="text-warning">
                        <i class="fas fa-user-tie fa-3x"></i>
                    </div>
                    <h3 class="mt-3">{{ total_militaires_rang }}</h3>
                    <p class="text-muted">Militaires du Rang</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et statistiques -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-chart-pie"></i>
                        Répartition par Armes
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartArmes" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        Répartition par Unités
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="chartUnites" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Activités et alertes -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Nouveaux Engagés
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ nouveaux_engages }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Permissions en cours
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ permissions_en_cours }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plane fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                PTC en cours
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ ptc_en_cours }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-graduation-cap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-left-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Détachements
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ detachements_en_cours }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Situation médicale -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-3">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Personnel Apte
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_aptes }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-3">
            <div class="card border-left-danger">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Personnel Inapte
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_inaptes }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt"></i>
                        Actions Rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.ajouter_personnel') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus"></i>
                                Ajouter Personnel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.recherche_personnel') }}" class="btn btn-info btn-block">
                                <i class="fas fa-search"></i>
                                Rechercher
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.liste_personnel') }}" class="btn btn-success btn-block">
                                <i class="fas fa-list"></i>
                                Liste Personnel
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="{{ url_for('rh.rapports') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-line"></i>
                                Rapports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique des armes
var ctxArmes = document.getElementById('chartArmes').getContext('2d');
var chartArmes = new Chart(ctxArmes, {
    type: 'pie',
    data: {
        labels: [{% for stat in stats_armes %}'{{ stat.arme }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for stat in stats_armes %}{{ stat.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Graphique des unités
var ctxUnites = document.getElementById('chartUnites').getContext('2d');
var chartUnites = new Chart(ctxUnites, {
    type: 'bar',
    data: {
        labels: [{% for stat in stats_unites %}'{{ stat.unite }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Effectif',
            data: [{% for stat in stats_unites %}{{ stat.total }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: '#36A2EB',
            borderColor: '#36A2EB',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
