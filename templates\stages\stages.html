{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Gestion des Stages</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStageModal">
            <i class="fas fa-plus"></i> Nouveau Stage
        </button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Liste des Stages</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <select class="form-select" id="filterType">
                                <option value="">Tous les types</option>
                                <option value="bcm">BCM</option>
                                <option value="be">BE</option>
                                <option value="bs">BS</option>
                                <option value="application">APPLICATION</option>
                                <option value="perfectionnement">PERFECTIONNEMENT</option>
                                <option value="cdc">CDC</option>
                                <option value="coordonnateur">COORDONNATEUR TACTIQUE</option>
                            </select>
                            <input type="text" class="form-control" placeholder="Rechercher...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Stagiaire</th>
                                <th>Date début</th>
                                <th>Date fin</th>
                                <th>Durée</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stage in stages %}
                            <tr>
                                <td>{{ stage.type_stage.libelle }}</td>
                                <td>
                                    <img src="{{ url_for('static', filename='images/default-avatar.png') }}"
                                        class="rounded-circle me-2" width="32" height="32">
                                    <span>ZIANI Amine</span>
                                </td>
                                <td>{{ stage.date_debut.strftime('%d/%m/%Y') }}</td>
                                <td>{{ stage.date_fin.strftime('%d/%m/%Y') }}</td>
                                <td>6 mois</td>
                                <td>
                                    {% if stage.statut == 'En cours' %}
                                        <span class="badge bg-success">{{ stage.statut }}</span>
                                    {% elif stage.statut == 'Planifié' %}
                                        <span class="badge bg-info">{{ stage.statut }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ stage.statut }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajout Stage -->
<div class="modal fade" id="addStageModal" tabindex="-1" aria-labelledby="addStageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStageModalLabel">Nouveau Stage</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addStageForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="typeStage" class="form-label">Type de stage</label>
                            <select class="form-select" id="typeStage" required>
                                <option value="">Sélectionner un type</option>
                                <option value="bcm">BCM</option>
                                <option value="be">BE</option>
                                <option value="bs">BS</option>
                                <option value="application">APPLICATION</option>
                                <option value="perfectionnement">PERFECTIONNEMENT</option>
                                <option value="cdc">CDC</option>
                                <option value="coordonnateur">COORDONNATEUR TACTIQUE</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="stagiaire" class="form-label">Stagiaire</label>
                            <select class="form-select" id="stagiaire" required>
                                <option value="">Sélectionner un stagiaire</option>
                                <option value="1">ZIANI Amine</option>
                                <option value="2">BENALI Youssef</option>
                                <option value="3">TAZI Ahmed</option>
                                <option value="4">IDRISSI Omar</option>
                                <option value="5">ALAOUI Karim</option>
                                <option value="6">EL AMRANI Mohammed</option>
                                <option value="7">BENJELLOUN Hamza</option>
                                <option value="8">BOUAZZAOUI Mehdi</option>
                                <option value="9">CHRAIBI Nabil</option>
                                <option value="10">FASSI Rachid</option>
                            </select>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="dateDebut" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="dateDebut" required>
                        </div>
                        <div class="col-md-6">
                            <label for="dateFin" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="dateFin" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="objectifs" class="form-label">Objectifs du stage</label>
                        <textarea class="form-control" id="objectifs" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="maitre_stage" class="form-label">Maître de stage</label>
                        <input type="text" class="form-control" id="maitre_stage">
                    </div>

                    <div class="mb-3">
                        <label for="commentaires" class="form-label">Commentaires</label>
                        <textarea class="form-control" id="commentaires" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion des filtres
    const filterType = document.getElementById('filterType');
    const searchInput = document.querySelector('input[type="text"]');

    filterType.addEventListener('change', function() {
        // Implémenter la logique de filtrage ici
        console.log('Type sélectionné:', this.value);
    });

    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });

    // Validation des dates
    const dateDebut = document.getElementById('dateDebut');
    const dateFin = document.getElementById('dateFin');

    dateFin.addEventListener('change', function() {
        if (dateDebut.value && this.value) {
            if (new Date(this.value) <= new Date(dateDebut.value)) {
                alert('La date de fin doit être postérieure à la date de début');
                this.value = '';
            }
        }
    });
});
</script>
{% endblock %} 