#!/usr/bin/env python3
"""
Script de test pour vérifier le bon fonctionnement des API de gestion de courrier
"""

import requests
import json
from datetime import datetime, date

# Configuration
BASE_URL = "http://127.0.0.1:3000"

def test_api_courriers_arrives():
    """Test de l'API des courriers arrivés"""
    print("🔍 Test de l'API des courriers arrivés...")
    
    # Test GET - Récupérer tous les courriers arrivés
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        print(f"GET /api/courriers/arrives - Status: {response.status_code}")
        if response.status_code == 200:
            courriers = response.json()
            print(f"✅ Nombre de courriers arrivés: {len(courriers)}")
            if courriers:
                print(f"   Premier courrier: {courriers[0]['id']} - {courriers[0]['objet']}")
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    
    # Test POST - Ajouter un nouveau courrier arrivé
    try:
        nouveau_courrier = {
            "id": f"CA-TEST-{int(datetime.now().timestamp())}",
            "urgence": "urgent",
            "nature": "message",
            "date_arrivee": date.today().strftime('%Y-%m-%d'),
            "date_signature": date.today().strftime('%Y-%m-%d'),
            "numero_ecrit": f"TEST-{int(datetime.now().timestamp())}",
            "expediteur": "Test Expediteur",
            "objet": "Test d'ajout de courrier arrivé via API",
            "classification": "public",
            "annotation": "Test automatique",
            "divisions_action": ["technique", "instruction"],
            "divisions_info": ["rh"]
        }
        
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(nouveau_courrier)
        )
        
        print(f"POST /api/courriers/arrives - Status: {response.status_code}")
        if response.status_code == 201:
            courrier_ajoute = response.json()
            print(f"✅ Courrier ajouté: {courrier_ajoute['id']}")
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")

def test_api_courriers_envoyes():
    """Test de l'API des courriers envoyés"""
    print("\n🔍 Test de l'API des courriers envoyés...")
    
    # Test GET - Récupérer tous les courriers envoyés
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        print(f"GET /api/courriers/envoyes - Status: {response.status_code}")
        if response.status_code == 200:
            courriers = response.json()
            print(f"✅ Nombre de courriers envoyés: {len(courriers)}")
            if courriers:
                print(f"   Premier courrier: {courriers[0]['id']} - {courriers[0]['objet']}")
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    
    # Test POST - Ajouter un nouveau courrier envoyé
    try:
        nouveau_courrier = {
            "id": f"CE-TEST-{int(datetime.now().timestamp())}",
            "numero_ecrit": f"TEST-ENV-{int(datetime.now().timestamp())}",
            "division_emettrice": "technique",
            "date_depart": date.today().strftime('%Y-%m-%d'),
            "nature": "message",
            "objet": "Test d'ajout de courrier envoyé via API",
            "destinataire": "Test Destinataire",
            "observations": "Test automatique"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/courriers/envoyes",
            headers={"Content-Type": "application/json"},
            data=json.dumps(nouveau_courrier)
        )
        
        print(f"POST /api/courriers/envoyes - Status: {response.status_code}")
        if response.status_code == 201:
            courrier_ajoute = response.json()
            print(f"✅ Courrier ajouté: {courrier_ajoute['id']}")
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")

def test_api_courriers_general():
    """Test de l'API générale des courriers"""
    print("\n🔍 Test de l'API générale des courriers...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers")
        print(f"GET /api/courriers - Status: {response.status_code}")
        if response.status_code == 200:
            courriers = response.json()
            print(f"✅ Nombre total de courriers: {len(courriers)}")
        else:
            print(f"❌ Erreur: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")

def main():
    """Fonction principale de test"""
    print("🚀 Début des tests des API de gestion de courrier")
    print("=" * 60)
    
    # Vérifier que l'application est accessible
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ L'application n'est pas accessible sur {BASE_URL}")
            return
    except Exception as e:
        print(f"❌ Impossible de se connecter à l'application: {e}")
        return
    
    print(f"✅ Application accessible sur {BASE_URL}")
    
    # Exécuter les tests
    test_api_courriers_arrives()
    test_api_courriers_envoyes()
    test_api_courriers_general()
    
    print("\n" + "=" * 60)
    print("🏁 Tests terminés")

if __name__ == "__main__":
    main()
