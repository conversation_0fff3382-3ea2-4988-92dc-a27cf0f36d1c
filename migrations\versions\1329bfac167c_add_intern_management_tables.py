"""add intern management tables

Revision ID: 1329bfac167c
Revises: 2e53bb172a5f
Create Date: 2025-06-30 12:48:35.189888

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '1329bfac167c'
down_revision = '2e53bb172a5f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('promotion',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('nom', sa.String(length=100), nullable=False),
    sa.Column('annee', sa.Integer(), nullable=False),
    sa.Column('filiere', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stage_type',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('nom', sa.String(length=100), nullable=False),
    sa.Column('conditions_admission', sa.Text(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stagiaire',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('nom', sa.String(length=100), nullable=False),
    sa.Column('prenom', sa.String(length=100), nullable=False),
    sa.Column('date_naissance', sa.Date(), nullable=False),
    sa.Column('email', sa.String(length=120), nullable=False),
    sa.Column('telephone', sa.String(length=20), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    op.create_table('document',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=False),
    sa.Column('chemin_fichier', sa.String(length=255), nullable=False),
    sa.Column('date_import', sa.DateTime(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('notification',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('date_envoi', sa.DateTime(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('prise_arme',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date_evenement', sa.Date(), nullable=False),
    sa.Column('presence', sa.Boolean(), nullable=True),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rapport',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=100), nullable=False),
    sa.Column('date_creation', sa.DateTime(), nullable=False),
    sa.Column('contenu', sa.Text(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('stage',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('date_debut', sa.Date(), nullable=False),
    sa.Column('date_fin', sa.Date(), nullable=False),
    sa.Column('statut', sa.Enum('en_attente', 'valide', 'termine', name='statut_enum'), nullable=False),
    sa.Column('id_type', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id_type'], ['stage_type.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('inscription',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_stagiaire', sa.Integer(), nullable=False),
    sa.Column('id_stage', sa.Integer(), nullable=False),
    sa.Column('id_promotion', sa.Integer(), nullable=False),
    sa.Column('date_inscription', sa.Date(), nullable=False),
    sa.ForeignKeyConstraint(['id_promotion'], ['promotion.id'], ),
    sa.ForeignKeyConstraint(['id_stage'], ['stage.id'], ),
    sa.ForeignKeyConstraint(['id_stagiaire'], ['stagiaire.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('inscription')
    op.drop_table('stage')
    op.drop_table('rapport')
    op.drop_table('prise_arme')
    op.drop_table('notification')
    op.drop_table('document')
    op.drop_table('stagiaire')
    op.drop_table('stage_type')
    op.drop_table('promotion')
    # ### end Alembic commands ###
