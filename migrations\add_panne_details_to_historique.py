from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import sys
import os

# Ajout du répertoire parent au path pour pouvoir importer app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app, db

def upgrade():
    with app.app_context():
        # Ajout des nouvelles colonnes
        with db.engine.connect() as conn:
            conn.execute(db.text('ALTER TABLE vehicule_historique ADD COLUMN IF NOT EXISTS type_panne VARCHAR(100)'))
            conn.execute(db.text('ALTER TABLE vehicule_historique ADD COLUMN IF NOT EXISTS date_panne DATE'))
            
            # Mise à jour des données existantes
            result = conn.execute(db.text('''
                SELECT vh.id, vh.vehicule_id, v.type_panne, v.date_panne 
                FROM vehicule_historique vh
                JOIN vehicule v ON vh.vehicule_id = v.id
            '''))
            
            for vehicle in result:
                conn.execute(
                    db.text('UPDATE vehicule_historique SET type_panne = :type_panne, date_panne = :date_panne WHERE id = :id'),
                    {"type_panne": vehicle.type_panne, "date_panne": vehicle.date_panne, "id": vehicle.id}
                )
            
            conn.commit()
            print("Migration terminée avec succès!")

if __name__ == '__main__':
    upgrade()
