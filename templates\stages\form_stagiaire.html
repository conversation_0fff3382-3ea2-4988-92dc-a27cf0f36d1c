{% extends "stages/base_stages.html" %}

{% block title %}{{ action }} un Stagiaire{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{{ action }} un Stagiaire</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" value="{{ stagiaire.nom if stagiaire else '' }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" name="prenom" value="{{ stagiaire.prenom if stagiaire else '' }}" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="email" class="form-label">Adresse Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="{{ stagiaire.email if stagiaire else '' }}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="date_naissance" class="form-label">Date de Naissance</label>
                        <input type="date" class="form-control" id="date_naissance" name="date_naissance" value="{{ stagiaire.date_naissance.strftime('%Y-%m-%d') if stagiaire else '' }}" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="telephone" class="form-label">Téléphone</label>
                        <input type="text" class="form-control" id="telephone" name="telephone" value="{{ stagiaire.telephone if stagiaire else '' }}">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="3">{{ stagiaire.adresse if stagiaire else '' }}</textarea>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="photo" class="form-label">Photo du Stagiaire</label>
                    <input type="file" class="form-control" id="photo" name="photo">
                    {% if stagiaire and stagiaire.photo %}
                        <small class="form-text text-muted">Une photo est déjà enregistrée. Laisser vide pour ne pas la modifier.</small>
                    {% endif %}
                </div>
                
                <hr>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_stagiaires') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {{ action }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 