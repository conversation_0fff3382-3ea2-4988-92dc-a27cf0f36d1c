#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple pour créer la table detachement manquante
"""

import mysql.connector
from mysql.connector import Error

def create_detachement_table():
    """Créer la table detachement"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        print("🔧 Création de la table detachement...")
        
        # Créer la table detachement
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS detachement (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                lieu_detachement VARCHAR(200) NOT NULL,
                adresse_detachement VARCHAR(500) NULL,
                pays VARCHAR(100) NOT NULL DEFAULT 'Maroc',
                organisme_accueil VARCHAR(200) NULL,
                fonction_detachement VARCHAR(200) NULL,
                numero_decision VARCHAR(50) NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        print("✅ Table detachement créée avec succès")
        
        # Créer aussi la table desertion
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS desertion (
                id INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_absence DATE NOT NULL,
                date_retour DATE NULL,
                date_arret_solde DATE NULL,
                date_prise_solde DATE NULL,
                motif TEXT NULL,
                circonstances TEXT NULL,
                sanctions TEXT NULL,
                observations TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        print("✅ Table desertion créée avec succès")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    if create_detachement_table():
        print("🎉 Tables créées avec succès !")
    else:
        print("❌ Échec de la création")
