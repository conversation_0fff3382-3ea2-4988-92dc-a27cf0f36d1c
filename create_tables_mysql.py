#!/usr/bin/env python3
"""
Créer les tables RH directement avec MySQL
"""

import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>

def create_rh_tables():
    """Créer toutes les tables RH"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔧 Création des tables de données RH...")
        
        # Table personnel (principale)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS personnel (
                matricule VARCHAR(20) PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                nom_arabe VARCHAR(100) NOT NULL,
                prenom_arabe VARCHAR(100) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                sexe_id INT NOT NULL,
                categorie_id INT NOT NULL,
                groupe_sanguin_id INT NOT NULL,
                numero_cin VARCHAR(20) NOT NULL,
                date_delivrance_cin DATE NOT NULL,
                date_expiration_cin DATE NOT NULL,
                gsm VARCHAR(20) NOT NULL,
                telephone_domicile VARCHAR(20),
                taille DECIMAL(5,2) NOT NULL,
                lieu_residence VARCHAR(150) NOT NULL,
                arme_id INT NOT NULL,
                specialite_id INT,
                unite_id INT NOT NULL,
                grade_actuel_id INT NOT NULL,
                fonction VARCHAR(100) NOT NULL,
                date_prise_fonction DATE NOT NULL,
                ccp VARCHAR(50) NOT NULL,
                compte_bancaire VARCHAR(50),
                numero_somme VARCHAR(50) NOT NULL,
                date_engagement DATE NOT NULL,
                nom_pere VARCHAR(100) NOT NULL,
                prenom_pere VARCHAR(100) NOT NULL,
                nom_mere VARCHAR(100) NOT NULL,
                prenom_mere VARCHAR(100) NOT NULL,
                adresse_parents VARCHAR(200) NOT NULL,
                situation_fam_id INT NOT NULL,
                nombre_enfants INT,
                numero_passport VARCHAR(50),
                date_delivrance_passport DATE,
                date_expiration_passport DATE,
                gsm_urgence VARCHAR(20) NOT NULL,
                degre_parente_id INT NOT NULL
            )
        """)
        print("✅ Table personnel créée")
        
        # Table personnel_langue
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS personnel_langue (
                matricule VARCHAR(20) NOT NULL,
                langue_id INT NOT NULL,
                PRIMARY KEY (matricule, langue_id)
            )
        """)
        print("✅ Table personnel_langue créée")
        
        # Table conjoint
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conjoint (
                id_conjoint INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL UNIQUE,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                nom_arabe VARCHAR(100) NOT NULL,
                prenom_arabe VARCHAR(100) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                lieu_naissance_arabe VARCHAR(100) NOT NULL,
                adresse VARCHAR(200) NOT NULL,
                adresse_arabe VARCHAR(200) NOT NULL,
                date_mariage DATE NOT NULL,
                lieu_mariage VARCHAR(100) NOT NULL,
                profession VARCHAR(100) NOT NULL,
                profession_arabe VARCHAR(100) NOT NULL,
                numero_cin VARCHAR(20) NOT NULL,
                gsm VARCHAR(20) NOT NULL,
                nom_pere VARCHAR(100) NOT NULL,
                prenom_pere VARCHAR(100) NOT NULL,
                nom_arabe_pere VARCHAR(100) NOT NULL,
                prenom_arabe_pere VARCHAR(100) NOT NULL,
                nom_mere VARCHAR(100) NOT NULL,
                prenom_mere VARCHAR(100) NOT NULL,
                nom_arabe_mere VARCHAR(100) NOT NULL,
                prenom_arabe_mere VARCHAR(100) NOT NULL,
                profession_pere VARCHAR(100) NOT NULL,
                profession_mere VARCHAR(100) NOT NULL
            )
        """)
        print("✅ Table conjoint créée")
        
        # Table enfant
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS enfant (
                id_enfant INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                sexe_id INT NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                date_deces DATE
            )
        """)
        print("✅ Table enfant créée")
        
        # Table situation_medicale
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS situation_medicale (
                id_sitmed INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL UNIQUE,
                maladies TEXT NOT NULL,
                date_hospitalisation DATE NOT NULL,
                lieu_hospitalisation VARCHAR(100) NOT NULL,
                aptitude ENUM('apte','inapte') NOT NULL,
                observations TEXT
            )
        """)
        print("✅ Table situation_medicale créée")
        
        # Table vaccination
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vaccination (
                id_vaccination INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_vaccination DATE NOT NULL,
                objet VARCHAR(100) NOT NULL,
                observation TEXT
            )
        """)
        print("✅ Table vaccination créée")
        
        # Table ptc
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ptc (
                id_ptc INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_ptc DATE NOT NULL,
                duree INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                objet VARCHAR(100) NOT NULL,
                observations TEXT
            )
        """)
        print("✅ Table ptc créée")
        
        # Table permission
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permission (
                id_permission INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                adresse VARCHAR(200) NOT NULL,
                numero_serie VARCHAR(50) NOT NULL
            )
        """)
        print("✅ Table permission créée")
        
        # Table desertion
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS desertion (
                id_desertion INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_absence DATE NOT NULL,
                date_desertion DATE NOT NULL,
                date_retour DATE NOT NULL,
                date_arret_solde DATE,
                date_prise_solde DATE
            )
        """)
        print("✅ Table desertion créée")
        
        # Table detachement
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS detachement (
                id_detachement INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                adresse_detachement VARCHAR(200) NOT NULL,
                pays VARCHAR(100) NOT NULL,
                date_fin DATE NOT NULL
            )
        """)
        print("✅ Table detachement créée")
        
        # Table mutation_fonction
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mutation_fonction (
                id_mutation INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                service_id INT NOT NULL,
                fonction VARCHAR(100) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE
            )
        """)
        print("✅ Table mutation_fonction créée")
        
        # Table liberation
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS liberation (
                id_liberation INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                motif VARCHAR(200) NOT NULL,
                date_liberation DATE NOT NULL,
                observation TEXT
            )
        """)
        print("✅ Table liberation créée")
        
        # Table avancement
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS avancement (
                id_avancement INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                grade_precedent_id INT NOT NULL,
                grade_suivant_id INT NOT NULL,
                date_avancement DATE NOT NULL,
                conditions TEXT
            )
        """)
        print("✅ Table avancement créée")
        
        # Table sanction
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sanction (
                id_sanction INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_sanction DATE NOT NULL,
                type_sanction ENUM('sanction','punition') NOT NULL,
                duree INT,
                motif VARCHAR(200) NOT NULL,
                observation TEXT
            )
        """)
        print("✅ Table sanction créée")
        
        connection.commit()
        print("🎉 Toutes les tables de données créées!")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def create_sample_data():
    """Créer des données d'exemple"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("\n🔧 Création de données d'exemple...")
        
        # Vérifier si des données existent déjà
        cursor.execute("SELECT COUNT(*) FROM personnel")
        count = cursor.fetchone()[0]
        
        if count == 0:
            # Insérer un exemple de personnel
            cursor.execute("""
                INSERT INTO personnel (
                    matricule, nom, prenom, nom_arabe, prenom_arabe, date_naissance, lieu_naissance,
                    sexe_id, categorie_id, groupe_sanguin_id, numero_cin, date_delivrance_cin, date_expiration_cin,
                    gsm, taille, lieu_residence, arme_id, unite_id, grade_actuel_id, fonction, date_prise_fonction,
                    ccp, numero_somme, date_engagement, nom_pere, prenom_pere, nom_mere, prenom_mere,
                    adresse_parents, situation_fam_id, gsm_urgence, degre_parente_id
                ) VALUES (
                    'MAT001', 'BENALI', 'Ahmed', 'بن علي', 'أحمد', '1990-05-15', 'Casablanca',
                    1, 3, 1, 'BE123456', '2015-01-01', '2025-01-01',
                    '0612345678', 1.75, 'Rabat', 1, 1, 5, 'Chef de section', '2020-01-01',
                    '12345678', 'SOM001', '2015-06-01', 'BENALI', 'Mohamed', 'BENALI', 'Fatima',
                    'Casablanca, Maroc', 1, '0612345679', 1
                )
            """)
            
            # Ajouter quelques langues pour ce personnel
            cursor.execute("""
                INSERT INTO personnel_langue (matricule, langue_id) VALUES 
                ('MAT001', 1),
                ('MAT001', 2)
            """)
            
            connection.commit()
            print("✅ Données d'exemple créées")
        else:
            print("✅ Données déjà existantes")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 Création des tables RH avec MySQL")
    print("=" * 50)
    
    steps = [
        ("Création des tables de données", create_rh_tables),
        ("Création de données d'exemple", create_sample_data)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Échec de l'étape: {step_name}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 Base de données RH configurée avec succès!")
    print("\n📋 Application prête:")
    print("1. Lancer: python app.py")
    print("2. Accéder à: http://localhost:5000/rh/")
    print("3. Cliquer sur 'Gestion RH' dans le menu principal")
    
    return True

if __name__ == '__main__':
    main()
