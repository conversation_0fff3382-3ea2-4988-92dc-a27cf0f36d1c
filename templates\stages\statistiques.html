{% extends "stages/base_stages.html" %}

{% block title %}Statistiques - Gestion des Stages{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #4B5320;
        --accent-color: #A89976;
        --camo-dark: #2F3A17;
        --camo-mid: #6B8E23;
        --camo-tan: #A89976;
        --camo-brown: #8B4513;
    }
    .dashboard-title {
        font-size: 2.2rem;
        font-weight: 900;
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 2.2rem;
        text-transform: uppercase;
        font-family: 'Black Ops One', 'Poppins', sans-serif;
        text-shadow: 2px 2px 0 var(--camo-dark);
    }
    .dashboard-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        justify-content: center;
        margin-bottom: 2.5rem;
        padding: 0 1rem;
    }
    .dashboard-card {
        background: var(--camo-dark);
        border: 2px solid var(--accent-color);
        border-radius: 12px;
        padding: 1.5rem;
        color: #fff;
        min-width: 250px;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
    }
    .dashboard-card .icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--accent-color);
    }
    .dashboard-card .label {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #fff;
        text-transform: uppercase;
    }
    .dashboard-card .value {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--camo-tan);
    }
    .dashboard-graphs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        padding: 1rem;
        margin-bottom: 2rem;
    }
    .dashboard-graph {
        background: #fff;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        min-height: 300px;
    }
    .dashboard-graph-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        text-align: center;
        text-transform: uppercase;
    }
    @media (max-width: 768px) {
        .dashboard-cards {
            flex-direction: column;
        }
        .dashboard-card {
            width: 100%;
        }
        .dashboard-graphs {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block stages_content %}
<div class="container-fluid p-4">
    <h1 class="dashboard-title">Statistiques & Dashboard</h1>

    <!-- Compteurs -->
    <div class="dashboard-cards">
        <div class="dashboard-card">
            <div class="icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="label">Stagiaires actifs</div>
            <div class="value">10</div>
        </div>
        <div class="dashboard-card">
            <div class="icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="label">Stages en cours</div>
            <div class="value">7</div>
        </div>
        <div class="dashboard-card">
            <div class="icon">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="label">Promotions actives</div>
            <div class="value">4</div>
        </div>
        <div class="dashboard-card">
            <div class="icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="label">Alertes</div>
            <div class="value">3</div>
        </div>
    </div>

    <!-- Graphiques -->
    <div class="dashboard-graphs">
        <div class="dashboard-graph">
            <div class="dashboard-graph-title">Répartition des stages par type</div>
            <canvas id="stagesTypeChart"></canvas>
        </div>
        <div class="dashboard-graph">
            <div class="dashboard-graph-title">Évolution mensuelle des stagiaires</div>
            <canvas id="evolutionChart"></canvas>
        </div>
        <div class="dashboard-graph">
            <div class="dashboard-graph-title">Répartition par promotion</div>
            <canvas id="promotionChart"></canvas>
        </div>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Configuration des couleurs militaires
    const colors = {
        primary: '#4B5320',
        secondary: '#6B8E23',
        accent: '#A89976',
        dark: '#2F3A17',
        brown: '#8B4513',
        olive: '#556B2F',
        sage: '#8FBC8F'
    };

    // Graphique des types de stages
    new Chart(document.getElementById('stagesTypeChart'), {
        type: 'doughnut',
        data: {
            labels: ['BCM', 'BE', 'BS', 'APPLICATION', 'PERFECTIONNEMENT', 'CDC', 'COORDONNATEUR TACTIQUE'],
            datasets: [{
                data: [3, 2, 2, 4, 3, 2, 1],
                backgroundColor: [
                    colors.primary,
                    colors.secondary,
                    colors.accent,
                    colors.dark,
                    colors.brown,
                    colors.olive,
                    colors.sage
                ],
                borderColor: colors.dark,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });

    // Graphique d'évolution
    new Chart(document.getElementById('evolutionChart'), {
        type: 'line',
        data: {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'],
            datasets: [{
                label: 'Nombre de stagiaires',
                data: [4, 6, 8, 10, 9, 10],
                borderColor: colors.primary,
                backgroundColor: `${colors.secondary}33`,
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#ddd'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Graphique par promotion
    new Chart(document.getElementById('promotionChart'), {
        type: 'bar',
        data: {
            labels: ['BCM', 'BE', 'BS', 'CDC'],
            datasets: [{
                label: 'Stagiaires',
                data: [3, 4, 2, 1],
                backgroundColor: [colors.primary, colors.secondary, colors.accent, colors.dark],
                borderColor: colors.dark,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    grid: {
                        color: '#ddd'
                    }
                },
                y: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
});
</script>
{% endblock %} 