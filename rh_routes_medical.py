#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes de gestion médicale pour le module RH
Implémentation des routes pour la gestion médicale et vaccinations
"""

from flask import request, redirect, url_for, flash, jsonify
from datetime import datetime, timedelta
from rh_models import *
from rh_blueprint import rh_bp

# ============================================================================
# ROUTES DE GESTION MÉDICALE
# ============================================================================

@rh_bp.route('/api/ajouter_situation_medicale', methods=['POST'])
def api_ajouter_situation_medicale():
    """API pour ajouter une situation médicale"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Vérifier si une situation médicale existe déjà
        if personnel.situation_medicale:
            return jsonify({
                'success': False,
                'error': 'Une situation médicale existe déjà pour ce personnel'
            }), 400
        
        # Créer la situation médicale
        situation = SituationMedicale(
            matricule=data['matricule'],
            maladies=data['maladies'],
            date_hospitalisation=datetime.strptime(data['date_hospitalisation'], '%Y-%m-%d').date(),
            lieu_hospitalisation=data['lieu_hospitalisation'],
            aptitude=data['aptitude'],
            observations=data.get('observations')
        )
        
        db.session.add(situation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Situation médicale ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/modifier_situation_medicale/<matricule>', methods=['PUT'])
def api_modifier_situation_medicale(matricule):
    """API pour modifier une situation médicale"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        if not personnel.situation_medicale:
            return jsonify({
                'success': False,
                'error': 'Aucune situation médicale trouvée pour ce personnel'
            }), 404
        
        data = request.get_json()
        situation = personnel.situation_medicale
        
        # Mise à jour des champs
        if 'maladies' in data:
            situation.maladies = data['maladies']
        if 'date_hospitalisation' in data:
            situation.date_hospitalisation = datetime.strptime(data['date_hospitalisation'], '%Y-%m-%d').date()
        if 'lieu_hospitalisation' in data:
            situation.lieu_hospitalisation = data['lieu_hospitalisation']
        if 'aptitude' in data:
            situation.aptitude = data['aptitude']
        if 'observations' in data:
            situation.observations = data['observations']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Situation médicale modifiée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_vaccination', methods=['POST'])
def api_ajouter_vaccination():
    """API pour ajouter une vaccination"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la vaccination
        vaccination = Vaccination(
            matricule=data['matricule'],
            date_vaccination=datetime.strptime(data['date_vaccination'], '%Y-%m-%d').date(),
            objet=data['objet'],
            observation=data.get('observation')
        )
        
        db.session.add(vaccination)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Vaccination ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/modifier_vaccination/<int:id_vaccination>', methods=['PUT'])
def api_modifier_vaccination(id_vaccination):
    """API pour modifier une vaccination"""
    try:
        vaccination = Vaccination.query.get_or_404(id_vaccination)
        data = request.get_json()
        
        # Mise à jour des champs
        if 'date_vaccination' in data:
            vaccination.date_vaccination = datetime.strptime(data['date_vaccination'], '%Y-%m-%d').date()
        if 'objet' in data:
            vaccination.objet = data['objet']
        if 'observation' in data:
            vaccination.observation = data['observation']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Vaccination modifiée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_vaccination/<int:id_vaccination>', methods=['DELETE'])
def api_supprimer_vaccination(id_vaccination):
    """API pour supprimer une vaccination"""
    try:
        vaccination = Vaccination.query.get_or_404(id_vaccination)
        
        db.session.delete(vaccination)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Vaccination supprimée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/medical', methods=['GET'])
def api_get_medical_complet(matricule):
    """API pour récupérer toutes les informations médicales d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # Situation médicale
        situation_data = None
        if personnel.situation_medicale:
            s = personnel.situation_medicale
            situation_data = {
                'id_sitmed': s.id_sitmed,
                'maladies': s.maladies,
                'date_hospitalisation': s.date_hospitalisation.strftime('%Y-%m-%d'),
                'lieu_hospitalisation': s.lieu_hospitalisation,
                'aptitude': s.aptitude,
                'observations': s.observations
            }
        
        # Vaccinations
        vaccinations_data = []
        for v in personnel.vaccinations:
            vaccinations_data.append({
                'id_vaccination': v.id_vaccination,
                'date_vaccination': v.date_vaccination.strftime('%Y-%m-%d'),
                'objet': v.objet,
                'observation': v.observation,
                'anciennete_jours': (datetime.now().date() - v.date_vaccination).days
            })
        
        # Trier les vaccinations par date (plus récente en premier)
        vaccinations_data.sort(key=lambda x: x['date_vaccination'], reverse=True)
        
        # Statistiques médicales
        nb_vaccinations = len(vaccinations_data)
        derniere_vaccination = vaccinations_data[0] if vaccinations_data else None
        
        # Vaccinations récentes (moins de 1 an)
        date_limite = datetime.now().date() - timedelta(days=365)
        vaccinations_recentes = [v for v in personnel.vaccinations if v.date_vaccination >= date_limite]
        
        return jsonify({
            'success': True,
            'medical': {
                'situation_medicale': situation_data,
                'vaccinations': vaccinations_data,
                'statistiques': {
                    'a_situation_medicale': situation_data is not None,
                    'aptitude': situation_data['aptitude'] if situation_data else None,
                    'nb_vaccinations': nb_vaccinations,
                    'nb_vaccinations_recentes': len(vaccinations_recentes),
                    'derniere_vaccination': derniere_vaccination,
                    'groupe_sanguin': personnel.groupe_sanguin.libelle
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel_inapte', methods=['GET'])
def api_personnel_inapte():
    """API pour récupérer le personnel inapte"""
    try:
        personnel_inapte = Personnel.query.join(SituationMedicale).filter(
            SituationMedicale.aptitude == 'inapte'
        ).all()
        
        personnel_data = []
        for p in personnel_inapte:
            personnel_data.append({
                'matricule': p.matricule,
                'nom_complet': p.nom_complet,
                'nom_complet_arabe': p.nom_complet_arabe,
                'unite': p.unite.libelle,
                'grade': p.grade_actuel.libelle,
                'fonction': p.fonction,
                'situation_medicale': {
                    'maladies': p.situation_medicale.maladies,
                    'date_hospitalisation': p.situation_medicale.date_hospitalisation.strftime('%d/%m/%Y'),
                    'lieu_hospitalisation': p.situation_medicale.lieu_hospitalisation,
                    'observations': p.situation_medicale.observations
                }
            })
        
        return jsonify({
            'success': True,
            'personnel_inapte': personnel_data,
            'total': len(personnel_data)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/vaccinations_par_type', methods=['GET'])
def api_vaccinations_par_type():
    """API pour récupérer les statistiques de vaccinations par type"""
    try:
        # Statistiques par type de vaccination
        stats_vaccinations = db.session.query(
            Vaccination.objet,
            db.func.count(Vaccination.id_vaccination).label('effectif')
        ).group_by(Vaccination.objet).order_by(
            db.func.count(Vaccination.id_vaccination).desc()
        ).all()
        
        # Vaccinations récentes (moins de 1 an)
        date_limite = datetime.now().date() - timedelta(days=365)
        vaccinations_recentes = db.session.query(
            Vaccination.objet,
            db.func.count(Vaccination.id_vaccination).label('effectif')
        ).filter(Vaccination.date_vaccination >= date_limite).group_by(
            Vaccination.objet
        ).order_by(db.func.count(Vaccination.id_vaccination).desc()).all()
        
        # Personnel non vacciné (sans aucune vaccination)
        personnel_avec_vaccination = db.session.query(Vaccination.matricule).distinct().subquery()
        personnel_sans_vaccination = Personnel.query.filter(
            ~Personnel.matricule.in_(personnel_avec_vaccination)
        ).count()
        
        return jsonify({
            'success': True,
            'statistiques': {
                'par_type': [{'type': s[0], 'effectif': s[1]} for s in stats_vaccinations],
                'recentes_par_type': [{'type': s[0], 'effectif': s[1]} for s in vaccinations_recentes],
                'personnel_sans_vaccination': personnel_sans_vaccination,
                'total_vaccinations': sum([s[1] for s in stats_vaccinations])
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/rappels_vaccination', methods=['GET'])
def api_rappels_vaccination():
    """API pour récupérer les rappels de vaccination nécessaires"""
    try:
        # Définir les types de vaccinations qui nécessitent des rappels
        rappels_config = {
            'Tétanos': 365 * 10,  # Tous les 10 ans
            'Hépatite B': 365 * 5,  # Tous les 5 ans
            'Grippe': 365,  # Tous les ans
            'COVID-19': 365,  # Tous les ans
            'Fièvre jaune': 365 * 10  # Tous les 10 ans
        }
        
        rappels_necessaires = []
        
        for type_vaccin, duree_jours in rappels_config.items():
            # Date limite pour ce type de vaccination
            date_limite = datetime.now().date() - timedelta(days=duree_jours)
            
            # Personnel ayant cette vaccination mais ancienne
            personnel_rappel = db.session.query(Personnel).join(Vaccination).filter(
                Vaccination.objet == type_vaccin,
                Vaccination.date_vaccination < date_limite
            ).all()
            
            # Personnel n'ayant jamais eu cette vaccination
            personnel_avec_vaccin = db.session.query(Personnel.matricule).join(Vaccination).filter(
                Vaccination.objet == type_vaccin
            ).distinct().subquery()
            
            personnel_sans_vaccin = Personnel.query.filter(
                ~Personnel.matricule.in_(personnel_avec_vaccin)
            ).all()
            
            # Combiner les deux listes
            for p in personnel_rappel + personnel_sans_vaccin:
                # Vérifier si déjà dans la liste
                if not any(r['matricule'] == p.matricule and r['type_vaccin'] == type_vaccin for r in rappels_necessaires):
                    # Trouver la dernière vaccination de ce type
                    derniere_vaccination = Vaccination.query.filter_by(
                        matricule=p.matricule,
                        objet=type_vaccin
                    ).order_by(Vaccination.date_vaccination.desc()).first()
                    
                    rappels_necessaires.append({
                        'matricule': p.matricule,
                        'nom_complet': p.nom_complet,
                        'unite': p.unite.libelle,
                        'grade': p.grade_actuel.libelle,
                        'type_vaccin': type_vaccin,
                        'derniere_vaccination': derniere_vaccination.date_vaccination.strftime('%d/%m/%Y') if derniere_vaccination else 'Jamais',
                        'jours_depuis_derniere': (datetime.now().date() - derniere_vaccination.date_vaccination).days if derniere_vaccination else None,
                        'priorite': 'Haute' if not derniere_vaccination else 'Moyenne'
                    })
        
        # Trier par priorité et par ancienneté
        rappels_necessaires.sort(key=lambda x: (x['priorite'] == 'Moyenne', x['jours_depuis_derniere'] or 99999), reverse=True)
        
        return jsonify({
            'success': True,
            'rappels': rappels_necessaires,
            'total': len(rappels_necessaires),
            'par_priorite': {
                'haute': len([r for r in rappels_necessaires if r['priorite'] == 'Haute']),
                'moyenne': len([r for r in rappels_necessaires if r['priorite'] == 'Moyenne'])
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/statistiques_medicales_globales', methods=['GET'])
def api_statistiques_medicales_globales():
    """API pour récupérer les statistiques médicales globales"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        personnel_avec_situation = Personnel.query.join(SituationMedicale).count()
        personnel_apte = Personnel.query.join(SituationMedicale).filter(
            SituationMedicale.aptitude == 'apte'
        ).count()
        personnel_inapte = Personnel.query.join(SituationMedicale).filter(
            SituationMedicale.aptitude == 'inapte'
        ).count()
        
        # Statistiques par groupe sanguin
        stats_groupe_sanguin = db.session.query(
            ReferentielGroupeSanguin.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielGroupeSanguin.libelle).all()
        
        # Vaccinations totales
        total_vaccinations = Vaccination.query.count()
        personnel_vaccines = db.session.query(Vaccination.matricule).distinct().count()
        
        # Vaccinations récentes (moins de 1 an)
        date_limite = datetime.now().date() - timedelta(days=365)
        vaccinations_recentes = Vaccination.query.filter(
            Vaccination.date_vaccination >= date_limite
        ).count()
        
        return jsonify({
            'success': True,
            'statistiques': {
                'personnel': {
                    'total': total_personnel,
                    'avec_situation_medicale': personnel_avec_situation,
                    'sans_situation_medicale': total_personnel - personnel_avec_situation,
                    'apte': personnel_apte,
                    'inapte': personnel_inapte,
                    'pourcentage_apte': round((personnel_apte / personnel_avec_situation) * 100, 2) if personnel_avec_situation > 0 else 0
                },
                'groupes_sanguins': [{'groupe': s[0], 'effectif': s[1]} for s in stats_groupe_sanguin],
                'vaccinations': {
                    'total': total_vaccinations,
                    'personnel_vaccines': personnel_vaccines,
                    'personnel_non_vaccines': total_personnel - personnel_vaccines,
                    'vaccinations_recentes': vaccinations_recentes,
                    'moyenne_par_personnel': round(total_vaccinations / total_personnel, 2) if total_personnel > 0 else 0
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
