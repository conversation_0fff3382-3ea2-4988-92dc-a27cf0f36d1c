{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Gestion des Documents</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <a href="{{ url_for('stages.export_excel') }}" class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> Exporter Excel
        </a>
        <a href="{{ url_for('stages.export_pdf') }}" class="btn btn-danger me-2">
            <i class="fas fa-file-pdf"></i> Exporter PDF
        </a>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#generateDocModal">
            <i class="fas fa-file-alt"></i> Générer un document
        </button>
    </div>
</div>

<div class="row">
    <!-- Types de documents -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Types de documents</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action active" data-type="convention">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-file-contract me-2"></i>
                                Conventions de stage
                            </div>
                            <span class="badge bg-primary rounded-pill">12</span>
                        </div>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-type="attestation">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-certificate me-2"></i>
                                Attestations de stage
                            </div>
                            <span class="badge bg-primary rounded-pill">8</span>
                        </div>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-type="evaluation">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-star me-2"></i>
                                Fiches d'évaluation
                            </div>
                            <span class="badge bg-primary rounded-pill">15</span>
                        </div>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" data-type="rapport">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-book me-2"></i>
                                Rapports de stage
                            </div>
                            <span class="badge bg-primary rounded-pill">10</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des documents -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Documents</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Rechercher un document...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Stagiaire</th>
                                <th>Date</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                    Convention de stage
                                </td>
                                <td>BOUAZZAOUI Mehdi</td>
                                <td>15/03/2024</td>
                                <td><span class="badge bg-success">Signé</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info" title="Envoyer par email">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <i class="fas fa-file-word text-primary me-2"></i>
                                    Attestation de stage
                                </td>
                                <td>CHRAIBI Nabil</td>
                                <td>14/03/2024</td>
                                <td><span class="badge bg-warning">En attente</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Télécharger">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info" title="Envoyer par email">
                                        <i class="fas fa-envelope"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Génération Document -->
<div class="modal fade" id="generateDocModal" tabindex="-1" aria-labelledby="generateDocModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="generateDocModalLabel">Générer un document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="generateDocForm">
                    <div class="mb-3">
                        <label for="docType" class="form-label">Type de document</label>
                        <select class="form-select" id="docType" required>
                            <option value="">Sélectionner un type</option>
                            <option value="convention">Convention de stage</option>
                            <option value="attestation">Attestation de stage</option>
                            <option value="evaluation">Fiche d'évaluation</option>
                            <option value="rapport">Rapport de stage</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="stagiaire" class="form-label">Stagiaire</label>
                        <select class="form-select" id="stagiaire" required>
                            <option value="">Sélectionner un stagiaire</option>
                            <option value="1">ZIANI Amine</option>
                            <option value="2">BENALI Youssef</option>
                            <option value="3">TAZI Ahmed</option>
                            <option value="4">IDRISSI Omar</option>
                            <option value="5">ALAOUI Karim</option>
                            <option value="6">EL AMRANI Mohammed</option>
                            <option value="7">BENJELLOUN Hamza</option>
                            <option value="8">BOUAZZAOUI Mehdi</option>
                            <option value="9">CHRAIBI Nabil</option>
                            <option value="10">FASSI Rachid</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="format" class="form-label">Format</label>
                        <select class="form-select" id="format" required>
                            <option value="pdf">PDF</option>
                            <option value="word">Word</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sendEmail">
                            <label class="form-check-label" for="sendEmail">
                                Envoyer par email au stagiaire
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Générer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion des filtres de type de document
    const typeLinks = document.querySelectorAll('.list-group-item-action');
    typeLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            // Retirer la classe active de tous les liens
            typeLinks.forEach(l => l.classList.remove('active'));
            // Ajouter la classe active au lien cliqué
            this.classList.add('active');
            // Filtrer les documents (à implémenter)
            console.log('Type sélectionné:', this.dataset.type);
        });
    });

    // Gestion de la recherche
    const searchInput = document.querySelector('input[type="text"]');
    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });

    // Gestion du formulaire de génération de document
    const generateForm = document.getElementById('generateDocForm');
    const generateButton = generateForm.nextElementSibling.querySelector('.btn-primary');

    generateButton.addEventListener('click', function() {
        // Récupération des valeurs du formulaire
        const formData = {
            docType: document.getElementById('docType').value,
            stagiaire: document.getElementById('stagiaire').value,
            format: document.getElementById('format').value,
            sendEmail: document.getElementById('sendEmail').checked
        };

        // Validation du formulaire
        if (!formData.docType || !formData.stagiaire || !formData.format) {
            alert('Veuillez remplir tous les champs obligatoires');
            return;
        }

        // Simulation de génération
        console.log('Génération du document:', formData);
        
        // Fermeture de la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('generateDocModal'));
        modal.hide();
    });
});
</script>
{% endblock %} 