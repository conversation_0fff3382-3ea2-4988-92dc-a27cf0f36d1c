#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier que toutes les 25 tables RH ont été créées
"""

import mysql.connector
from mysql.connector import Error

def verify_tables():
    """Vérifie que toutes les tables RH existent"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    # Liste des 25 tables RH attendues
    expected_tables = [
        # Tables de référence (11)
        'referentiel_genre',
        'referentiel_groupe_sanguin', 
        'referentiel_categorie',
        'referentiel_service',
        'referentiel_specialite',
        'referentiel_unite',
        'referentiel_grade',
        'referentiel_etat_matrimonial',
        'referentiel_langue',
        'referentiel_lien_parente',
        'referentiel_type_absence',
        
        # Table principale (1)
        'personnel',
        
        # Tables associatives et historique (2)
        'personnel_langue',
        'historique_grade',
        
        # Tables familiales (2)
        'conjoint',
        'enfant',
        
        # Tables médicales (3)
        'situation_medicale',
        'vaccination',
        'ptc',
        
        # Tables d'absences (3)
        'absence_desertion',
        'absence_detachement',
        'absence_permission',
        
        # Tables de mouvements (3)
        'mouvement_interbie',
        'sejour_ops',
        'liberation'
    ]
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("Connexion à la base de données réussie")
        print(f"Vérification de {len(expected_tables)} tables RH...")
        print("-" * 60)
        
        # Obtenir la liste des tables existantes
        cursor.execute("SHOW TABLES;")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        # Vérifier chaque table attendue
        missing_tables = []
        existing_rh_tables = []
        
        for i, table_name in enumerate(expected_tables, 1):
            if table_name in existing_tables:
                print(f"✓ {i:2d}/25 - {table_name}")
                existing_rh_tables.append(table_name)
            else:
                print(f"✗ {i:2d}/25 - {table_name} (MANQUANTE)")
                missing_tables.append(table_name)
        
        print("-" * 60)
        print(f"📊 RÉSULTATS:")
        print(f"   • Tables trouvées: {len(existing_rh_tables)}/25")
        print(f"   • Tables manquantes: {len(missing_tables)}")
        
        if missing_tables:
            print(f"\n❌ Tables manquantes:")
            for table in missing_tables:
                print(f"   - {table}")
            return False
        else:
            print(f"\n🎉 Toutes les 25 tables RH sont présentes !")
            
            # Vérifier quelques structures de tables importantes
            print(f"\n🔍 Vérification des structures clés...")
            
            # Vérifier la table personnel
            cursor.execute("DESCRIBE personnel;")
            personnel_columns = cursor.fetchall()
            print(f"   • Table personnel: {len(personnel_columns)} colonnes")
            
            # Vérifier les contraintes de clés étrangères
            cursor.execute("""
                SELECT COUNT(*) as fk_count 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'gestion_vehicules' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                AND TABLE_NAME IN ('personnel', 'conjoint', 'enfant', 'situation_medicale', 'vaccination', 'ptc', 'absence_desertion', 'absence_detachement', 'absence_permission', 'mouvement_interbie', 'sejour_ops', 'liberation', 'personnel_langue', 'historique_grade', 'referentiel_specialite');
            """)
            fk_count = cursor.fetchone()[0]
            print(f"   • Contraintes de clés étrangères: {fk_count}")
            
            return True
        
    except Error as e:
        print(f"Erreur : {e}")
        return False
        
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion fermée")

if __name__ == "__main__":
    print("=== VÉRIFICATION DES TABLES RH ===")
    print("Contrôle de l'existence des 25 tables")
    print("=" * 60)
    
    success = verify_tables()
    
    if success:
        print("\n✅ ÉTAPE 3 TERMINÉE AVEC SUCCÈS !")
        print("Les 25 tables RH sont créées et prêtes.")
        print("\n📋 PROCHAINE ÉTAPE:")
        print("   Étape 4: Adapter le backend et frontend")
        print("   Référence: checkpoint 70")
    else:
        print("\n❌ PROBLÈMES DÉTECTÉS")
        print("Certaines tables sont manquantes.")
