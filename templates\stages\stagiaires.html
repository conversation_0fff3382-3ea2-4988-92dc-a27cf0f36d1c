{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Gestion des Stagiaires</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStagiaireModal">
            <i class="fas fa-plus"></i> Nouveau Stagiaire
        </button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Liste des Stagiaires</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Rechercher un stagiaire...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Photo</th>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Email</th>
                                <th>Date de naissance</th>
                                <th>Promotion</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stagiaire in stagiaires %}
                            <tr>
                                <td>
                                    <img src="{{ stagiaire.photo or url_for('static', filename='images/default-avatar.png') }}"
                                        alt="Photo" class="rounded-circle" width="32" height="32">
                                </td>
                                <td>{{ stagiaire.nom }}</td>
                                <td>{{ stagiaire.prenom }}</td>
                                <td>{{ stagiaire.email }}</td>
                                <td>{{ stagiaire.date_naissance.strftime('%d/%m/%Y') }}</td>
                                <td><span class="badge bg-primary">Promotion 2024</span></td>
                                <td><span class="badge bg-success">Actif</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajout Stagiaire -->
<div class="modal fade" id="addStagiaireModal" tabindex="-1" aria-labelledby="addStagiaireModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStagiaireModalLabel">Nouveau Stagiaire</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addStagiaireForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" required>
                        </div>
                        <div class="col-md-6">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" required>
                        </div>
                        <div class="col-md-6">
                            <label for="dateNaissance" class="form-label">Date de naissance</label>
                            <input type="date" class="form-control" id="dateNaissance" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="promotion" class="form-label">Promotion</label>
                            <select class="form-select" id="promotion" required>
                                <option value="">Sélectionner une promotion</option>
                                <option value="2024">Promotion 2024</option>
                                <option value="2025">Promotion 2025</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="typeStage" class="form-label">Type de stage</label>
                            <select class="form-select" id="typeStage" required>
                                <option value="">Sélectionner un type</option>
                                <option value="observation">Stage d'observation</option>
                                <option value="technique">Stage technique</option>
                                <option value="fin_etudes">Stage de fin d'études</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="photo" class="form-label">Photo</label>
                        <input type="file" class="form-control" id="photo" accept="image/*">
                    </div>

                    <div class="mb-3">
                        <label for="commentaires" class="form-label">Commentaires</label>
                        <textarea class="form-control" id="commentaires" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion de la recherche
    const searchInput = document.querySelector('input[type="text"]');
    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });
});
</script>
{% endblock %} 