#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ajout de situations réalistes variées pour les 200 militaires
Données familiales, médicales, absences, mouvements
"""

import random
from datetime import date, datetime, timedelta
from rh_models import *
from app import app

# Données réalistes
NOMS_CONJOINTS_F = [
    ("ALAMI", "FATIMA", "العلمي", "فاطمة"),
    ("BENALI", "AICHA", "بن علي", "عائشة"),
    ("CHERKAOUI", "KHADIJA", "الشرقاوي", "خديجة"),
    ("DOUIRI", "ZINEB", "الدويري", "زينب"),
    ("FASSI", "MALIKA", "الفاسي", "مليكة"),
    ("GHAZI", "SAMIRA", "الغازي", "سميرة"),
    ("HAJJI", "NAIMA", "الحاجي", "نعيمة"),
    ("IDRISSI", "RACHIDA", "الإدريسي", "رشيدة"),
    ("JAMAL", "LATIFA", "جمال", "لطيفة"),
    ("KABBAJ", "AMINA", "القباج", "أمينة")
]

PRENOMS_ENFANTS = [
    ("ADAM", "آدم"), ("YOUSSEF", "يوسف"), ("MOHAMMED", "محمد"), ("OMAR", "عمر"),
    ("HASSAN", "حسن"), ("AMINE", "أمين"), ("ZAKARIA", "زكرياء"), ("MEHDI", "مهدي"),
    ("FATIMA", "فاطمة"), ("AICHA", "عائشة"), ("KHADIJA", "خديجة"), ("ZINEB", "زينب"),
    ("MALIKA", "مليكة"), ("SAMIRA", "سميرة"), ("NAIMA", "نعيمة"), ("RACHIDA", "رشيدة")
]

VILLES_MAROCAINES = [
    "Rabat", "Casablanca", "Fès", "Marrakech", "Agadir", "Tanger", 
    "Meknès", "Oujda", "Kenitra", "Tétouan", "Safi", "Mohammedia",
    "Khouribga", "Beni Mellal", "El Jadida", "Nador", "Settat",
    "Larache", "Ksar El Kebir", "Sale", "Berrechid", "Khemisset"
]

PROFESSIONS_CONJOINTS = [
    "Enseignante", "Infirmière", "Employée de bureau", "Commerçante", 
    "Fonctionnaire", "Secrétaire", "Comptable", "Pharmacienne",
    "Sage-femme", "Institutrice", "Sans profession", "Femme au foyer"
]

MALADIES_COURANTES = [
    "Hypertension artérielle", "Diabète type 2", "Asthme", "Allergie alimentaire",
    "Migraine chronique", "Lombalgie", "Arthrose", "Gastrite",
    "Sinusite chronique", "Eczéma", "Aucune maladie particulière"
]

VACCINS_MILITAIRES = [
    "COVID-19", "Hépatite B", "Tétanos", "Grippe saisonnière", 
    "Méningite", "Fièvre jaune", "Typhoïde", "Rage"
]

MOTIFS_PERMISSIONS = [
    "Congé annuel", "Mariage", "Décès dans la famille", "Naissance",
    "Maladie personnelle", "Maladie familiale", "Pèlerinage", "Études",
    "Convenance personnelle", "Urgence familiale"
]

LIEUX_MISSIONS = [
    "Laâyoune", "Dakhla", "Guelmim", "Ouarzazate", "Errachidia",
    "Frontière algérienne", "Frontière mauritanienne", "Sahara occidental",
    "Zone nord", "Zone centre", "Zone sud"
]

def random_date_between(start_date, end_date):
    """Génère une date aléatoire entre deux dates"""
    time_between = end_date - start_date
    days_between = time_between.days
    if days_between <= 0:
        return start_date
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def add_family_situations():
    """Ajoute des situations familiales réalistes"""
    print("\n👨‍👩‍👧‍👦 Ajout des situations familiales...")
    
    with app.app_context():
        try:
            personnels = Personnel.query.all()
            degres_parente = ReferentielDegreParente.query.all()
            
            conjoints_count = 0
            enfants_count = 0
            
            # Conjoints pour 40% des militaires
            militaires_maries = random.sample(personnels, int(len(personnels) * 0.4))
            
            for personnel in militaires_maries:
                nom_c, prenom_c, nom_arabe_c, prenom_arabe_c = random.choice(NOMS_CONJOINTS_F)
                
                conjoint = Conjoint(
                    matricule=personnel.matricule,
                    nom=nom_c,
                    prenom=prenom_c,
                    nom_arabe=nom_arabe_c,
                    prenom_arabe=prenom_arabe_c,
                    date_naissance=random_date_between(date(1985, 1, 1), date(2000, 12, 31)),
                    lieu_naissance=random.choice(VILLES_MAROCAINES),
                    numero_cin=f"{random.choice(['A','B','C','D','E','F'])}{random.randint(100000, 999999)}",
                    date_mariage=random_date_between(date(2010, 1, 1), date(2024, 12, 31)),
                    lieu_mariage=random.choice(VILLES_MAROCAINES),
                    profession=random.choice(PROFESSIONS_CONJOINTS),
                    profession_arabe=random.choice(PROFESSIONS_CONJOINTS),
                    gsm=f"06{random.randint(10000000, 99999999)}",
                    adresse=personnel.lieu_residence,
                    adresse_arabe=personnel.lieu_residence,
                    nom_pere=random.choice([n[1] for n in NOMS_CONJOINTS_F]),
                    prenom_pere=random.choice([n[0] for n in NOMS_CONJOINTS_F]),
                    nom_arabe_pere=random.choice([n[3] for n in NOMS_CONJOINTS_F]),
                    prenom_arabe_pere=random.choice([n[2] for n in NOMS_CONJOINTS_F]),
                    nom_mere=random.choice([n[1] for n in NOMS_CONJOINTS_F]),
                    prenom_mere=random.choice([n[0] for n in NOMS_CONJOINTS_F]),
                    nom_arabe_mere=random.choice([n[3] for n in NOMS_CONJOINTS_F]),
                    prenom_arabe_mere=random.choice([n[2] for n in NOMS_CONJOINTS_F]),
                    profession_pere=random.choice(["Commerçant", "Fonctionnaire", "Retraité", "Agriculteur"]),
                    profession_mere=random.choice(["Femme au foyer", "Enseignante", "Retraitée", "Commerçante"])
                )
                db.session.add(conjoint)
                conjoints_count += 1
            
            # Enfants pour 50% des militaires (mariés et célibataires)
            militaires_avec_enfants = random.sample(personnels, int(len(personnels) * 0.5))
            
            for personnel in militaires_avec_enfants:
                nb_enfants = random.randint(1, 4)
                for i in range(nb_enfants):
                    prenom_enfant, prenom_arabe_enfant = random.choice(PRENOMS_ENFANTS)
                    
                    enfant = Enfant(
                        matricule=personnel.matricule,
                        nom=personnel.nom,
                        prenom=prenom_enfant,
                        nom_arabe=personnel.nom_arabe,
                        prenom_arabe=prenom_arabe_enfant,
                        date_naissance=random_date_between(date(2005, 1, 1), date(2024, 12, 31)),
                        lieu_naissance=random.choice(VILLES_MAROCAINES),
                        sexe_id=random.choice([1, 2]),  # Supposons 1=M, 2=F
                        date_deces=None if random.random() > 0.02 else random_date_between(date(2020, 1, 1), date(2024, 12, 31))
                    )
                    db.session.add(enfant)
                    enfants_count += 1
            
            db.session.commit()
            print(f"✅ Situations familiales ajoutées: {conjoints_count} conjoints, {enfants_count} enfants")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur situations familiales: {e}")

def add_medical_situations():
    """Ajoute des situations médicales réalistes"""
    print("\n🏥 Ajout des situations médicales...")
    
    with app.app_context():
        try:
            personnels = Personnel.query.all()
            situations_count = 0
            vaccinations_count = 0
            
            # Situations médicales pour 70% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.7)):
                situation = SituationMedicale(
                    matricule=personnel.matricule,
                    maladies=random.choice(MALADIES_COURANTES),
                    date_hospitalisation=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                    lieu_hospitalisation=f"Hôpital Militaire {random.choice(VILLES_MAROCAINES)}",
                    aptitude=random.choice(["apte", "apte", "apte", "apte", "inapte"]),  # 80% aptes
                    observations=f"Visite médicale du {random_date_between(date(2023, 1, 1), date(2024, 12, 31))}"
                )
                db.session.add(situation)
                situations_count += 1
            
            # Vaccinations pour 90% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.9)):
                nb_vaccins = random.randint(2, 5)
                vaccins_choisis = random.sample(VACCINS_MILITAIRES, nb_vaccins)
                
                for vaccin in vaccins_choisis:
                    vaccination = Vaccination(
                        matricule=personnel.matricule,
                        date_vaccination=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                        objet=vaccin,
                        observation=f"Vaccination {vaccin} - Lot {random.randint(1000, 9999)}"
                    )
                    db.session.add(vaccination)
                    vaccinations_count += 1
            
            db.session.commit()
            print(f"✅ Situations médicales ajoutées: {situations_count} situations, {vaccinations_count} vaccinations")

        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur situations médicales: {e}")

def add_absence_situations():
    """Ajoute des situations d'absences réalistes"""
    print("\n📅 Ajout des situations d'absences...")

    with app.app_context():
        try:
            personnels = Personnel.query.all()
            permissions_count = 0
            ptc_count = 0
            detachements_count = 0
            desertions_count = 0

            # Permissions pour 80% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.8)):
                nb_permissions = random.randint(1, 4)
                for _ in range(nb_permissions):
                    date_debut = random_date_between(date(2023, 1, 1), date(2024, 12, 31))
                    duree = random.randint(3, 30)
                    date_fin = date_debut + timedelta(days=duree)

                    permission = Permission(
                        matricule=personnel.matricule,
                        date_debut=date_debut,
                        date_fin=date_fin,
                        adresse=random.choice(VILLES_MAROCAINES),
                        numero_serie=f"PERM{random.randint(1000, 9999)}/2024"
                    )
                    db.session.add(permission)
                    permissions_count += 1

            # PTC pour 30% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.3)):
                date_debut = random_date_between(date(2023, 1, 1), date(2024, 12, 31))
                duree = random.randint(7, 90)
                date_fin = date_debut + timedelta(days=duree)

                ptc = Ptc(
                    matricule=personnel.matricule,
                    date_ptc=date_debut - timedelta(days=random.randint(1, 30)),
                    duree=duree,
                    date_debut=date_debut,
                    date_fin=date_fin,
                    objet=random.choice(["Formation spécialisée", "Stage perfectionnement", "Cours langue", "Formation technique"]),
                    observations=f"PTC autorisé par décision n°{random.randint(100, 999)}/2024"
                )
                db.session.add(ptc)
                ptc_count += 1

            # Détachements pour 20% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.2)):
                date_debut = random_date_between(date(2023, 1, 1), date(2024, 12, 31))
                duree = random.randint(30, 365)
                date_fin = date_debut + timedelta(days=duree)

                detachement = Detachement(
                    matricule=personnel.matricule,
                    date_debut=date_debut,
                    adresse_detachement=random.choice(LIEUX_MISSIONS),
                    pays="Maroc",
                    date_fin=date_fin
                )
                db.session.add(detachement)
                detachements_count += 1

            # Quelques cas de désertion (très rare - 1%)
            for personnel in random.sample(personnels, max(1, int(len(personnels) * 0.01))):
                desertion = Desertion(
                    matricule=personnel.matricule,
                    date_absence=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    date_desertion=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    date_retour=random_date_between(date(2023, 6, 1), date(2024, 12, 31)),
                    date_arret_solde=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    date_prise_solde=random_date_between(date(2023, 6, 1), date(2024, 12, 31))
                )
                db.session.add(desertion)
                desertions_count += 1

            db.session.commit()
            print(f"✅ Situations d'absences ajoutées: {permissions_count} permissions, {ptc_count} PTC, {detachements_count} détachements, {desertions_count} désertions")

        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur situations d'absences: {e}")

def add_movement_situations():
    """Ajoute des situations de mouvements réalistes"""
    print("\n🔄 Ajout des situations de mouvements...")

    with app.app_context():
        try:
            personnels = Personnel.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()

            mutations_count = 0
            avancements_count = 0
            sanctions_count = 0
            liberations_count = 0
            sejours_count = 0

            # Mutations pour 25% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.25)):
                mutation = MutationFonction(
                    matricule=personnel.matricule,
                    ancienne_fonction=personnel.fonction,
                    nouvelle_fonction=random.choice(["Chef d'équipe", "Instructeur", "Responsable matériel", "Secrétaire"]),
                    date_mutation=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                    motif=random.choice(["Besoin du service", "Promotion", "Demande personnelle", "Réorganisation"]),
                    observations=f"Mutation approuvée par décision n°{random.randint(100, 999)}/2024"
                )
                db.session.add(mutation)
                mutations_count += 1

            # Avancements pour 20% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.2)):
                avancement = Avancement(
                    matricule=personnel.matricule,
                    ancien_grade_id=personnel.grade_actuel_id,
                    nouveau_grade_id=random.choice(grades).id_grade,
                    date_avancement=random_date_between(date(2020, 1, 1), date(2024, 12, 31)),
                    motif=random.choice(["Ancienneté", "Au choix", "Mérite exceptionnel", "Formation complétée"]),
                    observations=f"Avancement selon tableau n°{random.randint(1, 50)}/2024"
                )
                db.session.add(avancement)
                avancements_count += 1

            # Sanctions pour 8% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.08)):
                sanction = Sanction(
                    matricule=personnel.matricule,
                    type_sanction=random.choice(["Blâme", "Avertissement", "Retenue de solde", "Consigne", "Arrêts simples"]),
                    date_sanction=random_date_between(date(2023, 1, 1), date(2024, 12, 31)),
                    motif=random.choice(["Retard répété", "Négligence", "Insubordination mineure", "Absence non justifiée"]),
                    duree_jours=random.randint(1, 15) if random.choice([True, False]) else None,
                    observations=f"Sanction prononcée par décision disciplinaire n°{random.randint(10, 99)}/2024"
                )
                db.session.add(sanction)
                sanctions_count += 1

            # Libérations pour 3% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.03)):
                liberation = Liberation(
                    matricule=personnel.matricule,
                    date_liberation=random_date_between(date(2024, 6, 1), date(2025, 12, 31)),
                    motif=random.choice(["Fin de contrat", "Démission", "Limite d'âge", "Convenance personnelle"]),
                    observations=f"Libération autorisée par décision n°{random.randint(100, 999)}/2024"
                )
                db.session.add(liberation)
                liberations_count += 1

            # Séjours opérationnels pour 40% des militaires
            for personnel in random.sample(personnels, int(len(personnels) * 0.4)):
                date_debut = random_date_between(date(2023, 1, 1), date(2024, 12, 31))
                duree = random.randint(30, 180)
                date_fin = date_debut + timedelta(days=duree)

                sejour = SejourOps(
                    matricule=personnel.matricule,
                    date_debut=date_debut,
                    date_fin=date_fin,
                    lieu_mission=random.choice(LIEUX_MISSIONS),
                    pays="Maroc",
                    type_mission=random.choice(["Surveillance", "Sécurisation", "Reconnaissance", "Appui logistique"]),
                    observations=f"Mission opérationnelle - Ordre n°{random.randint(100, 999)}/2024"
                )
                db.session.add(sejour)
                sejours_count += 1

            db.session.commit()
            print(f"✅ Situations de mouvements ajoutées: {mutations_count} mutations, {avancements_count} avancements, {sanctions_count} sanctions, {liberations_count} libérations, {sejours_count} séjours")

        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur situations de mouvements: {e}")

if __name__ == "__main__":
    print("🚀 Ajout de situations réalistes aux 200 militaires...")
    add_family_situations()
    add_medical_situations()
    add_absence_situations()
    add_movement_situations()

    # Statistiques finales
    with app.app_context():
        print(f"\n📊 STATISTIQUES FINALES:")
        print(f"   👥 Personnel: {Personnel.query.count()}")
        print(f"   💑 Conjoints: {Conjoint.query.count()}")
        print(f"   👶 Enfants: {Enfant.query.count()}")
        print(f"   🏥 Situations médicales: {SituationMedicale.query.count()}")
        print(f"   💉 Vaccinations: {Vaccination.query.count()}")
        print(f"   📅 Permissions: {Permission.query.count()}")
        print(f"   🎓 PTC: {Ptc.query.count()}")
        print(f"   📍 Détachements: {Detachement.query.count()}")
        print(f"   ⚠️ Désertions: {Desertion.query.count()}")
        print(f"   🔄 Mutations: {MutationFonction.query.count()}")
        print(f"   ⬆️ Avancements: {Avancement.query.count()}")
        print(f"   ⚖️ Sanctions: {Sanction.query.count()}")
        print(f"   🚪 Libérations: {Liberation.query.count()}")
        print(f"   🎯 Séjours ops: {SejourOps.query.count()}")

    print("\n🎉 TOUTES LES SITUATIONS AJOUTÉES AVEC SUCCÈS!")
