#!/usr/bin/env python3
"""
Test des différentes URLs pour identifier le problème
"""

import requests
import webbrowser
import time

def test_urls():
    """Tester différentes URLs"""
    
    urls_to_test = [
        "http://127.0.0.1:3000",
        "http://localhost:3000", 
        "http://127.0.0.1:3000/courrier/arrives",
        "http://localhost:3000/courrier/arrives",
        "http://127.0.0.1:3000/api/courriers/arrives",
        "http://localhost:3000/api/courriers/arrives"
    ]
    
    print("🔍 Test des URLs...")
    print("=" * 50)
    
    for url in urls_to_test:
        try:
            response = requests.get(url, timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"{status} {url} - Status: {response.status_code}")
            
            if "/api/" in url and response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   📊 Données: {len(data)} éléments")
                except:
                    print(f"   📄 Contenu HTML")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ {url} - Erreur: {e}")
    
    print("\n" + "=" * 50)
    print("💡 RECOMMANDATIONS:")
    print("1. Utilisez http://127.0.0.1:3000 au lieu de localhost:3000")
    print("2. Vérifiez que JavaScript est activé dans votre navigateur")
    print("3. Ouvrez les outils de développement (F12) pour voir les erreurs")
    print("4. Essayez de vider le cache (Ctrl+F5)")

def open_correct_urls():
    """Ouvrir les bonnes URLs dans le navigateur"""
    print("\n🌐 Ouverture des bonnes URLs...")
    
    urls = [
        "http://127.0.0.1:3000/diagnostic",
        "http://127.0.0.1:3000/courrier/arrives", 
        "http://127.0.0.1:3000/courrier/envoyes"
    ]
    
    for url in urls:
        print(f"🔗 Ouverture: {url}")
        webbrowser.open(url)
        time.sleep(1)  # Attendre un peu entre les ouvertures

if __name__ == "__main__":
    test_urls()
    
    response = input("\n❓ Voulez-vous ouvrir les bonnes URLs dans le navigateur ? (o/n): ")
    if response.lower() in ['o', 'oui', 'y', 'yes']:
        open_correct_urls()
        print("\n✅ URLs ouvertes. Vérifiez maintenant dans votre navigateur !")
    
    print("\n🔧 INSTRUCTIONS DE DÉBOGAGE:")
    print("1. Dans le navigateur, appuyez sur F12")
    print("2. Allez dans l'onglet Console")
    print("3. Rechargez la page")
    print("4. Regardez les messages de débogage qui commencent par 🚀, 📡, ✅")
    print("5. Si vous voyez des erreurs rouges, notez-les")
