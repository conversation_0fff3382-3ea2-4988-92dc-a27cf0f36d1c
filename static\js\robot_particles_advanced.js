// Système de particules avancé pour le robot militaire
// Sauvegarde du code pour restauration ultérieure

// ===== SYSTÈME DE PARTICULES =====
// Particules pour effet high-tech
function createParticleSystem() {
    const particlesGeometry = new THREE.BufferGeometry();
    const particleCount = 100;
    const posArray = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount * 3; i += 3) {
        // Distribuer les particules dans un cylindre autour du robot
        const radius = 1.5 + Math.random() * 1.5;
        const theta = Math.random() * Math.PI * 2;
        const y = Math.random() * 4 - 1;
        
        posArray[i] = radius * Math.cos(theta);
        posArray[i+1] = y;
        posArray[i+2] = radius * Math.sin(theta);
    }
    
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    
    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.03,
        color: 0x00ff00,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
    });
    
    particles = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particles);
    
    return particles;
}
