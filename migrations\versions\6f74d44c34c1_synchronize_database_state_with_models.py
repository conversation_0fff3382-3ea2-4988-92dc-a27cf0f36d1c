"""Synchronize database state with models

Revision ID: 6f74d44c34c1
Revises: 1329bfac167c
Create Date: 2025-06-30 14:55:23.103177

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6f74d44c34c1'
down_revision = '1329bfac167c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.create_unique_constraint(None, ['matricule'])

    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=mysql.VARCHAR(length=10),
               type_=sa.String(length=20),
               nullable=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('vehicule_gar', schema=None) as batch_op:
        batch_op.alter_column('unite',
               existing_type=sa.String(length=20),
               type_=mysql.VARCHAR(length=10),
               nullable=False)

    with op.batch_alter_table('vehicule', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')

    # ### end Alembic commands ###
