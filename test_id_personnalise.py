#!/usr/bin/env python3
"""
Test pour vérifier que l'ID personnalisé est bien utilisé
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:3000"

def test_id_personnalise():
    """Tester l'ajout d'un courrier avec un ID personnalisé"""
    print("🧪 Test d'ajout avec ID personnalisé...")
    
    # ID personnalisé simple (chiffres uniquement comme demandé)
    id_personnalise = "9999"
    
    courrier_test = {
        "id": id_personnalise,
        "urgence": "urgent",
        "nature": "message",
        "date_arrivee": datetime.now().strftime('%Y-%m-%d'),
        "date_signature": datetime.now().strftime('%Y-%m-%d'),
        "numero_ecrit": "TEST-9999",
        "expediteur": "Bureau de Test",
        "objet": "Test d'ID personnalisé - Vérification",
        "classification": "public",
        "annotation": "Test pour vérifier que l'ID saisi est conservé",
        "divisions_action": ["technique"],
        "divisions_info": ["rh"]
    }
    
    try:
        # Ajouter le courrier via API
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_test)
        )
        
        if response.status_code == 201:
            courrier_ajoute = response.json()
            print(f"✅ Courrier ajouté avec succès")
            print(f"   ID demandé: {id_personnalise}")
            print(f"   ID obtenu: {courrier_ajoute['id']}")
            
            if courrier_ajoute['id'] == id_personnalise:
                print("🎉 SUCCÈS: L'ID personnalisé est correctement conservé !")
            else:
                print("❌ PROBLÈME: L'ID a été modifié")
                
            return courrier_ajoute['id'] == id_personnalise
            
        else:
            print(f"❌ Erreur lors de l'ajout: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_courriers_existants():
    """Vérifier les IDs des courriers existants"""
    print("\n🔍 Vérification des courriers existants...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers = response.json()
            print(f"📥 Courriers arrivés trouvés: {len(courriers)}")
            
            for courrier in courriers:
                print(f"   • ID: {courrier['id']} - Objet: {courrier['objet']}")
                
        else:
            print(f"❌ Erreur API: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Test principal"""
    print("🔧 TEST DE L'ID PERSONNALISÉ POUR COURRIERS ARRIVÉS")
    print("=" * 60)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Vérifier les courriers existants
    verifier_courriers_existants()
    
    # Tester l'ID personnalisé
    succes = test_id_personnalise()
    
    # Vérifier à nouveau après ajout
    verifier_courriers_existants()
    
    print("\n" + "=" * 60)
    if succes:
        print("🎉 TEST RÉUSSI")
        print("✅ La correction fonctionne : l'ID saisi est conservé")
    else:
        print("❌ TEST ÉCHOUÉ")
        print("❌ L'ID est encore modifié automatiquement")
    print("=" * 60)
    
    print("\n📋 INSTRUCTIONS:")
    print("1. 🌐 Ouvrez l'interface: http://127.0.0.1:3000/courrier/arrives")
    print("2. ➕ Cliquez sur 'Ajouter Courrier'")
    print("3. 📝 Saisissez un ID simple (ex: 1234)")
    print("4. ✅ Vérifiez que l'ID affiché dans le tableau est bien 1234")
    print("   (et non plus CA-1753107957118)")

if __name__ == "__main__":
    main()
