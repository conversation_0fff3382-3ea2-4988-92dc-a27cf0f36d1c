from flask import render_template, redirect, url_for, request, jsonify, send_file, make_response
from . import stages_bp
from .models import mock_stagiaires, mock_promotions, mock_stages, mock_types_stages
import pandas as pd
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph
from io import BytesIO
from datetime import datetime

@stages_bp.route('/')
def index():
    """Page d'accueil du module de gestion des stages"""
    return render_template('stages/accueil.html')

@stages_bp.route('/stagiaires')
def stagiaires():
    """Liste des stagiaires"""
    return render_template('stages/stagiaires.html', stagiaires=mock_stagiaires)

@stages_bp.route('/stages')
def stages():
    """Liste des stages"""
    return render_template('stages/stages.html', stages=mock_stages)

@stages_bp.route('/promotions')
def promotions():
    """Liste des promotions"""
    return render_template('stages/promotions.html', promotions=mock_promotions)

@stages_bp.route('/suivi-alertes')
def suivi_alertes():
    """Suivi et alertes"""
    return render_template('stages/suivi_alertes.html')

@stages_bp.route('/documents')
def documents():
    """Gestion des documents"""
    return render_template('stages/documents.html')

@stages_bp.route('/ia')
def ia():
    """Assistant IA"""
    return render_template('stages/ia.html')

@stages_bp.route('/utilisateurs')
def utilisateurs():
    """Gestion des utilisateurs"""
    return render_template('stages/utilisateurs.html')

@stages_bp.route('/journalisation')
def journalisation():
    """Journalisation des actions"""
    return render_template('stages/journalisation.html')

@stages_bp.route('/statistiques')
def statistiques():
    """Statistiques et rapports"""
    return render_template('stages/statistiques.html')

@stages_bp.route('/courrier')
def courrier():
    """Interface de gestion des courriers pour la Division Instruction"""
    return render_template('stages/courrier.html')

@stages_bp.route('/export-excel')
def export_excel():
    # Création d'un DataFrame avec les données des stages
    data = []
    for stage in mock_stages:
        stagiaire = next((s for s in mock_stagiaires if s.id == 1), None)  # À adapter selon votre logique
        data.append({
            'Type de stage': stage.type_stage.libelle,
            'Stagiaire': f"{stagiaire.nom} {stagiaire.prenom}" if stagiaire else "N/A",
            'Date début': stage.date_debut.strftime('%d/%m/%Y'),
            'Date fin': stage.date_fin.strftime('%d/%m/%Y'),
            'Statut': stage.statut
        })
    
    df = pd.DataFrame(data)
    
    # Création du fichier Excel en mémoire
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Stages', index=False)
        
        # Ajustement automatique de la largeur des colonnes
        worksheet = writer.sheets['Stages']
        for idx, col in enumerate(df.columns):
            max_length = max(df[col].astype(str).apply(len).max(), len(col)) + 2
            worksheet.column_dimensions[chr(65 + idx)].width = max_length
    
    output.seek(0)
    
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'stages_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    )

@stages_bp.route('/export-pdf')
def export_pdf():
    # Création du buffer pour le PDF
    buffer = BytesIO()
    
    # Création du document PDF
    doc = SimpleDocTemplate(
        buffer,
        pagesize=landscape(letter),
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )
    
    # Préparation des données
    elements = []
    styles = getSampleStyleSheet()
    
    # Titre
    title = Paragraph("Liste des Stages", styles['Heading1'])
    elements.append(title)
    elements.append(Paragraph("<br/><br/>", styles['Normal']))
    
    # En-têtes du tableau
    headers = ['Type de stage', 'Stagiaire', 'Date début', 'Date fin', 'Statut']
    data = [headers]
    
    # Données du tableau
    for stage in mock_stages:
        stagiaire = next((s for s in mock_stagiaires if s.id == 1), None)  # À adapter selon votre logique
        row = [
            stage.type_stage.libelle,
            f"{stagiaire.nom} {stagiaire.prenom}" if stagiaire else "N/A",
            stage.date_debut.strftime('%d/%m/%Y'),
            stage.date_fin.strftime('%d/%m/%Y'),
            stage.statut
        ]
        data.append(row)
    
    # Création du tableau
    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 14),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 12),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 0.25, colors.black),
    ]))
    
    elements.append(table)
    
    # Génération du PDF
    doc.build(elements)
    buffer.seek(0)
    
    return send_file(
        buffer,
        mimetype='application/pdf',
        as_attachment=True,
        download_name=f'stages_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.pdf'
    )
