#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes de gestion du personnel pour le module RH
Implémentation des routes pour la gestion du personnel militaire
"""

from flask import request, redirect, url_for, flash, jsonify
from datetime import datetime
from rh_models import *
from rh_blueprint import rh_bp

# ============================================================================
# ROUTES DE GESTION DU PERSONNEL
# ============================================================================

@rh_bp.route('/api/modifier_personnel/<matricule>', methods=['PUT'])
def api_modifier_personnel(matricule):
    """API pour modifier les informations d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        data = request.get_json()
        
        # Mise à jour des champs modifiables
        if 'nom' in data:
            personnel.nom = data['nom']
        if 'prenom' in data:
            personnel.prenom = data['prenom']
        if 'nom_arabe' in data:
            personnel.nom_arabe = data['nom_arabe']
        if 'prenom_arabe' in data:
            personnel.prenom_arabe = data['prenom_arabe']
        if 'gsm' in data:
            personnel.gsm = data['gsm']
        if 'telephone_domicile' in data:
            personnel.telephone_domicile = data['telephone_domicile']
        if 'lieu_residence' in data:
            personnel.lieu_residence = data['lieu_residence']
        if 'fonction' in data:
            personnel.fonction = data['fonction']
        if 'date_prise_fonction' in data:
            personnel.date_prise_fonction = datetime.strptime(data['date_prise_fonction'], '%Y-%m-%d').date()
        if 'unite_id' in data:
            personnel.unite_id = data['unite_id']
        if 'grade_actuel_id' in data:
            personnel.grade_actuel_id = data['grade_actuel_id']
        if 'compte_bancaire' in data:
            personnel.compte_bancaire = data['compte_bancaire']
        if 'gsm_urgence' in data:
            personnel.gsm_urgence = data['gsm_urgence']
        if 'degre_parente_id' in data:
            personnel.degre_parente_id = data['degre_parente_id']
        
        # Mise à jour des documents si fournis
        if 'date_expiration_cin' in data:
            personnel.date_expiration_cin = datetime.strptime(data['date_expiration_cin'], '%Y-%m-%d').date()
        if 'numero_passport' in data:
            personnel.numero_passport = data['numero_passport']
        if 'date_delivrance_passport' in data and data['date_delivrance_passport']:
            personnel.date_delivrance_passport = datetime.strptime(data['date_delivrance_passport'], '%Y-%m-%d').date()
        if 'date_expiration_passport' in data and data['date_expiration_passport']:
            personnel.date_expiration_passport = datetime.strptime(data['date_expiration_passport'], '%Y-%m-%d').date()
        
        # Mise à jour des langues si fournies
        if 'langues' in data:
            # Supprimer les anciennes langues
            PersonnelLangue.query.filter_by(matricule=matricule).delete()
            # Ajouter les nouvelles langues
            for langue_id in data['langues']:
                personnel_langue = PersonnelLangue(
                    matricule=matricule,
                    langue_id=langue_id
                )
                db.session.add(personnel_langue)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Personnel modifié avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_personnel/<matricule>', methods=['DELETE'])
def api_supprimer_personnel(matricule):
    """API pour supprimer un personnel (avec toutes ses données liées)"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # SQLAlchemy se charge de supprimer les données liées grâce aux cascade='all, delete-orphan'
        db.session.delete(personnel)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Personnel supprimé avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/langues', methods=['GET'])
def api_get_personnel_langues(matricule):
    """API pour récupérer les langues d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        langues = []
        
        for pl in personnel.langues:
            langues.append({
                'id': pl.langue.id_langue,
                'libelle': pl.langue.libelle
            })
        
        return jsonify({
            'success': True,
            'langues': langues
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/statistiques', methods=['GET'])
def api_get_personnel_statistiques(matricule):
    """API pour récupérer les statistiques d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # Compter les différents éléments
        nb_enfants = len(personnel.enfants)
        nb_vaccinations = len(personnel.vaccinations)
        nb_ptc = len(personnel.ptcs)
        nb_permissions = len(personnel.permissions)
        nb_detachements = len(personnel.detachements)
        nb_mutations = len(personnel.mutations)
        nb_avancements = len(personnel.avancements)
        nb_sanctions = len(personnel.sanctions)
        
        # Permissions en cours
        permissions_en_cours = 0
        for permission in personnel.permissions:
            if permission.date_debut <= datetime.now().date() <= permission.date_fin:
                permissions_en_cours += 1
        
        # PTC en cours
        ptc_en_cours = 0
        for ptc in personnel.ptcs:
            if ptc.date_debut <= datetime.now().date() <= ptc.date_fin:
                ptc_en_cours += 1
        
        # Détachements en cours
        detachements_en_cours = 0
        for detachement in personnel.detachements:
            if detachement.date_debut <= datetime.now().date() <= detachement.date_fin:
                detachements_en_cours += 1
        
        # Calcul de l'âge
        today = datetime.now().date()
        age = today.year - personnel.date_naissance.year
        if today.month < personnel.date_naissance.month or (today.month == personnel.date_naissance.month and today.day < personnel.date_naissance.day):
            age -= 1
        
        # Calcul de l'ancienneté
        anciennete_jours = (today - personnel.date_engagement).days
        anciennete_annees = anciennete_jours // 365
        
        return jsonify({
            'success': True,
            'statistiques': {
                'age': age,
                'anciennete_annees': anciennete_annees,
                'anciennete_jours': anciennete_jours,
                'nb_enfants': nb_enfants,
                'nb_vaccinations': nb_vaccinations,
                'nb_ptc': nb_ptc,
                'nb_permissions': nb_permissions,
                'nb_detachements': nb_detachements,
                'nb_mutations': nb_mutations,
                'nb_avancements': nb_avancements,
                'nb_sanctions': nb_sanctions,
                'permissions_en_cours': permissions_en_cours,
                'ptc_en_cours': ptc_en_cours,
                'detachements_en_cours': detachements_en_cours,
                'a_conjoint': personnel.conjoint is not None,
                'a_situation_medicale': personnel.situation_medicale is not None
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/export', methods=['POST'])
def api_export_personnel():
    """API pour exporter la liste du personnel selon des critères"""
    try:
        data = request.get_json()
        format_export = data.get('format', 'json')  # json, csv, excel
        
        # Construction de la requête avec filtres
        query = Personnel.query
        
        if data.get('arme_id'):
            query = query.filter(Personnel.arme_id == data['arme_id'])
        if data.get('unite_id'):
            query = query.filter(Personnel.unite_id == data['unite_id'])
        if data.get('grade_id'):
            query = query.filter(Personnel.grade_actuel_id == data['grade_id'])
        if data.get('categorie_id'):
            query = query.filter(Personnel.categorie_id == data['categorie_id'])
        
        personnel_list = query.all()
        
        if format_export == 'json':
            # Export JSON
            export_data = []
            for p in personnel_list:
                export_data.append({
                    'matricule': p.matricule,
                    'nom': p.nom,
                    'prenom': p.prenom,
                    'nom_arabe': p.nom_arabe,
                    'prenom_arabe': p.prenom_arabe,
                    'date_naissance': p.date_naissance.strftime('%Y-%m-%d'),
                    'lieu_naissance': p.lieu_naissance,
                    'sexe': p.genre.libelle,
                    'categorie': p.categorie.libelle,
                    'arme': p.arme.libelle,
                    'specialite': p.specialite.libelle if p.specialite else None,
                    'unite': p.unite.libelle,
                    'grade': p.grade_actuel.libelle,
                    'fonction': p.fonction,
                    'date_engagement': p.date_engagement.strftime('%Y-%m-%d'),
                    'gsm': p.gsm,
                    'lieu_residence': p.lieu_residence
                })
            
            return jsonify({
                'success': True,
                'data': export_data,
                'total': len(export_data)
            })
        
        else:
            return jsonify({
                'success': False,
                'error': 'Format d\'export non supporté'
            }), 400
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/recherche_avancee', methods=['POST'])
def api_recherche_avancee_personnel():
    """API pour recherche avancée du personnel avec critères multiples"""
    try:
        data = request.get_json()
        
        # Construction de la requête de base
        query = Personnel.query
        
        # Filtres textuels avec LIKE
        if data.get('nom_prenom'):
            terme = f"%{data['nom_prenom']}%"
            query = query.filter(
                db.or_(
                    Personnel.nom.like(terme),
                    Personnel.prenom.like(terme),
                    Personnel.nom_arabe.like(terme),
                    Personnel.prenom_arabe.like(terme)
                )
            )
        
        # Filtres par dates
        if data.get('date_naissance_debut'):
            query = query.filter(Personnel.date_naissance >= datetime.strptime(data['date_naissance_debut'], '%Y-%m-%d').date())
        if data.get('date_naissance_fin'):
            query = query.filter(Personnel.date_naissance <= datetime.strptime(data['date_naissance_fin'], '%Y-%m-%d').date())
        
        if data.get('date_engagement_debut'):
            query = query.filter(Personnel.date_engagement >= datetime.strptime(data['date_engagement_debut'], '%Y-%m-%d').date())
        if data.get('date_engagement_fin'):
            query = query.filter(Personnel.date_engagement <= datetime.strptime(data['date_engagement_fin'], '%Y-%m-%d').date())
        
        # Filtres par références
        if data.get('sexe_id'):
            query = query.filter(Personnel.sexe_id == data['sexe_id'])
        if data.get('arme_id'):
            query = query.filter(Personnel.arme_id == data['arme_id'])
        if data.get('unite_id'):
            query = query.filter(Personnel.unite_id == data['unite_id'])
        if data.get('grade_id'):
            query = query.filter(Personnel.grade_actuel_id == data['grade_id'])
        if data.get('categorie_id'):
            query = query.filter(Personnel.categorie_id == data['categorie_id'])
        if data.get('situation_fam_id'):
            query = query.filter(Personnel.situation_fam_id == data['situation_fam_id'])
        
        # Filtres par lieu
        if data.get('lieu_naissance'):
            query = query.filter(Personnel.lieu_naissance.like(f"%{data['lieu_naissance']}%"))
        if data.get('lieu_residence'):
            query = query.filter(Personnel.lieu_residence.like(f"%{data['lieu_residence']}%"))
        
        # Pagination
        page = data.get('page', 1)
        per_page = data.get('per_page', 50)
        
        # Tri
        sort_by = data.get('sort_by', 'nom')
        sort_order = data.get('sort_order', 'asc')
        
        if sort_by == 'nom':
            if sort_order == 'desc':
                query = query.order_by(Personnel.nom.desc())
            else:
                query = query.order_by(Personnel.nom.asc())
        elif sort_by == 'date_engagement':
            if sort_order == 'desc':
                query = query.order_by(Personnel.date_engagement.desc())
            else:
                query = query.order_by(Personnel.date_engagement.asc())
        elif sort_by == 'grade':
            query = query.join(ReferentielGrade)
            if sort_order == 'desc':
                query = query.order_by(ReferentielGrade.libelle.desc())
            else:
                query = query.order_by(ReferentielGrade.libelle.asc())
        
        # Exécution avec pagination
        resultats = query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Formatage des résultats
        personnel_list = []
        for p in resultats.items:
            personnel_list.append({
                'matricule': p.matricule,
                'nom_complet': p.nom_complet,
                'nom_complet_arabe': p.nom_complet_arabe,
                'date_naissance': p.date_naissance.strftime('%d/%m/%Y'),
                'lieu_naissance': p.lieu_naissance,
                'sexe': p.genre.libelle,
                'categorie': p.categorie.libelle,
                'arme': p.arme.libelle,
                'specialite': p.specialite.libelle if p.specialite else '',
                'unite': p.unite.libelle,
                'grade': p.grade_actuel.libelle,
                'fonction': p.fonction,
                'date_engagement': p.date_engagement.strftime('%d/%m/%Y'),
                'gsm': p.gsm,
                'lieu_residence': p.lieu_residence,
                'situation_familiale': p.situation_familiale.libelle
            })
        
        return jsonify({
            'success': True,
            'personnel': personnel_list,
            'pagination': {
                'page': resultats.page,
                'pages': resultats.pages,
                'per_page': resultats.per_page,
                'total': resultats.total,
                'has_next': resultats.has_next,
                'has_prev': resultats.has_prev
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
