{% extends "stages/base_stages.html" %}

{% block title %}{{ action }} une Inscription{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">{{ action }} une Inscription</h1>
    </div>
    <div class="card">
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="stagiaire" class="form-label">Stagiaire</label>
                        <select class="form-select" id="stagiaire" name="stagiaire" required>
                            <option value="">Sélectionner...</option>
                            {% for s in stagiaires %}
                                <option value="{{ s.id_stagiaire }}" {% if inscription and inscription.id_stagiaire == s.id_stagiaire %}selected{% endif %}>{{ s.nom }} {{ s.prenom }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="promotion" class="form-label">Promotion</label>
                        <select class="form-select" id="promotion" name="promotion" required>
                            <option value="">Sélectionner...</option>
                            {% for p in promotions %}
                                <option value="{{ p.id_promotion }}" {% if inscription and inscription.id_promotion == p.id_promotion %}selected{% endif %}>{{ p.nom }} ({{ p.annee }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="stage" class="form-label">Stage</label>
                        <select class="form-select" id="stage" name="stage" required>
                            <option value="">Sélectionner...</option>
                            {% for st in stages %}
                                <option value="{{ st.id_stage }}" {% if inscription and inscription.id_stage == st.id_stage %}selected{% endif %}>{{ st.type_stage.libelle }} ({{ st.date_debut.strftime('%d/%m/%Y') }} - {{ st.date_fin.strftime('%d/%m/%Y') }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="date_inscription" class="form-label">Date d'inscription</label>
                    <input type="date" class="form-control" id="date_inscription" name="date_inscription" value="{{ inscription.date_inscription.strftime('%Y-%m-%d') if inscription else '' }}" required>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_inscriptions') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> {{ action }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const typeStageSelect = document.getElementById('id_type_stage');
    const stageSelect = document.getElementById('id_stage');

    typeStageSelect.addEventListener('change', function() {
        const typeId = this.value;
        stageSelect.innerHTML = '<option value="">Chargement...</option>';
        stageSelect.disabled = true;

        if (!typeId) {
            stageSelect.innerHTML = '<option value="">Sélectionnez un stage</option>';
            return;
        }

        fetch(`/api/stages_par_type/${typeId}`)
            .then(response => response.json())
            .then(data => {
                stageSelect.innerHTML = '<option value="">Sélectionnez un stage</option>';
                if (data.length > 0) {
                    data.forEach(function(stage) {
                        const option = document.createElement('option');
                        option.value = stage.id;
                        option.textContent = `Du ${stage.date_debut} au ${stage.date_fin}`;
                        stageSelect.appendChild(option);
                    });
                    stageSelect.disabled = false;
                } else {
                    stageSelect.innerHTML = '<option value="">Aucun stage disponible pour ce type</option>';
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                stageSelect.innerHTML = '<option value="">Erreur de chargement</option>';
            });
    });
});
</script>
{% endblock %} 