from app import app
from db import db
from sqlalchemy import text

with app.app_context():
    try:
        # Test de connexion
        with db.engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("✅ Connexion à la base de données réussie")

            # Lister les tables
            result = connection.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]

            print(f"📋 {len(tables)} tables trouvées")

            # Filtrer les tables RH
            rh_tables = [t for t in tables if 'referentiel_' in t or t in ['personnel', 'conjoint', 'enfant', 'situation_medicale', 'vaccination', 'ptc', 'absence_desertion', 'absence_detachement', 'absence_permission', 'mouvement_interbie', 'sejour_ops', 'liberation', 'personnel_langue', 'historique_grade']]

            print(f"\n🎯 Tables RH ({len(rh_tables)}/25):")
            for table in sorted(rh_tables):
                print(f"  ✓ {table}")

            if len(rh_tables) >= 24:
                print("\n✅ ÉTAPE 3 TERMINÉE AVEC SUCCÈS!")
                print("Les 25 tables RH sont créées et prêtes.")
            else:
                print(f"\n⚠️ Seulement {len(rh_tables)} tables RH sur 25 attendues")

    except Exception as e:
        print(f"❌ Erreur: {e}")
