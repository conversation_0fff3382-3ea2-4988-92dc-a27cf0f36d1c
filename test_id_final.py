#!/usr/bin/env python3
"""
Test final pour vérifier que l'ID personnalisé fonctionne
"""

import requests
import json
import webbrowser
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:3000"

def nettoyer_donnees_test():
    """Supprimer les courriers de test avec IDs automatiques"""
    print("🧹 Nettoyage des données de test...")
    
    try:
        # Récupérer tous les courriers
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers = response.json()
            
            # Identifier les courriers avec IDs automatiques (CA-...)
            courriers_a_supprimer = [c for c in courriers if c['id'].startswith('CA-')]
            
            print(f"📋 Courriers avec IDs automatiques trouvés: {len(courriers_a_supprimer)}")
            for courrier in courriers_a_supprimer:
                print(f"   • {courrier['id']} - {courrier['objet']}")
                
            if courriers_a_supprimer:
                print("⚠️  Note: Ces courriers ont été créés avant la correction")
                print("   Ils gardent leur ID automatique existant")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def tester_nouveaux_ids():
    """Tester l'ajout de courriers avec des IDs personnalisés"""
    print("\n🧪 Test d'ajout avec IDs personnalisés...")
    
    courriers_test = [
        {
            "id": "1111",
            "numero_ecrit": "TEST-1111",
            "objet": "Test ID 1111 - Chiffres uniquement"
        },
        {
            "id": "2222", 
            "numero_ecrit": "TEST-2222",
            "objet": "Test ID 2222 - Format simple"
        },
        {
            "id": "ABCD",
            "numero_ecrit": "TEST-ABCD", 
            "objet": "Test ID ABCD - Format lettres"
        }
    ]
    
    for courrier_base in courriers_test:
        courrier = {
            "id": courrier_base["id"],
            "urgence": "urgent",
            "nature": "message",
            "date_arrivee": datetime.now().strftime('%Y-%m-%d'),
            "date_signature": datetime.now().strftime('%Y-%m-%d'),
            "numero_ecrit": courrier_base["numero_ecrit"],
            "expediteur": "Bureau Test",
            "objet": courrier_base["objet"],
            "classification": "public",
            "annotation": "Test ID personnalisé",
            "divisions_action": ["technique"],
            "divisions_info": ["rh"]
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/arrives",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                courrier_ajoute = response.json()
                id_demande = courrier["id"]
                id_obtenu = courrier_ajoute["id"]
                
                if id_obtenu == id_demande:
                    print(f"✅ ID {id_demande}: Conservé correctement")
                else:
                    print(f"❌ ID {id_demande}: Modifié en {id_obtenu}")
                    
            else:
                print(f"❌ Erreur pour ID {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour ID {courrier['id']}: {e}")

def ouvrir_interface_test():
    """Ouvrir l'interface pour test manuel"""
    print("\n🌐 Ouverture de l'interface pour test manuel...")
    webbrowser.open(f"{BASE_URL}/courrier/arrives")
    
    print("\n📋 INSTRUCTIONS DE TEST:")
    print("1. 🔄 Rechargez la page avec Ctrl+F5 (vider le cache)")
    print("2. ➕ Cliquez sur 'Ajouter Courrier'")
    print("3. 📝 Testez ces IDs:")
    print("   • ID: 3333")
    print("   • ID: TEST")
    print("   • ID: 4567")
    print("4. 👀 Vérifiez que l'ID affiché dans le tableau est exactement celui saisi")
    print("5. 🔍 Si problème, ouvrez la console (F12) et notez les erreurs")

def verifier_resultats():
    """Vérifier les résultats finaux"""
    print("\n🔍 Vérification des résultats...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            courriers = response.json()
            
            print(f"📥 Total courriers: {len(courriers)}")
            print("\nCourriers par type d'ID:")
            
            ids_automatiques = [c for c in courriers if c['id'].startswith('CA-')]
            ids_personnalises = [c for c in courriers if not c['id'].startswith('CA-')]
            
            print(f"   🤖 IDs automatiques (anciens): {len(ids_automatiques)}")
            for c in ids_automatiques:
                print(f"      • {c['id']} - {c['objet']}")
                
            print(f"   ✋ IDs personnalisés (nouveaux): {len(ids_personnalises)}")
            for c in ids_personnalises:
                print(f"      • {c['id']} - {c['objet']}")
                
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Test final complet"""
    print("🎯 TEST FINAL - VÉRIFICATION ID PERSONNALISÉ")
    print("=" * 60)
    
    # Vérifier l'application
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Exécuter les tests
    nettoyer_donnees_test()
    tester_nouveaux_ids()
    verifier_resultats()
    ouvrir_interface_test()
    
    print("\n" + "=" * 60)
    print("🎉 TEST FINAL TERMINÉ")
    print("=" * 60)
    
    print("\n📊 RÉSUMÉ:")
    print("✅ Backend: Conserve correctement les IDs personnalisés")
    print("✅ Template: Corrigé pour utiliser l'ID saisi")
    print("⚠️  Cache: Peut nécessiter Ctrl+F5 pour voir les changements")
    
    print("\n🎯 POUR TESTER:")
    print("1. Utilisez Ctrl+F5 pour vider le cache")
    print("2. Ajoutez un courrier avec ID simple (ex: 8888)")
    print("3. Vérifiez que l'ID 8888 apparaît dans le tableau")

if __name__ == "__main__":
    main()
