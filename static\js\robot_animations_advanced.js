// Animations avancées du robot militaire
// Sauvegarde du code pour restauration ultérieure

function animate() {
    requestAnimationFrame(animate);
    
    // Mise à jour du temps pour les animations
    const delta = clock.getDelta();
    const elapsedTime = clock.getElapsedTime();
    
    if (robot) {
        // Rotation de base plus fluide
        robot.rotation.y += ROTATION_SPEED * Math.sin(elapsedTime * 0.2) * 0.5 + ROTATION_SPEED;
        
        // Animation de flottement avancée avec plusieurs fréquences
        robot.position.y = -0.5 + 
            Math.sin(elapsedTime * 0.8) * 0.07 + 
            Math.sin(elapsedTime * 1.2) * 0.03;
            
        // Légère oscillation sur l'axe X pour plus de dynamisme
        robot.rotation.z = Math.sin(elapsedTime * 0.4) * 0.02;
        
        // Animation des antennes
        if (robot.children) {
            // Trouver les groupes d'antennes
            robot.children.forEach(child => {
                if (child.name === 'leftAntennaGroup' || child.name === 'rightAntennaGroup') {
                    // Oscillation des antennes
                    child.rotation.z = Math.sin(elapsedTime * 2 + (child.name === 'leftAntennaGroup' ? 0 : Math.PI)) * 0.05;
                }
            });
            
            // Animation des lumières des antennes
            const leftAntennaGroup = robot.children.find(child => child.name === 'leftAntennaGroup');
            const rightAntennaGroup = robot.children.find(child => child.name === 'rightAntennaGroup');
            
            if (leftAntennaGroup && rightAntennaGroup) {
                // Trouver les lumières des antennes
                const leftLight = leftAntennaGroup.children.find(child => child instanceof THREE.PointLight);
                const rightLight = rightAntennaGroup.children.find(child => child instanceof THREE.PointLight);
                
                if (leftLight && rightLight) {
                    // Clignotement alterné des lumières
                    const blinkIntensity = (Math.sin(elapsedTime * 4) + 1) / 2;
                    leftLight.intensity = 0.4 + blinkIntensity * 0.6;
                    rightLight.intensity = 0.4 + (1 - blinkIntensity) * 0.6;
                }
            }
        }
    }
    
    // Animation des particules
    if (particles) {
        // Rotation lente des particules
        particles.rotation.y += delta * 0.05;
        
        // Mise à jour des positions des particules pour un effet de scintillement
        const positions = particles.geometry.attributes.position.array;
        for (let i = 0; i < positions.length; i += 3) {
            // Mouvement vertical lent
            positions[i+1] += Math.sin(elapsedTime + i) * 0.001;
            
            // Réinitialiser les particules qui sortent de la zone visible
            if (positions[i+1] > 3) positions[i+1] = -1;
        }
        particles.geometry.attributes.position.needsUpdate = true;
    }
    
    renderer.render(scene, camera);
}

function animateRobot(type) {
    if (!robot) return;
    
    // Trouver les composants spécifiques pour des animations plus détaillées
    const headGroup = robot.children.find(child => child instanceof THREE.Group && child.position.y === 1.2);
    const leftEye = robot.children.find(child => child instanceof THREE.Mesh && child.position.x < 0 && child.position.y > 1);
    const rightEye = robot.children.find(child => child instanceof THREE.Mesh && child.position.x > 0 && child.position.y > 1);
    
    // Nommer les groupes d'antennes pour les retrouver facilement
    robot.children.forEach(child => {
        if (child instanceof THREE.Group && child.position.y === 1.8) {
            if (child.position.x < 0) {
                child.name = 'leftAntennaGroup';
            } else if (child.position.x > 0) {
                child.name = 'rightAntennaGroup';
            }
        }
    });
    
    const leftAntennaGroup = robot.children.find(child => child.name === 'leftAntennaGroup');
    const rightAntennaGroup = robot.children.find(child => child.name === 'rightAntennaGroup');
    
    switch(type) {
        case 'thinking':
            // Animation de réflexion plus complexe
            // Inclinaison de la tête
            if (headGroup) {
                gsap.to(headGroup.rotation, {
                    x: 0.3,
                    duration: 0.8,
                    yoyo: true,
                    repeat: 1,
                    ease: "power2.inOut"
                });
            }
            
            // Clignotement des yeux plus rapide
            if (leftEye && rightEye) {
                // Séquence pour l'œil gauche
                gsap.to(leftEye.material, {
                    emissiveIntensity: 0.2,
                    duration: 0.2,
                    yoyo: true,
                    repeat: 5,
                    ease: "power1.inOut"
                });
                
                // Séquence pour l'œil droit avec léger décalage
                gsap.to(rightEye.material, {
                    emissiveIntensity: 0.2,
                    duration: 0.2,
                    delay: 0.1,
                    yoyo: true,
                    repeat: 5,
                    ease: "power1.inOut"
                });
            }
            
            // Mouvement des antennes
            if (leftAntennaGroup && rightAntennaGroup) {
                gsap.to(leftAntennaGroup.rotation, {
                    z: 0.2,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });
                
                gsap.to(rightAntennaGroup.rotation, {
                    z: -0.2,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });
            }
            break;
            
        case 'speaking':
            // Animation de parole plus sophistiquée
            // Léger mouvement de la tête
            if (headGroup) {
                gsap.to(headGroup.rotation, {
                    y: 0.1,
                    duration: 0.2,
                    yoyo: true,
                    repeat: 3,
                    ease: "power1.inOut"
                });
            }
            
            // Pulsation des yeux
            if (leftEye && rightEye) {
                const eyeAnimation = {
                    scale: 1.0
                };
                
                gsap.to(eyeAnimation, {
                    scale: 1.3,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3,
                    onUpdate: () => {
                        leftEye.scale.set(eyeAnimation.scale, eyeAnimation.scale, eyeAnimation.scale);
                        rightEye.scale.set(eyeAnimation.scale, eyeAnimation.scale, eyeAnimation.scale);
                    }
                });
                
                // Intensité lumineuse
                gsap.to(leftEye.material, {
                    emissiveIntensity: 1.5,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3
                });
                
                gsap.to(rightEye.material, {
                    emissiveIntensity: 1.5,
                    duration: 0.15,
                    yoyo: true,
                    repeat: 3
                });
            }
            
            // Léger rebond du robot
            gsap.to(robot.position, {
                y: robot.position.y + 0.15,
                duration: 0.2,
                yoyo: true,
                repeat: 1,
                ease: "power2.out"
            });
            break;
            
        case 'greeting':
            // Animation de salutation plus élaborée
            // Rotation complète avec effet de rebond
            gsap.to(robot.rotation, {
                y: robot.rotation.y + Math.PI * 2,
                duration: 1.5,
                ease: "back.inOut(1.7)"
            });
            
            // Mouvement vertical
            gsap.to(robot.position, {
                y: robot.position.y + 0.3,
                duration: 0.75,
                yoyo: true,
                repeat: 1,
                ease: "power2.inOut"
            });
            
            // Animation des yeux
            if (leftEye && rightEye) {
                // Séquence pour les deux yeux
                gsap.to([leftEye.material, rightEye.material], {
                    emissiveIntensity: 2.0,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1
                });
            }
            
            // Animation des antennes
            if (leftAntennaGroup && rightAntennaGroup) {
                // Mouvement synchronisé des antennes
                gsap.to([leftAntennaGroup.rotation, rightAntennaGroup.rotation], {
                    z: 0.3,
                    duration: 0.5,
                    yoyo: true,
                    repeat: 1,
                    stagger: 0.2
                });
            }
            break;
    }
}
