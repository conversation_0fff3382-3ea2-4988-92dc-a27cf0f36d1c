#!/usr/bin/env python3
"""
Vérifier les tables manquantes
"""

import mysql.connector
from mysql.connector import Error

def check_missing_tables():
    """Vérifier quelles tables manquent"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("🔍 Vérification des tables manquantes...")
        
        # Lister toutes les tables
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        # Tables RH attendues selon architecture_rh.md
        expected_rh_tables = [
            # Tables de référence
            'referentiel_genre',
            'referentiel_categorie', 
            'referentiel_groupe_sanguin',
            'referentiel_arme',
            'referentiel_specialite',
            'referentiel_unite',
            'referentiel_grade',
            'referentiel_situation_familiale',
            'referentiel_degre_parente',
            'referentiel_langue',
            
            # Tables de données
            'personnel',
            'personnel_langue',
            'conjoint',
            'enfant',
            'situation_medicale',
            'vaccination',
            'ptc',
            'permission',
            'desertion',
            'detachement',
            'mutation_fonction',
            'sejour_ops',
            'mouvement_interbie',
            'liberation',
            'avancement',
            'sanction'
        ]
        
        print(f"📊 Total des tables dans la base: {len(existing_tables)}")
        
        # Vérifier les tables RH
        rh_tables_found = []
        rh_tables_missing = []
        
        for table in expected_rh_tables:
            if table in existing_tables:
                rh_tables_found.append(table)
            else:
                rh_tables_missing.append(table)
        
        print(f"\n✅ Tables RH trouvées ({len(rh_tables_found)}/{len(expected_rh_tables)}):")
        for table in sorted(rh_tables_found):
            print(f"  ✓ {table}")
        
        if rh_tables_missing:
            print(f"\n❌ Tables RH manquantes ({len(rh_tables_missing)}):")
            for table in sorted(rh_tables_missing):
                print(f"  ✗ {table}")
        else:
            print("\n🎉 Toutes les tables RH sont présentes!")
        
        # Vérifier les données dans les tables principales
        print(f"\n📊 Vérification des données:")
        
        data_tables = ['personnel', 'referentiel_genre', 'referentiel_arme', 'referentiel_unite']
        for table in data_tables:
            if table in existing_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} enregistrements")
        
        return rh_tables_missing
        
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return []
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def create_final_missing_tables(missing_tables):
    """Créer les dernières tables manquantes"""
    
    if not missing_tables:
        print("✅ Aucune table manquante!")
        return True
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '',
        'database': 'gestion_vehicules',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print(f"\n🔧 Création des {len(missing_tables)} tables manquantes...")
        
        # Définitions des tables manquantes
        table_definitions = {
            'permission': """
                CREATE TABLE permission (
                    id_permission INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    date_debut DATE NOT NULL,
                    date_fin DATE NOT NULL,
                    adresse VARCHAR(200) NOT NULL,
                    numero_serie VARCHAR(50) NOT NULL,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule)
                )
            """,
            'desertion': """
                CREATE TABLE desertion (
                    id_desertion INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    date_absence DATE NOT NULL,
                    date_desertion DATE NOT NULL,
                    date_retour DATE NOT NULL,
                    date_arret_solde DATE,
                    date_prise_solde DATE,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule)
                )
            """,
            'detachement': """
                CREATE TABLE detachement (
                    id_detachement INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    date_debut DATE NOT NULL,
                    adresse_detachement VARCHAR(200) NOT NULL,
                    pays VARCHAR(100) NOT NULL,
                    date_fin DATE NOT NULL,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule)
                )
            """,
            'mutation_fonction': """
                CREATE TABLE mutation_fonction (
                    id_mutation INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    service_id INT NOT NULL,
                    fonction VARCHAR(100) NOT NULL,
                    date_debut DATE NOT NULL,
                    date_fin DATE,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                    FOREIGN KEY (service_id) REFERENCES referentiel_unite(id_unite)
                )
            """,
            'avancement': """
                CREATE TABLE avancement (
                    id_avancement INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    grade_precedent_id INT NOT NULL,
                    grade_suivant_id INT NOT NULL,
                    date_avancement DATE NOT NULL,
                    conditions TEXT,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                    FOREIGN KEY (grade_precedent_id) REFERENCES referentiel_grade(id_grade),
                    FOREIGN KEY (grade_suivant_id) REFERENCES referentiel_grade(id_grade)
                )
            """,
            'sanction': """
                CREATE TABLE sanction (
                    id_sanction INT AUTO_INCREMENT PRIMARY KEY,
                    matricule VARCHAR(20) NOT NULL,
                    date_sanction DATE NOT NULL,
                    type_sanction ENUM('sanction','punition') NOT NULL,
                    duree INT,
                    motif VARCHAR(200) NOT NULL,
                    observation TEXT,
                    FOREIGN KEY (matricule) REFERENCES personnel(matricule)
                )
            """
        }
        
        for table in missing_tables:
            if table in table_definitions:
                cursor.execute(table_definitions[table])
                print(f"  ✅ Table {table} créée")
            else:
                print(f"  ⚠️ Définition manquante pour {table}")
        
        connection.commit()
        print("🎉 Tables manquantes créées!")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur MySQL: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def main():
    """Fonction principale"""
    print("🚀 Vérification finale des tables RH")
    print("=" * 50)
    
    missing_tables = check_missing_tables()
    
    if missing_tables:
        print(f"\n📋 Création des {len(missing_tables)} tables manquantes...")
        if create_final_missing_tables(missing_tables):
            print("\n🔄 Nouvelle vérification...")
            missing_tables = check_missing_tables()
    
    if not missing_tables:
        print("\n" + "=" * 50)
        print("🎉 Base de données RH complètement configurée!")
        print("\n📋 Application prête:")
        print("1. L'application Flask est en cours d'exécution")
        print("2. Accéder à: http://localhost:5000/")
        print("3. Cliquer sur 'Gestion RH' dans le menu principal")
        print("4. Tester toutes les fonctionnalités")
    else:
        print(f"\n⚠️ Il reste {len(missing_tables)} tables manquantes")
    
    return len(missing_tables) == 0

if __name__ == '__main__':
    main()
