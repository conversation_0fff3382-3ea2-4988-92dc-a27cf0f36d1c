#!/usr/bin/env python3
"""
Test final après correction des erreurs JavaScript
"""

import requests
import json
import webbrowser
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:3000"

def test_api_functionality():
    """Tester que les API fonctionnent toujours"""
    print("🔍 Test des API...")
    
    try:
        # Test courriers arrivés
        response = requests.get(f"{BASE_URL}/api/courriers/arrives")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Courriers Arrivés: {len(data)} courriers")
        else:
            print(f"❌ API Courriers Arrivés: Erreur {response.status_code}")
            return False
            
        # Test courriers envoyés
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Courriers Envoyés: {len(data)} courriers")
        else:
            print(f"❌ API Courriers Envoyés: Erreur {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def add_test_courrier_final():
    """Ajouter un courrier de test final"""
    print("\n📝 Ajout d'un courrier de test final...")
    
    timestamp = int(datetime.now().timestamp())
    
    # Courrier arrivé
    courrier_arrive = {
        "id": f"CA-FINAL-{timestamp}",
        "urgence": "urgent",
        "nature": "message",
        "date_arrivee": datetime.now().strftime('%Y-%m-%d'),
        "date_signature": datetime.now().strftime('%Y-%m-%d'),
        "numero_ecrit": f"FINAL-{timestamp}",
        "expediteur": "Test Final",
        "objet": f"Courrier de test final - {datetime.now().strftime('%H:%M:%S')}",
        "classification": "public",
        "annotation": "Test après correction des erreurs JavaScript",
        "divisions_action": ["technique"],
        "divisions_info": ["rh"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/courriers/arrives",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_arrive)
        )
        
        if response.status_code == 201:
            print(f"✅ Courrier arrivé ajouté: {courrier_arrive['id']}")
        else:
            print(f"❌ Erreur: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    
    # Courrier envoyé
    courrier_envoye = {
        "id": f"CE-FINAL-{timestamp}",
        "numero_ecrit": f"FINAL-ENV-{timestamp}",
        "division_emettrice": "technique",
        "date_depart": datetime.now().strftime('%Y-%m-%d'),
        "nature": "nds",
        "objet": f"Courrier envoyé de test final - {datetime.now().strftime('%H:%M:%S')}",
        "destinataire": "Test Final Destinataire",
        "observations": "Test après correction des erreurs JavaScript"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/courriers/envoyes",
            headers={"Content-Type": "application/json"},
            data=json.dumps(courrier_envoye)
        )
        
        if response.status_code == 201:
            print(f"✅ Courrier envoyé ajouté: {courrier_envoye['id']}")
        else:
            print(f"❌ Erreur: {response.text}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def open_final_test_pages():
    """Ouvrir les pages pour le test final"""
    print("\n🌐 Ouverture des pages pour test final...")
    
    pages = [
        f"{BASE_URL}/courrier/arrives",
        f"{BASE_URL}/courrier/envoyes"
    ]
    
    for url in pages:
        print(f"🔗 Ouverture: {url}")
        webbrowser.open(url)
        time.sleep(1)

def main():
    """Test final complet"""
    print("🎯 TEST FINAL APRÈS CORRECTION DES ERREURS JAVASCRIPT")
    print("=" * 60)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ Application non accessible")
            return
    except:
        print(f"❌ Application non accessible")
        return
    
    print(f"✅ Application accessible")
    
    # Tester les API
    if not test_api_functionality():
        print("❌ Problème avec les API")
        return
    
    # Ajouter des courriers de test
    add_test_courrier_final()
    
    # Ouvrir les pages
    open_final_test_pages()
    
    print("\n" + "=" * 60)
    print("🎉 TEST FINAL TERMINÉ")
    print("=" * 60)
    
    print("\n📋 INSTRUCTIONS FINALES:")
    print("1. 🌐 Les pages sont maintenant ouvertes dans votre navigateur")
    print("2. 🔍 Vérifiez que les courriers s'affichent maintenant")
    print("3. 🛠️ Ouvrez la console (F12) pour voir les messages de débogage")
    print("4. ✅ Vous devriez voir:")
    print("   • 🚀 Initialisation de la page...")
    print("   • ✅ Élément trouvé: tbody-courriers")
    print("   • 📡 Chargement des courriers depuis l'API...")
    print("   • ✅ Courriers chargés: X")
    print("   • 📊 Compteurs mis à jour: X total...")
    print("   • ✅ Tableau rendu avec X courriers affichés")
    print("\n5. 🚨 Si vous voyez encore des erreurs rouges:")
    print("   • Notez l'erreur exacte")
    print("   • Vérifiez l'onglet Network pour les requêtes API")
    print("   • Essayez Ctrl+F5 pour vider le cache")
    
    print("\n🎯 OBJECTIF: Voir les courriers dans les tableaux !")

if __name__ == "__main__":
    main()
