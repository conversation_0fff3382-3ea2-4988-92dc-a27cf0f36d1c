{% extends 'stages/base_stages.html' %}
{% block title %}Stagiaires & Stages{% endblock %}
{% block content %}
<div class="text-center mt-5">
    <h2 class="mb-4">Stagiaires & Stages</h2>
    <div class="d-flex flex-wrap justify-content-center gap-3 mb-4">
        <button class="btn btn-primary btn-lg" id="btn-stagiaires"><i class="fas fa-users me-2"></i>Liste des Stagiaires</button>
        <button class="btn btn-success btn-lg" id="btn-ajouter-stagiaire"><i class="fas fa-user-plus me-2"></i>Ajouter un Stagiaire</button>
        <button class="btn btn-secondary btn-lg" id="btn-types"><i class="fas fa-chalkboard-teacher me-2"></i>Types de Stage</button>
        <button class="btn btn-info btn-lg" id="btn-conditions"><i class="fas fa-file-alt me-2"></i>Conditions d'admission</button>
        <button class="btn btn-warning btn-lg" id="btn-suivi"><i class="fas fa-eye me-2"></i>Suivi des stages en cours</button>
    </div>
    <div id="section-stagiaires">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">Photo</th>
                                    <th scope="col">Nom</th>
                                    <th scope="col">Prénom</th>
                                    <th scope="col">Email</th>
                                    <th scope="col">Date de naissance</th>
                                    <th scope="col">Téléphone</th>
                                    <th scope="col" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stagiaire in stagiaires %}
                                <tr>
                                    <td>
                                        {% if stagiaire.photo %}
                                            <img src="{{ url_for('uploaded_file', filename=stagiaire.photo) }}" alt="Photo de {{ stagiaire.nom }}" class="img-thumbnail" style="width: 50px; height: 50px;">
                                        {% else %}
                                            <img src="{{ url_for('static', filename='images/default_avatar.png') }}" alt="Avatar par défaut" class="img-thumbnail" style="width: 50px; height: 50px;">
                                        {% endif %}
                                    </td>
                                    <td>{{ stagiaire.nom }}</td>
                                    <td>{{ stagiaire.prenom }}</td>
                                    <td>{{ stagiaire.email }}</td>
                                    <td>{{ stagiaire.date_naissance.strftime('%d/%m/%Y') if stagiaire.date_naissance else '' }}</td>
                                    <td>{{ stagiaire.telephone or '' }}</td>
                                    <td class="text-center">
                                        <a href="{{ url_for('modifier_stagiaire', id=stagiaire.id_stagiaire) }}" class="btn btn-warning btn-sm">Modifier</a>
                                        <form action="{{ url_for('supprimer_stagiaire', id=stagiaire.id_stagiaire) }}" method="post" style="display:inline;">
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce stagiaire ?');">Supprimer</button>
                                        </form>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">Aucun stagiaire trouvé.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="section-ajouter-stagiaire" style="display:none;">
        <div class="container mt-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="mb-3">Ajouter un Stagiaire</h4>
                    <form method="POST" enctype="multipart/form-data" action="{{ url_for('ajouter_stagiaire') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nom" class="form-label">Nom</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="prenom" class="form-label">Prénom</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date_naissance" class="form-label">Date de naissance</label>
                                <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="adresse" class="form-label">Adresse</label>
                                <input type="text" class="form-control" id="adresse" name="adresse">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="text" class="form-control" id="telephone" name="telephone">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="photo" class="form-label">Photo</label>
                            <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-success">Ajouter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div id="section-types" style="display:none;">
        <div class="container-fluid">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Nom du Type de Stage</th>
                                    <th scope="col">Conditions d'Admission</th>
                                    <th scope="col" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for type in types %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ type.libelle }}</td>
                                    <td>{{ type.conditions_admission }}</td>
                                    <td class="text-center">
                                        <a href="{{ url_for('modifier_type_stage', id=type.id_type_stage) }}" class="btn btn-warning btn-sm" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="4" class="text-center">Aucun type de stage trouvé.</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="section-conditions" style="display:none;">
        <div class="container mt-5">
            <h1 class="mb-4">Conditions d'Admission par Type de Stage</h1>
            <div class="accordion" id="accordionConditions">
                {% for type_stage in types_stage %}
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading{{ type_stage.id_type_stage }}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ type_stage.id_type_stage }}" aria-expanded="false" aria-controls="collapse{{ type_stage.id_type_stage }}">
                            <strong>{{ type_stage.libelle }}</strong>
                        </button>
                    </h2>
                    <div id="collapse{{ type_stage.id_type_stage }}" class="accordion-collapse collapse" aria-labelledby="heading{{ type_stage.id_type_stage }}" data-bs-parent="#accordionConditions">
                        <div class="accordion-body">
                            <p><strong>Conditions d'admission :</strong></p>
                            <p>{{ type_stage.conditions_admission | safe if type_stage.conditions_admission else "Aucune condition spécifique." }}</p>
                            <hr>
                            <p><strong>Documents requis :</strong></p>
                            <p>{{ type_stage.documents_requis | safe if type_stage.documents_requis else "Aucun document spécifique requis." }}</p>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="alert alert-info">
                    Aucun type de stage n'a été trouvé. Veuillez en ajouter pour voir les conditions d'admission.
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    <div id="section-suivi" style="display:none;">
        <div class="container mt-5">
            <h1 class="mb-4">Suivi des stages en cours</h1>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Répartition des stages par type</h5>
                            <canvas id="chartTypeStage"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Répartition des stages par statut</h5>
                            <div style="width: 320px; margin: 0 auto;">
                                <canvas id="chartStatutStage"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Évolution du nombre de stages par mois</h5>
                            <canvas id="chartStagesParMois"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Compétences évaluées (Radar)</h5>
                            <canvas id="chartRadarCompetences"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="application/json" id="data-type-labels">{{ stages_stats.type_labels|tojson }}</script>
<script type="application/json" id="data-stages-par-type">{{ stages_stats.stages_par_type|tojson }}</script>
<script type="application/json" id="data-statut-labels">{{ stages_stats.statuts|tojson }}</script>
<script type="application/json" id="data-stages-par-statut">{{ stages_stats.stages_par_statut|tojson }}</script>
<script type="application/json" id="data-stages-par-mois">{{ stages_stats.stages_par_mois|tojson }}</script>
<script type="application/json" id="data-labels-mois">{{ stages_stats.labels_mois|tojson }}</script>
<script type="application/json" id="data-competences-labels">{{ stages_stats.competences_labels|tojson }}</script>
<script type="application/json" id="data-competences-values">{{ stages_stats.competences_values|tojson }}</script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
window.chartStagesParMois = null;
window.chartRadarCompetences = null;
document.addEventListener('DOMContentLoaded', function() {
    // Désactiver toute logique d'outline : les boutons gardent leur couleur remplie d'origine
    // On ne modifie plus les classes btn-*
    document.getElementById('btn-stagiaires').addEventListener('click', function() {
        document.getElementById('section-stagiaires').style.display = '';
        document.getElementById('section-ajouter-stagiaire').style.display = 'none';
        document.getElementById('section-types').style.display = 'none';
        document.getElementById('section-conditions').style.display = 'none';
        document.getElementById('section-suivi').style.display = 'none';
    });
    document.getElementById('btn-types').addEventListener('click', function() {
        document.getElementById('section-stagiaires').style.display = 'none';
        document.getElementById('section-ajouter-stagiaire').style.display = 'none';
        document.getElementById('section-types').style.display = '';
        document.getElementById('section-conditions').style.display = 'none';
        document.getElementById('section-suivi').style.display = 'none';
    });
    document.getElementById('btn-conditions').addEventListener('click', function() {
        document.getElementById('section-stagiaires').style.display = 'none';
        document.getElementById('section-ajouter-stagiaire').style.display = 'none';
        document.getElementById('section-types').style.display = 'none';
        document.getElementById('section-conditions').style.display = '';
        document.getElementById('section-suivi').style.display = 'none';
    });
    document.getElementById('btn-suivi').addEventListener('click', function() {
        document.getElementById('section-stagiaires').style.display = 'none';
        document.getElementById('section-ajouter-stagiaire').style.display = 'none';
        document.getElementById('section-types').style.display = 'none';
        document.getElementById('section-conditions').style.display = 'none';
        document.getElementById('section-suivi').style.display = '';
        try {
            var typeLabels = JSON.parse(document.getElementById('data-type-labels').textContent);
            var stagesParType = JSON.parse(document.getElementById('data-stages-par-type').textContent);
            var ctxType = document.getElementById('chartTypeStage');
            if (ctxType) {
                if (window.chartTypeInstance) window.chartTypeInstance.destroy();
                window.chartTypeInstance = new Chart(ctxType.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: typeLabels,
                        datasets: [{
                            label: 'Nombre de stages',
                            data: stagesParType,
                            backgroundColor: 'rgba(54, 162, 235, 0.7)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: { legend: { display: false } },
                        scales: { y: { beginAtZero: true } }
                    }
                });
            }
            var statutLabels = JSON.parse(document.getElementById('data-statut-labels').textContent);
            var stagesParStatut = JSON.parse(document.getElementById('data-stages-par-statut').textContent);
            var ctxStatut = document.getElementById('chartStatutStage');
            if (ctxStatut) {
                if (window.chartStatutInstance) window.chartStatutInstance.destroy();
                window.chartStatutInstance = new Chart(ctxStatut.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: statutLabels,
                        datasets: [{
                            label: 'Nombre de stages',
                            data: stagesParStatut,
                            backgroundColor: [
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(153, 102, 255, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: { legend: { position: 'bottom' } },
                        cutout: '65%',
                        layout: { padding: 0 },
                    }
                });
            }
            var labelsMois = JSON.parse(document.getElementById('data-labels-mois').textContent);
            var stagesParMois = JSON.parse(document.getElementById('data-stages-par-mois').textContent);
            var ctxMois = document.getElementById('chartStagesParMois');
            if (ctxMois) {
                if (window.chartStagesParMois) window.chartStagesParMois.destroy();
                window.chartStagesParMois = new Chart(ctxMois.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: labelsMois,
                        datasets: [{
                            label: 'Stages/mois',
                            data: stagesParMois,
                            fill: true,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.15)',
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: { legend: { display: true } },
                        scales: { y: { beginAtZero: true } }
                    }
                });
            } else { console.warn('Canvas chartStagesParMois non trouvé'); }
            var competencesLabels = JSON.parse(document.getElementById('data-competences-labels').textContent);
            var competencesValues = JSON.parse(document.getElementById('data-competences-values').textContent);
            var ctxRadar = document.getElementById('chartRadarCompetences');
            if (ctxRadar) {
                if (window.chartRadarCompetences) window.chartRadarCompetences.destroy();
                window.chartRadarCompetences = new Chart(ctxRadar.getContext('2d'), {
                    type: 'radar',
                    data: {
                        labels: competencesLabels,
                        datasets: [{
                            label: 'Score',
                            data: competencesValues,
                            fill: true,
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            pointBackgroundColor: 'rgba(255, 99, 132, 1)'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: { legend: { display: true } },
                        scales: { r: { min: 0, max: 10, ticks: { stepSize: 2 } } }
                    }
                });
            } else { console.warn('Canvas chartRadarCompetences non trouvé'); }
        } catch (e) {
            console.error('Erreur lors de la génération des graphiques :', e);
        }
    });
    document.getElementById('btn-ajouter-stagiaire').addEventListener('click', function() {
        document.getElementById('section-stagiaires').style.display = 'none';
        document.getElementById('section-ajouter-stagiaire').style.display = '';
        document.getElementById('section-types').style.display = 'none';
        document.getElementById('section-conditions').style.display = 'none';
        document.getElementById('section-suivi').style.display = 'none';
    });
});
</script>
{% endblock %} 