{% extends "stages/base_stages.html" %}

{% block title %}Conditions d'Admission - Gestion des Stages{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="mb-4">Conditions d'Admission par Type de Stage</h1>

    <div class="accordion" id="accordionConditions">
        {% for type_stage in types_stage %}
        <div class="accordion-item">
            <h2 class="accordion-header" id="heading{{ type_stage.id_type_stage }}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ type_stage.id_type_stage }}" aria-expanded="false" aria-controls="collapse{{ type_stage.id_type_stage }}">
                    <strong>{{ type_stage.libelle }}</strong>
                </button>
            </h2>
            <div id="collapse{{ type_stage.id_type_stage }}" class="accordion-collapse collapse" aria-labelledby="heading{{ type_stage.id_type_stage }}" data-bs-parent="#accordionConditions">
                <div class="accordion-body">
                    <p><strong>Conditions d'admission :</strong></p>
                    <p>{{ type_stage.conditions_admission | safe if type_stage.conditions_admission else "Aucune condition spécifique." }}</p>
                    <hr>
                    <p><strong>Documents requis :</strong></p>
                    <p>{{ type_stage.documents_requis | safe if type_stage.documents_requis else "Aucun document spécifique requis." }}</p>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            Aucun type de stage n'a été trouvé. Veuillez en ajouter pour voir les conditions d'admission.
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %} 