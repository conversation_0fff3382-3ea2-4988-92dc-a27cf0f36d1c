{% extends "base.html" %}
{% block title %}Gestion du Courrier - Mo<PERSON>le Principal{% endblock %}
{% block content %}
<div class="container-fluid mt-4">


    <!-- Menu principal avec 2 boutons -->
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="row g-4">
                <!-- Bouton Courrier Arrivé -->
                <div class="col-md-6">
                    <div class="card shadow-lg h-100 courrier-card" onclick="window.location.href='{{ url_for('courrier_arrives') }}'">
                        <div class="card-body text-center p-5">
                            <div class="courrier-icon mb-4">
                                <i class="fas fa-inbox"></i>
                            </div>
                            <h3 class="card-title mb-3" style="color: #6B8E23;">Courriers Arrivés</h3>
                            <p class="card-text text-muted mb-4">
                                Gérer les courriers entrants, les distribuer aux divisions concernées et assurer leur traçabilité
                            </p>
                            <div class="d-flex justify-content-center gap-3 mb-3">
                                <span class="badge fs-6" style="background: #6B8E23; color: white;">
                                    <i class="fas fa-list me-1"></i>
                                    <span id="count-arrives-card">0</span> courriers
                                </span>
                                <span class="badge fs-6" style="background: #6B8E23; color: white;">
                                    <i class="fas fa-clock me-1"></i>
                                    <span id="count-attente-card">0</span> en attente
                                </span>
                            </div>
                            <button class="btn btn-lg" style="background: #6B8E23; color: white; border: none;">
                                <i class="fas fa-arrow-right me-2"></i>Accéder
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bouton Courrier Envoyé -->
                <div class="col-md-6">
                    <div class="card shadow-lg h-100 courrier-card" onclick="window.location.href='{{ url_for('courrier_envoyes') }}'">
                        <div class="card-body text-center p-5">
                            <div class="courrier-icon mb-4">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <h3 class="card-title mb-3" style="color: #6B8E23;">Courriers Envoyés</h3>
                            <p class="card-text text-muted mb-4">
                                Gérer les courriers sortants des divisions vers les organes externes et assurer leur suivi
                            </p>
                            <div class="d-flex justify-content-center gap-3 mb-3">
                                <span class="badge fs-6" style="background: #6B8E23; color: white;">
                                    <i class="fas fa-list me-1"></i>
                                    <span id="count-envoyes-card">0</span> courriers
                                </span>
                                <span class="badge fs-6" style="background: #6B8E23; color: white;">
                                    <i class="fas fa-calendar me-1"></i>
                                    Aujourd'hui: <span id="count-today">0</span>
                                </span>
                            </div>
                            <button class="btn btn-lg" style="background: #6B8E23; color: white; border: none;">
                                <i class="fas fa-arrow-right me-2"></i>Accéder
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



</div>


</div>

<style>
.bg-military {
    background: linear-gradient(90deg, #4b5320 60%, #8b7355 100%);
}

/* Cartes de courrier modernes */
.courrier-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    opacity: 1 !important;
    visibility: visible !important;
}

.courrier-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.courrier-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 10px 30px rgba(0,123,255,0.3);
}

.courrier-card:nth-child(2) .courrier-icon {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 10px 30px rgba(40,167,69,0.3);
}

.courrier-icon i {
    font-size: 3rem;
    color: white;
}

.courrier-card .card-title {
    font-weight: 700;
    font-size: 1.5rem;
}

.courrier-card .card-text {
    font-size: 1rem;
    line-height: 1.6;
}

.courrier-card .btn {
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.courrier-card .btn:hover {
    transform: scale(1.05);
}

/* Badges modernes */
.badge {
    font-size: 0.875em;
    padding: 8px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Animation d'entrée simplifiée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.courrier-card {
    opacity: 1;
    transform: translateY(0);
    animation: fadeInUp 0.4s ease-out;
}

.courrier-card:nth-child(2) {
    animation-delay: 0.1s;
}



/* Force la visibilité des cartes */
.courrier-card,
.courrier-card .card-body,
.courrier-icon {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Responsive */
@media (max-width: 768px) {
    .courrier-card .card-body {
        padding: 2rem !important;
    }

    .courrier-icon {
        width: 80px;
        height: 80px;
    }

    .courrier-icon i {
        font-size: 2rem;
    }
}
</style>

<script>
// Variables globales pour stocker les données
let courriersArrives = [];
let courriersEnvoyes = [];

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // S'assurer que les cartes sont visibles
    ensureCardsVisible();

    // Charger les données existantes (simulation)
    loadMockData();

    // Mettre à jour les compteurs
    updateCounters();


});

// S'assurer que les cartes restent visibles
function ensureCardsVisible() {
    const cards = document.querySelectorAll('.courrier-card');
    cards.forEach(card => {
        card.style.opacity = '1';
        card.style.visibility = 'visible';
        card.style.display = 'block';
    });

    // Vérification supplémentaire après un délai
    setTimeout(() => {
        cards.forEach(card => {
            if (card.style.opacity !== '1') {
                card.style.opacity = '1';
                card.style.visibility = 'visible';
            }
        });
    }, 100);
}

// Charger des données de test
function loadMockData() {
    courriersArrives = [
        {
            id: 'CA-001',
            urgence: 'urgent',
            date_arrivee: '2024-01-15',
            nature: 'message',
            numero_ecrit: 'MSG-2024-001',
            date_signature: '2024-01-14',
            expediteur: 'Commandement Régional Sud',
            objet: 'Demande de rapport mensuel des activités',
            divisions_action: ['instruction', 'technique'],
            divisions_info: ['rh'],
            classification: 'restreint',
            annotation: 'Traitement prioritaire demandé'
        },
        {
            id: 'CA-002',
            urgence: 'routine',
            date_arrivee: '2024-01-16',
            nature: 'nds',
            numero_ecrit: 'NDS-2024-002',
            date_signature: '2024-01-15',
            expediteur: 'État-Major Général',
            objet: 'Instructions pour la formation continue',
            divisions_action: ['instruction'],
            divisions_info: ['rh', 'technique'],
            classification: 'public',
            annotation: 'Diffusion large recommandée'
        }
    ];

    courriersEnvoyes = [
        {
            id: 'CE-001',
            numero_ecrit: 'DIV-2024-001',
            division_emettrice: 'instruction',
            date_depart: '2024-01-16',
            nature: 'nds',
            objet: 'Réponse au rapport mensuel',
            destinataire: 'Commandement Régional Sud'
        },
        {
            id: 'CE-002',
            numero_ecrit: 'TECH-2024-001',
            division_emettrice: 'technique',
            date_depart: '2024-01-17',
            nature: 'message',
            objet: 'État des véhicules en maintenance',
            destinataire: 'Direction Centrale du Matériel'
        }
    ];
}

// Mettre à jour les compteurs
function updateCounters() {
    const countArrives = courriersArrives.length;
    const countEnvoyes = courriersEnvoyes.length;
    const countAttente = courriersArrives.filter(c => c.divisions_action && c.divisions_action.length > 0).length;
    const countToday = courriersEnvoyes.filter(c => c.date_depart === new Date().toISOString().split('T')[0]).length;

    // Mettre à jour les compteurs du header
    document.getElementById('count-arrives').textContent = countArrives;
    document.getElementById('count-envoyes').textContent = countEnvoyes;
    document.getElementById('count-attente').textContent = countAttente;

    // Mettre à jour les compteurs des cartes
    document.getElementById('count-arrives-card').textContent = countArrives;
    document.getElementById('count-envoyes-card').textContent = countEnvoyes;
    document.getElementById('count-attente-card').textContent = countAttente;
    document.getElementById('count-today').textContent = countToday;
}







// Formater la date
function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}


</script>
{% endblock %}
