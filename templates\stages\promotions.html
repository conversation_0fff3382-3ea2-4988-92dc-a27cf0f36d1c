{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Gestion des Promotions</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-success me-2">
            <i class="fas fa-file-excel"></i> Exporter Excel
        </button>
        <button class="btn btn-danger me-2">
            <i class="fas fa-file-pdf"></i> Exporter PDF
        </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPromotionModal">
            <i class="fas fa-plus"></i> Nouvelle Promotion
        </button>
    </div>
</div>

<div class="row">
    <!-- Liste des promotions -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Liste des Promotions</h5>
                    </div>
                    <div class="col-auto">
                        <div class="input-group">
                            <select class="form-select" id="filterAnnee">
                                <option value="">Toutes les années</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                            <input type="text" class="form-control" placeholder="Rechercher...">
                            <button class="btn btn-outline-secondary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Année</th>
                                <th>Filière</th>
                                <th>Nombre de stagiaires</th>
                                <th>Date de début</th>
                                <th>Date de fin</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for promotion in promotions %}
                            <tr>
                                <td>{{ promotion.annee }}</td>
                                <td>{{ promotion.filiere }}</td>
                                <td>15</td>
                                <td>01/01/{{ promotion.annee }}</td>
                                <td>31/12/{{ promotion.annee }}</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" title="Voir détails">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajout Promotion -->
<div class="modal fade" id="addPromotionModal" tabindex="-1" aria-labelledby="addPromotionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPromotionModalLabel">Nouvelle Promotion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addPromotionForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="annee" class="form-label">Année</label>
                            <input type="number" class="form-control" id="annee" min="2020" max="2030" required>
                        </div>
                        <div class="col-md-6">
                            <label for="filiere" class="form-label">Filière</label>
                            <input type="text" class="form-control" id="filiere" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="dateDebut" class="form-label">Date de début</label>
                            <input type="date" class="form-control" id="dateDebut" required>
                        </div>
                        <div class="col-md-6">
                            <label for="dateFin" class="form-label">Date de fin</label>
                            <input type="date" class="form-control" id="dateFin" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="responsable" class="form-label">Responsable de promotion</label>
                        <input type="text" class="form-control" id="responsable">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion des filtres
    const filterAnnee = document.getElementById('filterAnnee');
    const searchInput = document.querySelector('input[type="text"]');

    filterAnnee.addEventListener('change', function() {
        // Implémenter la logique de filtrage ici
        console.log('Année sélectionnée:', this.value);
    });

    searchInput.addEventListener('keyup', function(e) {
        // Implémenter la logique de recherche ici
        console.log('Recherche:', e.target.value);
    });

    // Validation des dates
    const dateDebut = document.getElementById('dateDebut');
    const dateFin = document.getElementById('dateFin');

    dateFin.addEventListener('change', function() {
        if (dateDebut.value && this.value) {
            if (new Date(this.value) <= new Date(dateDebut.value)) {
                alert('La date de fin doit être postérieure à la date de début');
                this.value = '';
            }
        }
    });

    // Validation de l'année
    const anneeInput = document.getElementById('annee');
    anneeInput.addEventListener('input', function() {
        const annee = parseInt(this.value);
        if (annee < 2020 || annee > 2030) {
            alert('L\'année doit être comprise entre 2020 et 2030');
            this.value = '';
        }
    });
});
</script>
{% endblock %} 