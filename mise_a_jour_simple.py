"""
Script simple de mise à jour des libellés existants
Met à jour uniquement les libellés selon vos spécifications exactes
"""

from app import app
from db import db
from gestion_vehicules.rh.models import ReferentielUnite, ReferentielGrade

def mettre_a_jour_grades_existants():
    """Met à jour les libellés des grades existants selon spécifications"""
    try:
        with app.app_context():
            print("🎖️ Mise à jour des grades existants...")
            
            # Mapping des codes vers les nouveaux libellés exacts
            grades_mapping = {
                'SOL1': 'Soldat 1ère Classe',
                'SOL2': 'Soldat 2ème Classe', 
                'BRG': 'Brigadier',
                'BRGC': 'Brigadier Chef',
                'MDL': 'MDL',
                'MDLC': 'MDL Chef',
                'ADJ': 'Adjudant',
                'ADJC': 'Adjudant Chef',
                'SLT': 'Sous-Lieutenant',
                'LTN': 'Lieutenant',
                'CPT': 'Capitaine',
                'CDT': 'Commandant',
                'LCL': 'Lieutenant-Colonel',
                'COL': 'Colonel'
            }
            
            # Mettre à jour les grades existants
            for code, nouveau_libelle in grades_mapping.items():
                grade = ReferentielGrade.query.filter_by(code_grade=code).first()
                if grade:
                    grade.libelle = nouveau_libelle
                    print(f"   ✅ {code}: {nouveau_libelle}")
                else:
                    # Créer le grade s'il n'existe pas
                    niveau = list(grades_mapping.keys()).index(code) + 1
                    grade = ReferentielGrade(
                        code_grade=code,
                        libelle=nouveau_libelle,
                        niveau=niveau,
                        description=f"Grade: {nouveau_libelle}"
                    )
                    db.session.add(grade)
                    print(f"   ➕ {code}: {nouveau_libelle} (créé)")
            
            db.session.commit()
            print(f"✅ Grades mis à jour selon spécifications")
            
    except Exception as e:
        print(f"❌ Erreur grades: {str(e)}")
        db.session.rollback()

def mettre_a_jour_unites_existantes():
    """Met à jour les libellés des unités existantes selon spécifications"""
    try:
        with app.app_context():
            print("🏢 Mise à jour des unités existantes...")
            
            # Mapping des codes vers les nouveaux libellés exacts
            unites_mapping = {}
            
            # 1GAR au 26GAR (format court)
            for i in range(1, 27):
                unites_mapping[f'{i}GAR'] = f'{i}GAR'
            
            # Unités spécialisées selon spécifications exactes
            unites_mapping.update({
                'INSPART': 'Inspection de l\'Artillerie',
                'ERART': 'ERART',
                'GSA': 'GSA', 
                'CFA': 'CFA',
                '1BUR': '1er Bureau',
                '2BUR': '2ème Bureau',
                '3BUR': '3ème Bureau',
                '4BUR': '4ème Bureau',
                '5BUR': '5ème Bureau',
                'BREC': 'Bureau de Recrutement',
                'BCOUR': 'Bureau Courrier',
                'DPO': 'DPO',
                'PCA': 'PCA',
                'EMZS': 'État-Major Zone Sud',
                'EMZE': 'État-Major Zone Est',
                'SOPTAF': 'SOPTAF',
                'SOPSAG': 'SOPSAG',
                'SORIENT': 'S.ORIENTAL',
                'AUTRE': 'Autre'
            })
            
            # Mettre à jour les unités existantes
            for code, nouveau_libelle in unites_mapping.items():
                unite = ReferentielUnite.query.filter_by(code=code).first()
                if unite:
                    unite.libelle = nouveau_libelle
                    print(f"   ✅ {code}: {nouveau_libelle}")
                else:
                    # Créer l'unité si elle n'existe pas
                    if 'GAR' in code:
                        type_unite = 'Régiment'
                    elif 'BUR' in code or 'Bureau' in nouveau_libelle:
                        type_unite = 'Bureau'
                    elif code == 'INSPART':
                        type_unite = 'Inspection'
                    else:
                        type_unite = 'Autre'
                    
                    unite = ReferentielUnite(
                        code=code,
                        libelle=nouveau_libelle,
                        type_unite=type_unite
                    )
                    db.session.add(unite)
                    print(f"   ➕ {code}: {nouveau_libelle} (créé)")
            
            db.session.commit()
            print(f"✅ Unités mises à jour selon spécifications")
            
    except Exception as e:
        print(f"❌ Erreur unités: {str(e)}")
        db.session.rollback()

def afficher_resultats_finaux():
    """Affiche les résultats selon l'ordre demandé"""
    try:
        with app.app_context():
            print("\n📊 RÉSULTATS SELON VOS SPÉCIFICATIONS:")
            print("=" * 60)
            
            # Grades dans l'ordre exact demandé
            grades_ordre = ['SOL1', 'SOL2', 'BRG', 'BRGC', 'MDL', 'MDLC', 'ADJ', 'ADJC', 'SLT', 'LTN', 'CPT', 'CDT', 'LCL', 'COL']
            print("🎖️ GRADES (ordre exact demandé):")
            for i, code in enumerate(grades_ordre, 1):
                grade = ReferentielGrade.query.filter_by(code_grade=code).first()
                if grade:
                    print(f"   {i:2d}. {grade.libelle}")
                else:
                    print(f"   {i:2d}. {code} (non trouvé)")
            
            # Unités dans l'ordre exact demandé
            print("\n🏢 UNITÉS (ordre exact demandé):")
            
            # 1GAR au 26GAR
            print("   📍 GAR:")
            for i in range(1, 27):
                code = f'{i}GAR'
                unite = ReferentielUnite.query.filter_by(code=code).first()
                if unite:
                    print(f"      • {unite.libelle}")
            
            # Autres dans l'ordre exact demandé
            autres_ordre = ['INSPART', 'ERART', 'GSA', 'CFA', '1BUR', '2BUR', '3BUR', '4BUR', '5BUR', 
                           'BREC', 'BCOUR', 'DPO', 'PCA', 'EMZS', 'EMZE', 'SOPTAF', 'SOPSAG', 'SORIENT', 'AUTRE']
            print("   📍 Autres:")
            for code in autres_ordre:
                unite = ReferentielUnite.query.filter_by(code=code).first()
                if unite:
                    print(f"      • {unite.libelle}")
            
            # Statistiques
            total_grades = ReferentielGrade.query.count()
            total_unites = ReferentielUnite.query.count()
            from gestion_vehicules.rh.models import Personnel
            total_personnel = Personnel.query.count()
            
            print(f"\n📈 STATISTIQUES:")
            print(f"   • Grades: {total_grades}")
            print(f"   • Unités: {total_unites}")
            print(f"   • Personnel: {total_personnel}")
            
    except Exception as e:
        print(f"❌ Erreur affichage: {str(e)}")

def main():
    """Fonction principale"""
    print("🎯 MISE À JOUR SELON VOS SPÉCIFICATIONS EXACTES")
    print("=" * 60)
    print("📋 Application de:")
    print("   • 14 grades: Soldat 1ère classe → Colonel")
    print("   • Unités: 1GAR → 26GAR, Inspection de l'Artillerie, etc.")
    print("   • Format exact demandé")
    print("=" * 60)
    
    print("\n🚀 Mise à jour en cours...")
    
    # Mise à jour
    mettre_a_jour_grades_existants()
    mettre_a_jour_unites_existantes()
    
    # Résultats
    afficher_resultats_finaux()
    
    print("\n🎉 MISE À JOUR TERMINÉE!")
    print("🔗 Testez maintenant:")
    print("   • Recherche: http://localhost:3000/rh/recherche")
    print("   • Nouveau militaire: http://localhost:3000/rh/nouveau_militaire")
    print("\n✅ Vous verrez EXACTEMENT vos spécifications!")

if __name__ == "__main__":
    main()
