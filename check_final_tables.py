#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour vérifier l'état final des tables après correction
"""

import mysql.connector
from mysql.connector import <PERSON><PERSON>r

def check_final_tables():
    """Vérifier l'état final des tables"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='gestion_vehicules',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        print("🔍 ÉTAT FINAL DES TABLES APRÈS CORRECTION")
        print("=" * 60)
        print(f"📊 Total : {len(tables)} tables")
        print()
        
        # Séparer les tables par catégorie
        tables_rh = []
        tables_existantes = []
        
        for (table_name,) in tables:
            if any(keyword in table_name for keyword in ['referentiel_', 'personnel', 'conjoint', 'enfant', 'situation_medicale', 'vaccination', 'ptc', 'hospitalisation', 'permission', 'historique_grade', 'personnel_langue']):
                tables_rh.append(table_name)
            else:
                tables_existantes.append(table_name)
        
        print(f"🎯 TABLES RH ({len(tables_rh)}) :")
        for table in sorted(tables_rh):
            print(f"  ✅ {table}")
        
        print(f"\n📋 TABLES EXISTANTES ({len(tables_existantes)}) :")
        for table in sorted(tables_existantes):
            print(f"  - {table}")
        
        # Vérifier quelques données de test
        print(f"\n🔍 VÉRIFICATION DES DONNÉES :")
        
        verifications = [
            ("referentiel_genre", "SELECT COUNT(*) FROM referentiel_genre"),
            ("referentiel_grade", "SELECT COUNT(*) FROM referentiel_grade"),
            ("personnel", "SELECT COUNT(*) FROM personnel"),
            ("situation_medicale", "SELECT COUNT(*) FROM situation_medicale")
        ]
        
        for table, query in verifications:
            try:
                cursor.execute(query)
                count = cursor.fetchone()[0]
                print(f"  ✅ {table}: {count} enregistrements")
            except Error as e:
                print(f"  ❌ {table}: Erreur - {e}")
        
        cursor.close()
        connection.close()
        
        print(f"\n🎉 VÉRIFICATION TERMINÉE !")
        print(f"✅ {len(tables_rh)} tables RH opérationnelles")
        print(f"✅ {len(tables_existantes)} tables existantes préservées")
        print(f"✅ Total : {len(tables)} tables dans la base")
        
        return True
        
    except Error as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    check_final_tables()
