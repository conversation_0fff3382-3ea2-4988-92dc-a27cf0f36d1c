#!/usr/bin/env python3
"""
Test rapide pour vérifier que l'erreur de syntaxe JavaScript est corrigée
"""

import requests
import webbrowser
import time

BASE_URL = "http://127.0.0.1:3000"

def test_pages_loading():
    """Tester que les pages se chargent sans erreur de syntaxe"""
    print("🔍 Test de chargement des pages après correction...")
    
    pages = [
        ("/courrier/arrives", "Courriers Arrivés"),
        ("/courrier/envoyes", "Courriers Envoyés")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}")
            if response.status_code == 200:
                print(f"✅ {name}: Page accessible")
                
                # Vérifier qu'il n'y a pas de duplication dans le JavaScript
                content = response.text
                if "const divisionsAction" in content:
                    count = content.count("const divisionsAction")
                    if count > 1:
                        print(f"⚠️  {name}: {count} déclarations de 'const divisionsAction' trouvées")
                    else:
                        print(f"✅ {name}: Une seule déclaration de 'const divisionsAction'")
                
            else:
                print(f"❌ {name}: Erreur {response.status_code}")
                
        except Exception as e:
            print(f"❌ {name}: {e}")

def open_test_pages():
    """Ouvrir les pages pour test manuel"""
    print("\n🌐 Ouverture des pages pour test manuel...")
    
    pages = [
        f"{BASE_URL}/courrier/arrives",
        f"{BASE_URL}/courrier/envoyes"
    ]
    
    for url in pages:
        print(f"🔗 Ouverture: {url}")
        webbrowser.open(url)
        time.sleep(1)

def main():
    """Test principal"""
    print("🔧 TEST APRÈS CORRECTION DE L'ERREUR DE SYNTAXE")
    print("=" * 50)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print(f"❌ Application non accessible")
            return
    except:
        print(f"❌ Application non accessible")
        return
    
    print(f"✅ Application accessible")
    
    # Tester les pages
    test_pages_loading()
    
    # Ouvrir les pages
    open_test_pages()
    
    print("\n" + "=" * 50)
    print("🎯 INSTRUCTIONS DE VÉRIFICATION:")
    print("=" * 50)
    
    print("\n1. 📱 Les pages sont ouvertes dans votre navigateur")
    print("2. 🔍 Ouvrez la console JavaScript (F12)")
    print("3. 🔄 Rechargez la page (Ctrl+F5)")
    print("4. ✅ Vérifiez qu'il n'y a PLUS d'erreur rouge de syntaxe")
    print("5. 📊 Vous devriez voir les messages de débogage:")
    print("   • 🚀 Initialisation de la page...")
    print("   • ✅ Élément trouvé: tbody-courriers")
    print("   • 📡 Chargement des courriers depuis l'API...")
    print("   • ✅ Courriers chargés: X")
    print("   • 📊 Compteurs mis à jour...")
    print("   • ✅ Tableau rendu avec X courriers affichés")
    
    print("\n6. 🎯 RÉSULTAT ATTENDU:")
    print("   • ✅ Aucune erreur rouge dans la console")
    print("   • ✅ Les courriers s'affichent dans le tableau")
    print("   • ✅ Les compteurs sont mis à jour")
    
    print("\n🚨 Si vous voyez encore des problèmes:")
    print("   • Notez l'erreur exacte de la console")
    print("   • Vérifiez l'onglet Network pour les requêtes API")

if __name__ == "__main__":
    main()
