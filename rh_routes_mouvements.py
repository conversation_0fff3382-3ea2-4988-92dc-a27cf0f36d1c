#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes de gestion des mouvements pour le module RH
Implémentation des routes pour mutations, avancements, sanctions, libérations, séjours opérationnels
"""

from flask import request, redirect, url_for, flash, jsonify
from datetime import datetime, timedelta
from rh_models import *
from rh_blueprint import rh_bp

# ============================================================================
# ROUTES DE GESTION DES MOUVEMENTS
# ============================================================================

@rh_bp.route('/api/ajouter_mutation', methods=['POST'])
def api_ajouter_mutation():
    """API pour ajouter une mutation de fonction"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la mutation
        mutation = MutationFonction(
            matricule=data['matricule'],
            ancienne_fonction=data['ancienne_fonction'],
            nouvelle_fonction=data['nouvelle_fonction'],
            date_mutation=datetime.strptime(data['date_mutation'], '%Y-%m-%d').date(),
            motif=data.get('motif'),
            observations=data.get('observations')
        )
        
        db.session.add(mutation)
        
        # Mettre à jour la fonction actuelle du personnel
        personnel.fonction = data['nouvelle_fonction']
        personnel.date_prise_fonction = mutation.date_mutation
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Mutation ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_avancement', methods=['POST'])
def api_ajouter_avancement():
    """API pour ajouter un avancement"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer l'avancement
        avancement = Avancement(
            matricule=data['matricule'],
            ancien_grade_id=data['ancien_grade_id'],
            nouveau_grade_id=data['nouveau_grade_id'],
            date_avancement=datetime.strptime(data['date_avancement'], '%Y-%m-%d').date(),
            motif=data.get('motif'),
            observations=data.get('observations')
        )
        
        db.session.add(avancement)
        
        # Mettre à jour le grade actuel du personnel
        personnel.grade_actuel_id = data['nouveau_grade_id']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Avancement ajouté avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_sanction', methods=['POST'])
def api_ajouter_sanction():
    """API pour ajouter une sanction"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la sanction
        sanction = Sanction(
            matricule=data['matricule'],
            type_sanction=data['type_sanction'],
            date_sanction=datetime.strptime(data['date_sanction'], '%Y-%m-%d').date(),
            motif=data['motif'],
            duree_jours=data.get('duree_jours'),
            observations=data.get('observations')
        )
        
        db.session.add(sanction)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Sanction ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_liberation', methods=['POST'])
def api_ajouter_liberation():
    """API pour ajouter une libération"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer la libération
        liberation = Liberation(
            matricule=data['matricule'],
            date_liberation=datetime.strptime(data['date_liberation'], '%Y-%m-%d').date(),
            motif=data['motif'],
            observations=data.get('observations')
        )
        
        db.session.add(liberation)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Libération ajoutée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_sejour_ops', methods=['POST'])
def api_ajouter_sejour_ops():
    """API pour ajouter un séjour opérationnel"""
    try:
        data = request.get_json()
        
        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404
        
        # Créer le séjour opérationnel
        sejour = SejourOps(
            matricule=data['matricule'],
            date_debut=datetime.strptime(data['date_debut'], '%Y-%m-%d').date(),
            date_fin=datetime.strptime(data['date_fin'], '%Y-%m-%d').date(),
            lieu_mission=data['lieu_mission'],
            pays=data['pays'],
            type_mission=data['type_mission'],
            observations=data.get('observations')
        )
        
        db.session.add(sejour)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Séjour opérationnel ajouté avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/modifier_mutation/<int:id_mutation>', methods=['PUT'])
def api_modifier_mutation(id_mutation):
    """API pour modifier une mutation"""
    try:
        mutation = MutationFonction.query.get_or_404(id_mutation)
        data = request.get_json()
        
        # Mise à jour des champs
        if 'ancienne_fonction' in data:
            mutation.ancienne_fonction = data['ancienne_fonction']
        if 'nouvelle_fonction' in data:
            mutation.nouvelle_fonction = data['nouvelle_fonction']
        if 'date_mutation' in data:
            mutation.date_mutation = datetime.strptime(data['date_mutation'], '%Y-%m-%d').date()
        if 'motif' in data:
            mutation.motif = data['motif']
        if 'observations' in data:
            mutation.observations = data['observations']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Mutation modifiée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_sanction/<int:id_sanction>', methods=['DELETE'])
def api_supprimer_sanction(id_sanction):
    """API pour supprimer une sanction"""
    try:
        sanction = Sanction.query.get_or_404(id_sanction)
        
        db.session.delete(sanction)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Sanction supprimée avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/mouvements', methods=['GET'])
def api_get_mouvements_personnel(matricule):
    """API pour récupérer tous les mouvements d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # Mutations
        mutations_data = []
        for mutation in personnel.mutations:
            mutations_data.append({
                'id_mutation': mutation.id_mutation,
                'ancienne_fonction': mutation.ancienne_fonction,
                'nouvelle_fonction': mutation.nouvelle_fonction,
                'date_mutation': mutation.date_mutation.strftime('%Y-%m-%d'),
                'motif': mutation.motif,
                'observations': mutation.observations
            })
        
        # Avancements
        avancements_data = []
        for avancement in personnel.avancements:
            avancements_data.append({
                'id_avancement': avancement.id_avancement,
                'ancien_grade': avancement.ancien_grade.libelle,
                'nouveau_grade': avancement.nouveau_grade.libelle,
                'date_avancement': avancement.date_avancement.strftime('%Y-%m-%d'),
                'motif': avancement.motif,
                'observations': avancement.observations
            })
        
        # Sanctions
        sanctions_data = []
        for sanction in personnel.sanctions:
            sanctions_data.append({
                'id_sanction': sanction.id_sanction,
                'type_sanction': sanction.type_sanction,
                'date_sanction': sanction.date_sanction.strftime('%Y-%m-%d'),
                'motif': sanction.motif,
                'duree_jours': sanction.duree_jours,
                'observations': sanction.observations
            })
        
        # Libérations
        liberations_data = []
        for liberation in personnel.liberations:
            liberations_data.append({
                'id_liberation': liberation.id_liberation,
                'date_liberation': liberation.date_liberation.strftime('%Y-%m-%d'),
                'motif': liberation.motif,
                'observations': liberation.observations
            })
        
        # Séjours opérationnels
        sejours_data = []
        for sejour in personnel.sejours_ops:
            sejours_data.append({
                'id_sejour': sejour.id_sejour,
                'date_debut': sejour.date_debut.strftime('%Y-%m-%d'),
                'date_fin': sejour.date_fin.strftime('%Y-%m-%d'),
                'lieu_mission': sejour.lieu_mission,
                'pays': sejour.pays,
                'type_mission': sejour.type_mission,
                'observations': sejour.observations,
                'duree_jours': (sejour.date_fin - sejour.date_debut).days + 1,
                'en_cours': sejour.date_debut <= datetime.now().date() <= sejour.date_fin
            })
        
        # Trier par date (plus récent en premier)
        mutations_data.sort(key=lambda x: x['date_mutation'], reverse=True)
        avancements_data.sort(key=lambda x: x['date_avancement'], reverse=True)
        sanctions_data.sort(key=lambda x: x['date_sanction'], reverse=True)
        liberations_data.sort(key=lambda x: x['date_liberation'], reverse=True)
        sejours_data.sort(key=lambda x: x['date_debut'], reverse=True)
        
        # Statistiques
        sejours_en_cours = len([s for s in sejours_data if s['en_cours']])
        
        return jsonify({
            'success': True,
            'mouvements': {
                'mutations': mutations_data,
                'avancements': avancements_data,
                'sanctions': sanctions_data,
                'liberations': liberations_data,
                'sejours_ops': sejours_data,
                'statistiques': {
                    'nb_mutations': len(mutations_data),
                    'nb_avancements': len(avancements_data),
                    'nb_sanctions': len(sanctions_data),
                    'nb_liberations': len(liberations_data),
                    'nb_sejours_ops': len(sejours_data),
                    'sejours_en_cours': sejours_en_cours
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/sejours_en_cours', methods=['GET'])
def api_sejours_en_cours():
    """API pour récupérer les séjours opérationnels en cours"""
    try:
        today = datetime.now().date()
        
        sejours_en_cours = SejourOps.query.filter(
            SejourOps.date_debut <= today,
            SejourOps.date_fin >= today
        ).all()
        
        sejours_data = []
        for sejour in sejours_en_cours:
            sejours_data.append({
                'id_sejour': sejour.id_sejour,
                'personnel': {
                    'matricule': sejour.matricule,
                    'nom_complet': sejour.personnel.nom_complet,
                    'unite': sejour.personnel.unite.libelle,
                    'grade': sejour.personnel.grade_actuel.libelle
                },
                'date_debut': sejour.date_debut.strftime('%d/%m/%Y'),
                'date_fin': sejour.date_fin.strftime('%d/%m/%Y'),
                'lieu_mission': sejour.lieu_mission,
                'pays': sejour.pays,
                'type_mission': sejour.type_mission,
                'jours_restants': (sejour.date_fin - today).days,
                'duree_totale': (sejour.date_fin - sejour.date_debut).days + 1
            })
        
        # Grouper par pays
        par_pays = {}
        for sejour in sejours_data:
            pays = sejour['pays']
            if pays not in par_pays:
                par_pays[pays] = []
            par_pays[pays].append(sejour)
        
        return jsonify({
            'success': True,
            'sejours_en_cours': {
                'liste': sejours_data,
                'par_pays': par_pays,
                'total': len(sejours_data),
                'pays_actifs': list(par_pays.keys())
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/statistiques_mouvements', methods=['GET'])
def api_statistiques_mouvements():
    """API pour récupérer les statistiques des mouvements"""
    try:
        # Statistiques générales
        total_mutations = MutationFonction.query.count()
        total_avancements = Avancement.query.count()
        total_sanctions = Sanction.query.count()
        total_liberations = Liberation.query.count()
        total_sejours = SejourOps.query.count()
        
        # Mouvements récents (derniers 30 jours)
        date_limite = datetime.now().date() - timedelta(days=30)
        
        mutations_recentes = MutationFonction.query.filter(
            MutationFonction.date_mutation >= date_limite
        ).count()
        
        avancements_recents = Avancement.query.filter(
            Avancement.date_avancement >= date_limite
        ).count()
        
        sanctions_recentes = Sanction.query.filter(
            Sanction.date_sanction >= date_limite
        ).count()
        
        # Statistiques par type de sanction
        stats_sanctions = db.session.query(
            Sanction.type_sanction,
            db.func.count(Sanction.id_sanction).label('effectif')
        ).group_by(Sanction.type_sanction).order_by(
            db.func.count(Sanction.id_sanction).desc()
        ).all()
        
        # Statistiques par grade pour les avancements
        stats_avancements_grade = db.session.query(
            ReferentielGrade.libelle,
            db.func.count(Avancement.id_avancement).label('effectif')
        ).join(Avancement, ReferentielGrade.id_grade == Avancement.nouveau_grade_id).group_by(
            ReferentielGrade.libelle
        ).order_by(db.func.count(Avancement.id_avancement).desc()).all()
        
        # Personnel avec le plus de mouvements
        personnel_max_mouvements = db.session.query(
            Personnel.matricule,
            Personnel.nom,
            Personnel.prenom,
            (db.func.count(MutationFonction.id_mutation) + 
             db.func.count(Avancement.id_avancement) + 
             db.func.count(Sanction.id_sanction) + 
             db.func.count(SejourOps.id_sejour)).label('total_mouvements')
        ).outerjoin(MutationFonction).outerjoin(Avancement).outerjoin(Sanction).outerjoin(SejourOps).group_by(
            Personnel.matricule, Personnel.nom, Personnel.prenom
        ).order_by(db.text('total_mouvements DESC')).limit(10).all()
        
        # Séjours opérationnels par pays
        stats_sejours_pays = db.session.query(
            SejourOps.pays,
            db.func.count(SejourOps.id_sejour).label('effectif')
        ).group_by(SejourOps.pays).order_by(
            db.func.count(SejourOps.id_sejour).desc()
        ).all()
        
        return jsonify({
            'success': True,
            'statistiques': {
                'totaux': {
                    'mutations': total_mutations,
                    'avancements': total_avancements,
                    'sanctions': total_sanctions,
                    'liberations': total_liberations,
                    'sejours_ops': total_sejours,
                    'total_mouvements': total_mutations + total_avancements + total_sanctions + total_sejours
                },
                'recents_30j': {
                    'mutations': mutations_recentes,
                    'avancements': avancements_recents,
                    'sanctions': sanctions_recentes,
                    'total': mutations_recentes + avancements_recents + sanctions_recentes
                },
                'sanctions_par_type': [{'type': s[0], 'effectif': s[1]} for s in stats_sanctions],
                'avancements_par_grade': [{'grade': s[0], 'effectif': s[1]} for s in stats_avancements_grade],
                'sejours_par_pays': [{'pays': s[0], 'effectif': s[1]} for s in stats_sejours_pays],
                'personnel_max_mouvements': [
                    {
                        'matricule': p[0],
                        'nom_complet': f"{p[2]} {p[1]}",
                        'total_mouvements': p[3]
                    } for p in personnel_max_mouvements if p[3] > 0
                ]
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/avancements_par_periode', methods=['POST'])
def api_avancements_par_periode():
    """API pour récupérer les avancements par période"""
    try:
        data = request.get_json()
        date_debut = datetime.strptime(data['date_debut'], '%Y-%m-%d').date()
        date_fin = datetime.strptime(data['date_fin'], '%Y-%m-%d').date()
        
        avancements = Avancement.query.filter(
            Avancement.date_avancement >= date_debut,
            Avancement.date_avancement <= date_fin
        ).all()
        
        avancements_data = []
        for avancement in avancements:
            avancements_data.append({
                'id_avancement': avancement.id_avancement,
                'personnel': {
                    'matricule': avancement.matricule,
                    'nom_complet': avancement.personnel.nom_complet,
                    'unite': avancement.personnel.unite.libelle
                },
                'ancien_grade': avancement.ancien_grade.libelle,
                'nouveau_grade': avancement.nouveau_grade.libelle,
                'date_avancement': avancement.date_avancement.strftime('%d/%m/%Y'),
                'motif': avancement.motif
            })
        
        # Grouper par grade
        par_grade = {}
        for avancement in avancements_data:
            grade = avancement['nouveau_grade']
            if grade not in par_grade:
                par_grade[grade] = []
            par_grade[grade].append(avancement)
        
        return jsonify({
            'success': True,
            'avancements': {
                'liste': avancements_data,
                'par_grade': par_grade,
                'total': len(avancements_data),
                'periode': {
                    'debut': date_debut.strftime('%d/%m/%Y'),
                    'fin': date_fin.strftime('%d/%m/%Y')
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
