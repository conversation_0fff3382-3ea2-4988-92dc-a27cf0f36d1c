/* Styles pour la barre latérale des détails - Design moderne et sophistiqué */
.details-sidebar {
    position: fixed;
    top: 50%;
    right: -450px;
    width: 450px;
    height: 90vh;
    max-height: 700px;
    transform: translateY(-50%);
    background-color: #f8f9fa;
    box-shadow: -5px 0 25px rgba(0,0,0,0.15);
    z-index: 1050;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
    border-left: 3px solid #198754;
    border-radius: 12px 0 0 12px;
}

.details-sidebar.show {
    right: 0;
}

.details-sidebar-header {
    background: linear-gradient(45deg, #198754, #20c997);
    position: relative;
    color: white;
    padding: 18px 20px;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px 0 0 0;
    overflow: hidden;
}

.details-sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(45deg,
                      rgba(255, 255, 255, 0.25),
                      rgba(255, 255, 255, 0.25) 15px,
                      transparent 15px,
                      transparent 30px);
    z-index: 0;
}

.details-sidebar-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.details-sidebar-header h5 i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.details-sidebar-body {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
    background-color: white;
    border-radius: 15px 15px 0 0;
    margin-top: -15px;
    box-shadow: 0 -4px 10px rgba(0,0,0,0.05);
}

.details-sidebar-close {
    position: relative;
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.details-sidebar-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.details-sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(3px);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.details-sidebar-backdrop.show {
    opacity: 1;
    visibility: visible;
}

/* Cartes modernes pour les détails */
.details-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 15px rgba(0,0,0,0.05);
    margin-bottom: 20px;
    border: none;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: white;
}

.details-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.08);
}

.details-card-header {
    background: linear-gradient(45deg, #198754, #20c997);
    position: relative;
    padding: 15px 18px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: white;
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    overflow: hidden;
}

.details-card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(45deg,
                      rgba(255, 255, 255, 0.25),
                      rgba(255, 255, 255, 0.25) 15px,
                      transparent 15px,
                      transparent 30px);
    z-index: 0;
}

.details-card-header i {
    margin-right: 10px;
    color: white;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
}

.details-card-body {
    padding: 18px;
}

/* Tableau d'historique moderne et compact */
.history-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 8px;
    margin-top: -8px;
    font-size: 0.9rem;
}

.history-table thead th {
    background: linear-gradient(45deg, #198754, #20c997);
    position: relative;
    padding: 10px 12px;
    font-weight: 600;
    color: white;
    border: none;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    overflow: hidden;
}

.history-table thead th::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(45deg,
                      rgba(255, 255, 255, 0.25),
                      rgba(255, 255, 255, 0.25) 15px,
                      transparent 15px,
                      transparent 30px);
    z-index: 0;
}

.history-table tbody tr {
    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
    border-radius: 8px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    background-color: white;
}

.history-table tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.08);
}

.history-table tbody td {
    padding: 10px 12px;
    border: none;
    vertical-align: middle;
}

.history-table tbody td:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.history-table tbody td:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* Badges compacts et modernes */
.compact-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.compact-badge i {
    margin-right: 4px;
    font-size: 0.85rem;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-item {
    animation: slideIn 0.3s ease-out forwards;
    opacity: 0;
}

.animate-fade {
    animation: fadeIn 0.4s ease-out forwards;
    opacity: 0;
}

.animate-scale {
    animation: scaleIn 0.3s ease-out forwards;
    opacity: 0;
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }
