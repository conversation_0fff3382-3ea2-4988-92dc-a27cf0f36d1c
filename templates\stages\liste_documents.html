{% extends "stages/base_stages.html" %}

{% block title %}Documents du Stagiaire{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Documents du Stagiaire</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('ajouter_document', stagiaire_id=stagiaire.id_stagiaire) }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-plus-circle me-1"></i>
                Ajouter un Document
            </a>
        </div>
    </div>
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Type</th>
                            <th>Fichier</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for doc in documents %}
                        <tr>
                            <td>{{ doc.type_doc }}</td>
                            <td>{{ doc.chemin_fichier.split('/')[-1] }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('telecharger_document', doc_id=doc.id_document) }}" class="btn btn-success btn-sm" title="Télécharger">
                                    <i class="fas fa-download"></i>
                                </a>
                                <form action="{{ url_for('supprimer_document', doc_id=doc.id_document) }}" method="post" style="display:inline;" onsubmit="return confirm('Supprimer ce document ?');">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="3" class="text-center">Aucun document trouvé.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 