#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Blueprint RH pour l'application de gestion militaire
Routes principales pour la gestion des ressources humaines
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from datetime import datetime, date
from rh_models import *
import json

# Création du blueprint RH
rh_bp = Blueprint('rh', __name__, url_prefix='/rh')

# Importer les routes spécialisées
from rh_routes_personnel import *
from rh_routes_famille import *
from rh_routes_medical import *
from rh_routes_absences import *
from rh_routes_mouvements import *

# ============================================================================
# ROUTES PRINCIPALES
# ============================================================================

@rh_bp.route('/')
@rh_bp.route('/dashboard')
def dashboard():
    """Dashboard principal RH avec statistiques"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        total_officiers = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier'
        ).count()
        total_officiers_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Officier du rang'
        ).count()
        total_militaires_rang = Personnel.query.join(ReferentielCategorie).filter(
            ReferentielCategorie.libelle == 'Militaire du rang'
        ).count()
        
        # Statistiques par arme
        stats_armes = db.session.query(
            ReferentielArme.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielArme.libelle).all()
        
        # Statistiques par unité (top 10)
        stats_unites = db.session.query(
            ReferentielUnite.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielUnite.libelle).order_by(
            db.func.count(Personnel.matricule).desc()
        ).limit(10).all()
        
        # Personnel récemment engagé (30 derniers jours)
        from datetime import date, timedelta
        date_limite = date.today() - timedelta(days=30)
        nouveaux_engages = Personnel.query.filter(
            Personnel.date_engagement >= date_limite
        ).count()
        
        # Permissions en cours
        permissions_en_cours = Permission.query.filter(
            Permission.date_debut <= date.today(),
            Permission.date_fin >= date.today()
        ).count()
        
        # PTC en cours
        ptc_en_cours = Ptc.query.filter(
            Ptc.date_debut <= date.today(),
            Ptc.date_fin >= date.today()
        ).count()
        
        # Détachements en cours
        detachements_en_cours = Detachement.query.filter(
            Detachement.date_debut <= date.today(),
            Detachement.date_fin >= date.today()
        ).count()

        # Statistiques médicales (pour éviter les erreurs de template)
        try:
            total_aptes = SituationMedicale.query.filter(
                SituationMedicale.aptitude_service == 'Apte'
            ).count()
        except:
            total_aptes = 0

        try:
            total_inaptes = SituationMedicale.query.filter(
                SituationMedicale.aptitude_service == 'Inapte'
            ).count()
        except:
            total_inaptes = 0

        return render_template('RH/dashboard.html',
                             total_personnel=total_personnel,
                             total_officiers=total_officiers,
                             total_officiers_rang=total_officiers_rang,
                             total_militaires_rang=total_militaires_rang,
                             stats_armes=stats_armes,
                             stats_unites=stats_unites,
                             nouveaux_engages=nouveaux_engages,
                             permissions_en_cours=permissions_en_cours,
                             ptc_en_cours=ptc_en_cours,
                             detachements_en_cours=detachements_en_cours,
                             total_aptes=total_aptes,
                             total_inaptes=total_inaptes)
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('RH/dashboard.html',
                             total_personnel=0,
                             total_officiers=0,
                             total_officiers_rang=0,
                             total_militaires_rang=0,
                             stats_armes=[],
                             stats_unites=[],
                             nouveaux_engages=0,
                             permissions_en_cours=0,
                             ptc_en_cours=0,
                             detachements_en_cours=0,
                             total_aptes=0,
                             total_inaptes=0)

@rh_bp.route('/recherche')
def recherche_personnel():
    """Page de recherche du personnel"""
    try:
        # Récupérer les données pour les filtres
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        categories = ReferentielCategorie.query.all()
        
        return render_template('RH/recherche_personnel.html',
                             armes=armes,
                             unites=unites,
                             grades=grades,
                             categories=categories)
    except Exception as e:
        flash(f'Erreur lors du chargement de la page de recherche: {str(e)}', 'error')
        return render_template('RH/recherche_personnel.html')

@rh_bp.route('/api/recherche', methods=['POST'])
def api_recherche_personnel():
    """API de recherche du personnel avec filtres"""
    try:
        data = request.get_json()
        
        # Construction de la requête de base
        query = Personnel.query
        
        # Filtres
        if data.get('matricule'):
            query = query.filter(Personnel.matricule.like(f"%{data['matricule']}%"))
        
        if data.get('nom'):
            query = query.filter(Personnel.nom.like(f"%{data['nom']}%"))
        
        if data.get('prenom'):
            query = query.filter(Personnel.prenom.like(f"%{data['prenom']}%"))
        
        if data.get('arme_id'):
            query = query.filter(Personnel.arme_id == data['arme_id'])
        
        if data.get('unite_id'):
            query = query.filter(Personnel.unite_id == data['unite_id'])
        
        if data.get('grade_id'):
            query = query.filter(Personnel.grade_actuel_id == data['grade_id'])
        
        if data.get('categorie_id'):
            query = query.filter(Personnel.categorie_id == data['categorie_id'])
        
        # Exécution de la requête
        resultats = query.limit(100).all()
        
        # Formatage des résultats
        personnel_list = []
        for p in resultats:
            personnel_list.append({
                'matricule': p.matricule,
                'nom_complet': p.nom_complet,
                'nom_complet_arabe': p.nom_complet_arabe,
                'arme': p.arme.libelle,
                'unite': p.unite.libelle,
                'grade': p.grade_actuel.libelle,
                'fonction': p.fonction,
                'date_engagement': p.date_engagement.strftime('%d/%m/%Y')
            })
        
        return jsonify({
            'success': True,
            'personnel': personnel_list,
            'total': len(personnel_list)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/nouveau_militaire')
def nouveau_militaire():
    """Page d'ajout d'un nouveau militaire"""
    try:
        # Récupérer toutes les données de référence
        genres = ReferentielGenre.query.all()
        categories = ReferentielCategorie.query.all()
        groupes_sanguins = ReferentielGroupeSanguin.query.all()
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        situations_familiales = ReferentielSituationFamiliale.query.all()
        degres_parente = ReferentielDegreParente.query.all()
        langues = ReferentielLangue.query.all()
        
        return render_template('RH/nouveau_militaire.html',
                             genres=genres,
                             categories=categories,
                             groupes_sanguins=groupes_sanguins,
                             armes=armes,
                             unites=unites,
                             grades=grades,
                             situations_familiales=situations_familiales,
                             degres_parente=degres_parente,
                             langues=langues)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return render_template('RH/nouveau_militaire.html')

@rh_bp.route('/api/specialites/<int:arme_id>')
def api_specialites_par_arme(arme_id):
    """API pour récupérer les spécialités d'une arme"""
    try:
        specialites = ReferentielSpecialite.query.filter_by(id_arme=arme_id).all()
        specialites_list = [{'id': s.id_specialite, 'libelle': s.libelle} for s in specialites]
        return jsonify({
            'success': True,
            'specialites': specialites_list
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/personnel/<matricule>')
def fiche_personnel(matricule):
    """Affichage de la fiche complète d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        return render_template('RH/fiche_personnel_complete.html', personnel=personnel)
    except Exception as e:
        flash(f'Erreur lors du chargement de la fiche personnel: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# ============================================================================
# ROUTES DE GESTION
# ============================================================================

@rh_bp.route('/gestion_famille/<matricule>')
def gestion_famille(matricule):
    """Page de gestion familiale unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        genres = ReferentielGenre.query.all()
        return render_template('RH/gestion_famille_unifie.html', 
                             personnel=personnel, 
                             genres=genres)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion familiale: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_medical/<matricule>')
def gestion_medical(matricule):
    """Page de gestion médicale unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        return render_template('RH/gestion_medical_unifie.html', personnel=personnel)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion médicale: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_absences/<matricule>')
def gestion_absences(matricule):
    """Page de gestion des absences unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        unites = ReferentielUnite.query.all()
        return render_template('RH/gestion_absences_unifie.html', 
                             personnel=personnel,
                             unites=unites)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion des absences: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

@rh_bp.route('/gestion_mouvements/<matricule>')
def gestion_mouvements(matricule):
    """Page de gestion des mouvements unifiée"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        armes = ReferentielArme.query.all()
        unites = ReferentielUnite.query.all()
        grades = ReferentielGrade.query.all()
        return render_template('RH/gestion_mouvements_unifie.html',
                             personnel=personnel,
                             armes=armes,
                             unites=unites,
                             grades=grades)
    except Exception as e:
        flash(f'Erreur lors du chargement de la gestion des mouvements: {str(e)}', 'error')
        return redirect(url_for('rh.recherche_personnel'))

# ============================================================================
# ROUTES API POUR LES FORMULAIRES
# ============================================================================

@rh_bp.route('/api/ajouter_personnel', methods=['POST'])
def api_ajouter_personnel():
    """API pour ajouter un nouveau personnel"""
    try:
        data = request.get_json()

        # Vérifier si le matricule existe déjà
        if Personnel.query.get(data['matricule']):
            return jsonify({
                'success': False,
                'error': 'Ce matricule existe déjà'
            }), 400

        # Créer le nouveau personnel
        personnel = Personnel(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            nom_arabe=data['nom_arabe'],
            prenom_arabe=data['prenom_arabe'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            sexe_id=data['sexe_id'],
            categorie_id=data['categorie_id'],
            groupe_sanguin_id=data['groupe_sanguin_id'],
            numero_cin=data['numero_cin'],
            date_delivrance_cin=datetime.strptime(data['date_delivrance_cin'], '%Y-%m-%d').date(),
            date_expiration_cin=datetime.strptime(data['date_expiration_cin'], '%Y-%m-%d').date(),
            gsm=data['gsm'],
            telephone_domicile=data.get('telephone_domicile'),
            taille=float(data['taille']),
            lieu_residence=data['lieu_residence'],
            arme_id=data['arme_id'],
            specialite_id=data.get('specialite_id'),
            unite_id=data['unite_id'],
            grade_actuel_id=data['grade_actuel_id'],
            fonction=data['fonction'],
            date_prise_fonction=datetime.strptime(data['date_prise_fonction'], '%Y-%m-%d').date(),
            ccp=data['ccp'],
            compte_bancaire=data.get('compte_bancaire'),
            numero_somme=data['numero_somme'],
            date_engagement=datetime.strptime(data['date_engagement'], '%Y-%m-%d').date(),
            nom_pere=data['nom_pere'],
            prenom_pere=data['prenom_pere'],
            nom_mere=data['nom_mere'],
            prenom_mere=data['prenom_mere'],
            adresse_parents=data['adresse_parents'],
            situation_fam_id=data['situation_fam_id'],
            nombre_enfants=data.get('nombre_enfants'),
            numero_passport=data.get('numero_passport'),
            date_delivrance_passport=datetime.strptime(data['date_delivrance_passport'], '%Y-%m-%d').date() if data.get('date_delivrance_passport') else None,
            date_expiration_passport=datetime.strptime(data['date_expiration_passport'], '%Y-%m-%d').date() if data.get('date_expiration_passport') else None,
            gsm_urgence=data['gsm_urgence'],
            degre_parente_id=data['degre_parente_id']
        )

        db.session.add(personnel)

        # Ajouter les langues
        if data.get('langues'):
            for langue_id in data['langues']:
                personnel_langue = PersonnelLangue(
                    matricule=data['matricule'],
                    langue_id=langue_id
                )
                db.session.add(personnel_langue)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Personnel ajouté avec succès',
            'matricule': data['matricule']
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_conjoint', methods=['POST'])
def api_ajouter_conjoint():
    """API pour ajouter un conjoint"""
    try:
        data = request.get_json()

        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404

        # Vérifier si un conjoint existe déjà
        if personnel.conjoint:
            return jsonify({
                'success': False,
                'error': 'Un conjoint existe déjà pour ce personnel'
            }), 400

        # Créer le conjoint
        conjoint = Conjoint(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            nom_arabe=data['nom_arabe'],
            prenom_arabe=data['prenom_arabe'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            lieu_naissance_arabe=data['lieu_naissance_arabe'],
            adresse=data['adresse'],
            adresse_arabe=data['adresse_arabe'],
            date_mariage=datetime.strptime(data['date_mariage'], '%Y-%m-%d').date(),
            lieu_mariage=data['lieu_mariage'],
            profession=data['profession'],
            profession_arabe=data['profession_arabe'],
            numero_cin=data['numero_cin'],
            gsm=data['gsm'],
            nom_pere=data['nom_pere'],
            prenom_pere=data['prenom_pere'],
            nom_arabe_pere=data['nom_arabe_pere'],
            prenom_arabe_pere=data['prenom_arabe_pere'],
            nom_mere=data['nom_mere'],
            prenom_mere=data['prenom_mere'],
            nom_arabe_mere=data['nom_arabe_mere'],
            prenom_arabe_mere=data['prenom_arabe_mere'],
            profession_pere=data['profession_pere'],
            profession_mere=data['profession_mere']
        )

        db.session.add(conjoint)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Conjoint ajouté avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/ajouter_enfant', methods=['POST'])
def api_ajouter_enfant():
    """API pour ajouter un enfant"""
    try:
        data = request.get_json()

        # Vérifier si le personnel existe
        personnel = Personnel.query.get(data['matricule'])
        if not personnel:
            return jsonify({
                'success': False,
                'error': 'Personnel non trouvé'
            }), 404

        # Créer l'enfant
        enfant = Enfant(
            matricule=data['matricule'],
            nom=data['nom'],
            prenom=data['prenom'],
            sexe_id=data['sexe_id'],
            date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date(),
            lieu_naissance=data['lieu_naissance'],
            date_deces=datetime.strptime(data['date_deces'], '%Y-%m-%d').date() if data.get('date_deces') else None
        )

        db.session.add(enfant)

        # Mettre à jour le nombre d'enfants du personnel
        personnel.nombre_enfants = len(personnel.enfants) + 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Enfant ajouté avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# ============================================================================
# IMPORT DES ROUTES SPÉCIALISÉES
# ============================================================================

# Importer les routes spécialisées pour les ajouter au blueprint
try:
    from rh_routes_personnel import *
    from rh_routes_famille import *
    from rh_routes_medical import *
    from rh_routes_absences import *
    from rh_routes_mouvements import *
    print("✅ Routes spécialisées RH importées avec succès")
except ImportError as e:
    print(f"⚠️ Erreur lors de l'import des routes spécialisées: {e}")
