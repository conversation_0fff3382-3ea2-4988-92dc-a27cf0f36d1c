{% extends "stages/base_stages.html" %}

{% block stages_content %}
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>Suivi & Alertes</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#configAlertsModal">
            <i class="fas fa-cog"></i> Configurer les alertes
        </button>
    </div>
</div>

<div class="row">
    <!-- Carte des alertes -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Alertes actives</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">5</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-bell fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Carte des stages à venir -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-info h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Stages à venir</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">8</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Carte des prises d'armes -->
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Prochaine prise d'armes</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">15/04/2024</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-flag fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des alertes -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header">
                <h5 class="card-title mb-0">Alertes actives</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fas fa-exclamation-circle text-warning fa-2x"></i>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-1">Fin de stage proche</h6>
                                    <small class="text-muted">Il y a 2h</small>
                                </div>
                                <p class="mb-1">Le stage de BENJELLOUN Hamza se termine dans 7 jours</p>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle text-danger fa-2x"></i>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-1">Document manquant</h6>
                                    <small class="text-muted">Il y a 1j</small>
                                </div>
                                <p class="mb-1">Convention non signée pour TAZI Ahmed</p>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <i class="fas fa-info-circle text-info fa-2x"></i>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-1">Prise d'armes à venir</h6>
                                    <small class="text-muted">Il y a 3j</small>
                                </div>
                                <p class="mb-1">Prise d'armes prévue le 15/04/2024</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendrier des événements -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header">
                <h5 class="card-title mb-0">Calendrier des événements</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Prise d'armes</h6>
                                <small class="text-muted">15/04/2024 - 09:00</small>
                            </div>
                            <span class="badge bg-primary">À venir</span>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Fin de stage - BENJELLOUN Hamza</h6>
                                <small class="text-muted">22/04/2024</small>
                            </div>
                            <span class="badge bg-warning">Dans 7 jours</span>
                        </div>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">Début de stage - Promotion 2024</h6>
                                <small class="text-muted">02/05/2024</small>
                            </div>
                            <span class="badge bg-info">Planifié</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Configuration des Alertes -->
<div class="modal fade" id="configAlertsModal" tabindex="-1" aria-labelledby="configAlertsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="configAlertsModalLabel">Configuration des alertes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="alertConfigForm">
                    <div class="mb-3">
                        <label class="form-label">Alertes de fin de stage</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="alert7days" checked>
                            <label class="form-check-label" for="alert7days">
                                7 jours avant la fin
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="alert3days" checked>
                            <label class="form-check-label" for="alert3days">
                                3 jours avant la fin
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alertes documents</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="alertDocs" checked>
                            <label class="form-check-label" for="alertDocs">
                                Documents manquants
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Alertes prises d'armes</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="alertCeremony" checked>
                            <label class="form-check-label" for="alertCeremony">
                                Rappel de prise d'armes
                            </label>
                        </div>
                        <select class="form-select mt-2" id="ceremonyAlertDelay">
                            <option value="7">7 jours avant</option>
                            <option value="3">3 jours avant</option>
                            <option value="1">1 jour avant</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary">Enregistrer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Gestion du formulaire de configuration des alertes
    const alertConfigForm = document.getElementById('alertConfigForm');
    const saveButton = alertConfigForm.nextElementSibling.querySelector('.btn-primary');

    saveButton.addEventListener('click', function() {
        // Récupération des valeurs du formulaire
        const config = {
            alert7days: document.getElementById('alert7days').checked,
            alert3days: document.getElementById('alert3days').checked,
            alertDocs: document.getElementById('alertDocs').checked,
            alertCeremony: document.getElementById('alertCeremony').checked,
            ceremonyAlertDelay: document.getElementById('ceremonyAlertDelay').value
        };

        // Simulation de sauvegarde
        console.log('Configuration sauvegardée:', config);
        
        // Fermeture de la modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('configAlertsModal'));
        modal.hide();
    });
});
</script>
{% endblock %} 