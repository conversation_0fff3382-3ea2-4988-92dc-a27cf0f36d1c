{% extends "base.html" %}

{% block content %}
<div class="container mt-5">
    <h2>Modifier un véhicule GAR</h2>
    <form method="POST">
        <div class="form-group">
            <label for="matricule">Matricule</label>
            <input type="text" class="form-control" id="matricule" name="matricule" value="{{ vehicule.matricule }}" required>
        </div>
        <div class="form-group">
            <label for="unite">Unité</label>
            <select class="form-control" id="unite" name="unite" required>
                {% for i in range(1, 27) %}
                    <option value="{{ i }}GAR" {% if vehicule.unite == i|string + 'GAR' %}selected{% endif %}>{{ i }}GAR</option>
                {% endfor %}
            </select>
        </div>
        <div class="form-group">
            <label for="type_vehicule">Type de véhicule</label>
            <select class="form-control" id="type_vehicule" name="type_vehicule" required onchange="updateMarques()">
                <option value="VL" {% if vehicule.type_vehicule == 'VL' %}selected{% endif %}>VL</option>
                <option value="PL" {% if vehicule.type_vehicule == 'PL' %}selected{% endif %}>PL</option>
                <option value="SA" {% if vehicule.type_vehicule == 'SA' %}selected{% endif %}>SA</option>
                <option value="Engin chenillé" {% if vehicule.type_vehicule == 'Engin chenillé' %}selected{% endif %}>Engin chenillé</option>
            </select>
        </div>
        <div class="form-group">
            <label for="marque">Marque</label>
            <select class="form-control" id="marque" name="marque" required>
                <!-- Les options seront remplies dynamiquement par JavaScript -->
            </select>
        </div>
        <button type="submit" class="btn btn-primary mt-3">Modifier</button>

        <script>
            const TYPES_VEHICULES = {
                'VL': ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan'],
                'PL': ['JBD', 'Kaiser', 'Renault'],
                'Engin chenillé': ['M109 155mm', 'M110 203mm', 'Vulcan'],
                'SA': ['PULS', 'HIMARS', 'CESAR']
            };

            function updateMarques() {
                const typeVehicule = document.getElementById('type_vehicule').value;
                const marqueSelect = document.getElementById('marque');
                const currentMarque = '{{ vehicule.marque }}';
                marqueSelect.innerHTML = ''; // Vider les options actuelles
                
                const marques = TYPES_VEHICULES[typeVehicule] || [];
                marques.forEach(marque => {
                    const option = document.createElement('option');
                    option.value = marque;
                    option.textContent = marque;
                    if (marque === currentMarque) {
                        option.selected = true;
                    }
                    marqueSelect.appendChild(option);
                });
            }

            // Initialiser les marques au chargement de la page
            document.addEventListener('DOMContentLoaded', updateMarques);
        </script>
        <a href="{{ url_for('liste_vehicules_gar') }}" class="btn btn-secondary mt-3">Annuler</a>
    </form>
</div>
{% endblock %}
