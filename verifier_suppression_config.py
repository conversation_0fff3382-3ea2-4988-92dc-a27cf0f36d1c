#!/usr/bin/env python3
"""
Script pour vérifier que l'interface de configuration de base de données a été supprimée
"""

import requests
import webbrowser

BASE_URL = "http://localhost:3000"

def verifier_page_gestion_courrier():
    """Vérifier que la page /gestion_courrier n'affiche plus l'interface de configuration"""
    print("🔍 Vérification de la page /gestion_courrier...")
    
    try:
        response = requests.get(f"{BASE_URL}/gestion_courrier")
        if response.status_code == 200:
            content = response.text
            
            # Vérifier que les éléments de configuration ont été supprimés
            elements_supprimes = [
                "Configuration de la Base de Données",
                "Initialiser les tables pour la gestion des courriers",
                "Initialiser la Base de Données",
                "init_courriers_db"
            ]
            
            elements_trouves = []
            for element in elements_supprimes:
                if element in content:
                    elements_trouves.append(element)
            
            if elements_trouves:
                print("❌ PROBLÈME: Éléments de configuration encore présents:")
                for element in elements_trouves:
                    print(f"   • {element}")
                return False
            else:
                print("✅ SUCCÈS: Interface de configuration supprimée")
                
                # Vérifier que les éléments principaux sont toujours présents
                elements_requis = [
                    "Courriers Arrivés",
                    "Courriers Envoyés",
                    "courrier_arrives",
                    "courrier_envoyes"
                ]
                
                elements_manquants = []
                for element in elements_requis:
                    if element not in content:
                        elements_manquants.append(element)
                
                if elements_manquants:
                    print("⚠️  ATTENTION: Éléments principaux manquants:")
                    for element in elements_manquants:
                        print(f"   • {element}")
                else:
                    print("✅ SUCCÈS: Éléments principaux conservés")
                
                return True
                
        else:
            print(f"❌ Erreur HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def verifier_route_init_db():
    """Vérifier que la route d'initialisation existe toujours (mais n'est plus accessible via l'interface)"""
    print("\n🔍 Vérification de la route /init_courriers_db...")
    
    try:
        response = requests.get(f"{BASE_URL}/init_courriers_db")
        if response.status_code == 302:  # Redirection attendue
            print("✅ Route d'initialisation fonctionne (redirection)")
        elif response.status_code == 200:
            print("✅ Route d'initialisation fonctionne")
        else:
            print(f"⚠️  Route d'initialisation: Status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def ouvrir_page_test():
    """Ouvrir la page pour vérification visuelle"""
    print("\n🌐 Ouverture de la page pour vérification visuelle...")
    webbrowser.open(f"{BASE_URL}/gestion_courrier")

def main():
    """Fonction principale"""
    print("🗑️ VÉRIFICATION DE LA SUPPRESSION DE L'INTERFACE DE CONFIGURATION")
    print("=" * 70)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Vérifications
    page_ok = verifier_page_gestion_courrier()
    verifier_route_init_db()
    ouvrir_page_test()
    
    print("\n" + "=" * 70)
    print("🎯 RÉSULTAT DE LA VÉRIFICATION")
    print("=" * 70)
    
    if page_ok:
        print("🎉 SUCCÈS COMPLET:")
        print("✅ Interface de configuration supprimée")
        print("✅ Fonctionnalités principales conservées")
        print("✅ Page propre et professionnelle")
    else:
        print("❌ PROBLÈME DÉTECTÉ:")
        print("❌ Interface de configuration encore visible")
    
    print("\n📋 VÉRIFICATION VISUELLE:")
    print("1. 🌐 La page /gestion_courrier est ouverte")
    print("2. 👀 Vérifiez que vous voyez SEULEMENT:")
    print("   • Carte 'Courriers Arrivés'")
    print("   • Carte 'Courriers Envoyés'")
    print("3. 🚫 Vérifiez que vous ne voyez PLUS:")
    print("   • Section 'Configuration de la Base de Données'")
    print("   • Bouton 'Initialiser la Base de Données'")
    print("   • Texte 'Initialiser les tables pour la gestion des courriers'")
    
    print("\n✨ RÉSULTAT ATTENDU:")
    print("• Interface propre avec seulement les 2 cartes principales")
    print("• Pas d'éléments techniques visibles pour l'utilisateur final")
    print("• Apparence professionnelle et épurée")

if __name__ == "__main__":
    main()
