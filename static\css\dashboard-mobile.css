/* Dashboard Mobile CSS - Corrections pour la page d'accueil sur mobile */

/* Règles générales pour mobile */
@media (max-width: 768px) {
    /* Bouton de sidebar toujours visible */
    #sidebarCollapse {
        display: block !important;
        position: fixed !important;
        top: 10px !important;
        left: 10px !important;
        z-index: 1050 !important;
        background: rgba(75, 83, 32, 0.9) !important;
        border: 2px solid #8b7355 !important;
        color: #ffffff !important;
        padding: 8px 12px !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
        transition: all 0.3s ease !important;
    }
    
    #sidebarCollapse:hover {
        background: rgba(75, 83, 32, 1) !important;
        transform: scale(1.05) !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4) !important;
    }
    
    #sidebarCollapse i {
        font-size: 1.2rem !important;
        color: #ffffff !important;
    }
    
    /* Sidebar mobile */
    #sidebar {
        position: fixed !important;
        top: 0 !important;
        left: -280px !important;
        width: 280px !important;
        height: 100vh !important;
        z-index: 1040 !important;
        transition: left 0.3s ease !important;
        background: rgba(75, 83, 32, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        border-right: 2px solid #8b7355 !important;
        overflow-y: auto !important;
    }
    
    #sidebar.active {
        left: 0 !important;
    }
    
    /* Overlay pour fermer la sidebar */
    .sidebar-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1030;
    }
    
    .sidebar-overlay.active {
        display: block;
    }
    
    /* Contenu principal */
    #content {
        margin-left: 0 !important;
        padding-top: 60px !important;
    }
    
    /* Navbar mobile */
    .navbar {
        padding: 10px !important;
        background: rgba(75, 83, 32, 0.9) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
    }
    
    .navbar .container-fluid {
        justify-content: center !important;
    }
    
    /* Horloge numérique mobile */
    .digital-clock {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 6px 10px !important;
        min-width: auto !important;
        width: auto !important;
        height: auto !important;
        margin: 0 auto !important;
        font-size: 0.8rem !important;
        background: rgba(0, 0, 0, 0.6) !important;
        border-radius: 6px !important;
    }
    
    .digital-clock-time {
        font-size: 1rem !important;
        margin-right: 3px !important;
    }
    
    .digital-clock-seconds {
        font-size: 0.7rem !important;
        margin-left: 2px !important;
        position: static !important;
        top: auto !important;
        right: auto !important;
    }
    
    .digital-clock-ampm {
        font-size: 0.6rem !important;
        margin-left: 3px !important;
        position: static !important;
        top: auto !important;
        left: auto !important;
    }
    
    .digital-clock-date {
        font-size: 0.6rem !important;
        margin-left: 6px !important;
        position: static !important;
        bottom: auto !important;
        opacity: 1 !important;
        transform: none !important;
    }
    
    /* Titres et textes */
    .welcome-title {
        font-size: 1.5rem !important;
        text-align: center !important;
        margin-bottom: 0.5rem !important;
    }
    
    .welcome-text {
        font-size: 0.9rem !important;
        text-align: center !important;
        margin-bottom: 1rem !important;
    }
    
    /* Cartes d'applications */
    .app-card {
        margin-bottom: 1rem !important;
        padding: 15px !important;
        border-radius: 10px !important;
    }
    
    .app-icon {
        width: 60px !important;
        height: 60px !important;
        margin: 0 auto 10px !important;
    }
    
    .app-card h3 {
        font-size: 1.1rem !important;
        margin-bottom: 8px !important;
    }
    
    .app-card p {
        font-size: 0.8rem !important;
        margin-bottom: 15px !important;
    }
    
    .app-card .btn {
        padding: 8px 16px !important;
        font-size: 0.9rem !important;
    }
}

/* Règles pour très petits écrans */
@media (max-width: 576px) {
    #sidebarCollapse {
        top: 5px !important;
        left: 5px !important;
        padding: 6px 10px !important;
    }
    
    #sidebarCollapse i {
        font-size: 1rem !important;
    }
    
    #content {
        padding-top: 50px !important;
    }
    
    .navbar {
        padding: 8px !important;
    }
    
    .digital-clock {
        padding: 4px 8px !important;
        font-size: 0.7rem !important;
    }
    
    .digital-clock-time {
        font-size: 0.9rem !important;
    }
    
    .digital-clock-seconds {
        font-size: 0.6rem !important;
    }
    
    .digital-clock-ampm {
        font-size: 0.5rem !important;
    }
    
    .digital-clock-date {
        font-size: 0.5rem !important;
    }
    
    .welcome-title {
        font-size: 1.3rem !important;
    }
    
    .welcome-text {
        font-size: 0.8rem !important;
    }
    
    .app-card {
        padding: 12px !important;
    }
    
    .app-icon {
        width: 50px !important;
        height: 50px !important;
    }
    
    .app-card h3 {
        font-size: 1rem !important;
    }
    
    .app-card p {
        font-size: 0.75rem !important;
    }
    
    .app-card .btn {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }
}

/* Règles pour les écrans en mode paysage sur mobile */
@media (max-width: 768px) and (orientation: landscape) {
    #sidebarCollapse {
        top: 5px !important;
        left: 5px !important;
        padding: 6px 10px !important;
    }
    
    #sidebarCollapse i {
        font-size: 1rem !important;
    }
    
    #content {
        padding-top: 40px !important;
    }
    
    .navbar {
        padding: 6px !important;
    }
    
    .digital-clock {
        padding: 4px 8px !important;
        font-size: 0.7rem !important;
    }
    
    .welcome-title {
        font-size: 1.2rem !important;
        margin-bottom: 0.3rem !important;
    }
    
    .welcome-text {
        font-size: 0.8rem !important;
        margin-bottom: 0.8rem !important;
    }
    
    .app-card {
        padding: 10px !important;
        margin-bottom: 0.8rem !important;
    }
    
    .app-icon {
        width: 45px !important;
        height: 45px !important;
        margin-bottom: 8px !important;
    }
    
    .app-card h3 {
        font-size: 0.9rem !important;
        margin-bottom: 6px !important;
    }
    
    .app-card p {
        font-size: 0.7rem !important;
        margin-bottom: 10px !important;
    }
    
    .app-card .btn {
        padding: 5px 10px !important;
        font-size: 0.75rem !important;
    }
}

/* Améliorations pour l'accessibilité mobile */
@media (max-width: 768px) {
    /* Augmenter la taille des zones tactiles */
    #sidebarCollapse,
    .app-card .btn {
        min-height: 44px; /* Recommandation Apple pour les zones tactiles */
    }
    
    /* Améliorer les contrastes */
    #sidebarCollapse {
        border-width: 2px !important;
    }
    
    .app-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }
    
    /* Améliorer les états de focus */
    #sidebarCollapse:focus {
        outline: 2px solid #e6c78b !important;
        outline-offset: 2px !important;
    }
    
    .app-card .btn:focus {
        outline: 2px solid #e6c78b !important;
        outline-offset: 2px !important;
    }
}

/* Corrections spécifiques pour les animations sur mobile */
@media (max-width: 768px) {
    /* Réduire les animations pour améliorer les performances */
    .app-card {
        transition: transform 0.2s ease !important;
    }
    
    .app-card:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
    }
    
    #sidebarCollapse {
        transition: all 0.2s ease !important;
    }
    
    #sidebar {
        transition: left 0.3s ease !important;
    }
}

/* Corrections pour les polices sur mobile */
@media (max-width: 768px) {
    /* Optimiser les polices pour mobile */
    .welcome-title {
        font-weight: 600 !important;
        line-height: 1.3 !important;
    }
    
    .welcome-text {
        line-height: 1.4 !important;
    }
    
    .app-card h3 {
        font-weight: 600 !important;
        line-height: 1.2 !important;
    }
    
    .app-card p {
        line-height: 1.3 !important;
    }
}

/* Corrections pour les bordures et effets sur mobile */
@media (max-width: 768px) {
    /* Simplifier les bordures pour de meilleures performances */
    .app-card {
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
    
    .navbar {
        border-bottom: 1px solid rgba(139, 115, 85, 0.3) !important;
    }
}

/* Améliorations pour la navigation au clavier sur mobile */
@media (max-width: 768px) {
    /* Améliorer la navigation au clavier */
    #sidebarCollapse:focus,
    .app-card .btn:focus {
        outline: 2px solid #e6c78b !important;
        outline-offset: 2px !important;
    }
    
    /* Améliorer l'ordre de tabulation */
    #sidebarCollapse {
        tabindex: 1;
    }
    
    .app-card .btn {
        tabindex: 2;
    }
}

/* Corrections pour les éléments de la sidebar sur mobile */
@media (max-width: 768px) {
    /* Éléments de la sidebar */
    #sidebar .sidebar-header {
        padding: 15px !important;
        border-bottom: 1px solid rgba(139, 115, 85, 0.3) !important;
    }
    
    #sidebar .sidebar-header h3 {
        font-size: 1.2rem !important;
        margin: 0 !important;
    }
    
    #sidebar .list-unstyled {
        padding: 10px 0 !important;
    }
    
    #sidebar .list-unstyled li {
        margin: 0 !important;
    }
    
    #sidebar .list-unstyled a {
        padding: 12px 20px !important;
        font-size: 0.9rem !important;
        border-bottom: 1px solid rgba(139, 115, 85, 0.1) !important;
    }
    
    #sidebar .list-unstyled a:hover {
        background: rgba(139, 115, 85, 0.2) !important;
    }
    
    #sidebar .sidebar-footer {
        padding: 15px !important;
        border-top: 1px solid rgba(139, 115, 85, 0.3) !important;
    }
    
    #sidebar .about-btn {
        padding: 10px 15px !important;
        font-size: 0.9rem !important;
        width: 100% !important;
        text-align: center !important;
    }
}

/* Corrections pour les icônes dans la sidebar */
@media (max-width: 768px) {
    #sidebar .list-unstyled a i {
        font-size: 1.1rem !important;
        margin-right: 10px !important;
        width: 20px !important;
        text-align: center !important;
    }
    
    #sidebar .about-btn i {
        font-size: 1rem !important;
        margin-right: 8px !important;
    }
}

/* Corrections pour les états actifs dans la sidebar */
@media (max-width: 768px) {
    #sidebar .list-unstyled a.active {
        background: rgba(139, 115, 85, 0.3) !important;
        border-left: 4px solid #e6c78b !important;
        font-weight: 600 !important;
    }
    
    #sidebar .list-unstyled a.active i {
        color: #e6c78b !important;
    }
} 