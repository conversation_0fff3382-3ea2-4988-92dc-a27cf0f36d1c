{% extends "base.html" %}

{% block title %}Codes QR pour Connexion{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12 text-center">
            <h2 class="mb-4">Codes QR pour Connexion</h2>
            <p class="text-muted">Scannez ces codes QR pour vous connecter au système</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <!-- Code QR pour Redouane -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header text-center bg-primary text-white">
                    <h4 class="mb-0">Redouane</h4>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=user:redouane" alt="QR Code Redouane" class="img-fluid">
                    </div>
                    <p class="card-text">Utilisateur: <strong>Redouane</strong></p>
                </div>
            </div>
        </div>

        <!-- Code QR pour Mustapha -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header text-center bg-success text-white">
                    <h4 class="mb-0">Mustapha</h4>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=user:mustapha" alt="QR Code Mustapha" class="img-fluid">
                    </div>
                    <p class="card-text">Utilisateur: <strong>Mustapha</strong></p>
                </div>
            </div>
        </div>

        <!-- Code QR pour Dareb Fss9ef -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header text-center bg-danger text-white">
                    <h4 class="mb-0">Dareb Fss9ef</h4>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=user:dareb_fss9ef" alt="QR Code Dareb Fss9ef" class="img-fluid">
                    </div>
                    <p class="card-text">Utilisateur: <strong>Dareb Fss9ef</strong></p>
                </div>
            </div>
        </div>

        <!-- Code QR pour l3ziiz -->
        <div class="col-md-3 mb-4">
            <div class="card">
                <div class="card-header text-center bg-info text-white">
                    <h4 class="mb-0">L3ziiz</h4>
                </div>
                <div class="card-body text-center">
                    <div class="qr-code-container mb-3">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=user:l3ziiz" alt="QR Code L3ziiz" class="img-fluid">
                    </div>
                    <p class="card-text">Utilisateur: <strong>L3ziiz</strong></p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{{ url_for('index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Retour à l'accueil
            </a>
            <button class="btn btn-primary ms-2" onclick="window.print()">
                <i class="fas fa-print me-2"></i>Imprimer les codes QR
            </button>
        </div>
    </div>
</div>

<style>
    .qr-code-container {
        padding: 15px;
        background-color: white;
        border-radius: 10px;
        display: inline-block;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    @media print {
        .btn, .text-muted {
            display: none;
        }
        .card {
            break-inside: avoid;
            page-break-inside: avoid;
            box-shadow: none !important;
        }
        body {
            padding: 0;
            margin: 0;
        }
    }
</style>
{% endblock %}
