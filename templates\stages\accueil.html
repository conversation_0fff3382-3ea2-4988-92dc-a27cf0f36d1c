{% extends "stages/base_stages.html" %}

{% block title %}Gestion des Stages - Accueil{% endblock %}

{% block extra_css %}
<style>
    .stages-dashboard {
        padding: 2rem;
    }
    .stages-title {
        font-size: 2.5rem;
        font-weight: 900;
        margin-bottom: 2rem;
        color: var(--primary-color);
        text-align: center;
        text-transform: uppercase;
        font-family: 'Black Ops One', 'Poppins', sans-serif;
        text-shadow: 2px 2px 0 var(--camo-dark);
    }
    .stages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        padding: 1rem;
    }
    .stage-card {
        background: linear-gradient(135deg, var(--camo-mid) 0%, var(--camo-dark) 100%);
        border: 2px solid var(--primary-color);
        border-radius: 12px;
        padding: 1.5rem;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    .stage-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        border-color: var(--accent-color);
        text-decoration: none;
        color: white;
    }
    .stage-card-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        padding: 8px;
    }
    .stage-card-content {
        flex: 1;
    }
    .stage-card-title {
        font-size: 1.25rem;
        font-weight: bold;
        margin: 0;
    }
    .stage-card-description {
        font-size: 0.9rem;
        opacity: 0.8;
        margin: 5px 0 0;
    }
    @media (max-width: 768px) {
        .stages-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="stages-dashboard">
    <h1 class="stages-title">Gestion des Stages</h1>
    
    <div class="stages-grid">
        <a href="{{ url_for('stages.stagiaires') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-users fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Liste des Stagiaires</h3>
                <p class="stage-card-description">Gérer les informations des stagiaires</p>
            </div>
        </a>

        <a href="{{ url_for('stages.stages') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-graduation-cap fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Liste des Stages</h3>
                <p class="stage-card-description">Consulter et gérer les stages en cours</p>
            </div>
        </a>

        <a href="{{ url_for('stages.promotions') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-user-graduate fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Promotions</h3>
                <p class="stage-card-description">Gestion des promotions et des groupes</p>
            </div>
        </a>

        <a href="{{ url_for('stages.suivi_alertes') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-bell fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Suivi & Alertes</h3>
                <p class="stage-card-description">Suivre les événements et les alertes</p>
            </div>
        </a>

        <a href="{{ url_for('stages.documents') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-file-alt fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Documents</h3>
                <p class="stage-card-description">Gérer les documents et les modèles</p>
            </div>
        </a>

        <a href="{{ url_for('stages.ia') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-robot fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Assistant IA</h3>
                <p class="stage-card-description">Aide et assistance intelligente</p>
            </div>
        </a>

        <a href="{{ url_for('stages.utilisateurs') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-user-shield fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Utilisateurs</h3>
                <p class="stage-card-description">Gestion des accès et des rôles</p>
            </div>
        </a>

        <a href="{{ url_for('stages.journalisation') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-history fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Journalisation</h3>
                <p class="stage-card-description">Historique des activités</p>
            </div>
        </a>

        <a href="{{ url_for('stages.courrier') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-envelope fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Gestion du Courrier</h3>
                <p class="stage-card-description">Courriers reçus et envoyés de la division</p>
            </div>
        </a>

        <a href="{{ url_for('stages.statistiques') }}" class="stage-card">
            <div class="stage-card-icon">
                <i class="fas fa-chart-bar fa-lg"></i>
            </div>
            <div class="stage-card-content">
                <h3 class="stage-card-title">Statistiques</h3>
                <p class="stage-card-description">Rapports et analyses</p>
            </div>
        </a>
    </div>
</div>
{% endblock %} 