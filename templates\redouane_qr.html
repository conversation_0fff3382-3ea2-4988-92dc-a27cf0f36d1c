<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code d'Authentification - Redouane</title>
    <link href="https://fonts.googleapis.com/css2?family=Black+Ops+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/qrcode-generator@1.4.4/qrcode.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Roboto Condensed', sans-serif;
        }

        body {
            min-height: 100vh;
            background:
                linear-gradient(45deg, rgba(75, 83, 32, 0.3), rgba(59, 66, 25, 0.3)),
                url('{{ url_for('static', filename='images/a6.jpg') }}');
            background-size: cover;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            backdrop-filter: blur(1.5px);
            -webkit-backdrop-filter: blur(1.5px);
            z-index: 0;
        }

        .container {
            background: rgba(75, 83, 32, 0.2);
            border: 4px solid #8b7355;
            padding: 30px;
            width: 100%;
            max-width: 600px;
            position: relative;
            box-shadow: 0 0 40px rgba(0,0,0,0.3), 0 0 15px rgba(139, 115, 85, 0.5);
            backdrop-filter: blur(2px);
            -webkit-backdrop-filter: blur(2px);
            z-index: 1;
            text-align: center;
            border-radius: 10px;
        }

        h1 {
            font-family: 'Black Ops One', cursive;
            color: #ffffff;
            text-shadow: 3px 3px 0 rgba(0,0,0,0.9), 0 0 20px rgba(0,0,0,0.8);
            margin-bottom: 20px;
            font-size: 2em;
        }

        .qr-container {
            background: white;
            padding: 20px;
            display: inline-block;
            margin: 20px auto;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }

        .instructions {
            color: #e6c78b;
            margin: 20px 0;
            font-size: 1.2em;
            line-height: 1.5;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .back-btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.8), rgba(139, 115, 85, 0.8));
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-family: 'Black Ops One', cursive;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, rgba(75, 83, 32, 0.9), rgba(139, 115, 85, 0.9));
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }

        .qr-info {
            color: #d0d0c0;
            margin-top: 15px;
            font-size: 0.9em;
            line-height: 1.5;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 5px;
            border-left: 3px solid #8b7355;
        }

        .qr-info p {
            margin: 5px 0;
        }

        .security-notice {
            margin-top: 20px;
            color: #e6c78b;
            font-weight: bold;
            background: rgba(75, 83, 32, 0.3);
            padding: 10px;
            border-radius: 5px;
            display: inline-block;
        }

        .security-notice i {
            margin-right: 8px;
            color: #8b7355;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>QR CODE D'AUTHENTIFICATION</h1>
        <div class="instructions">
            Utilisez ce QR code pour vous connecter au système de gestion des véhicules militaires en tant que <strong>redouane</strong>.
        </div>
        <div class="qr-container" id="qrcode"></div>
        <div class="qr-info">
            <p><strong>IMPORTANT :</strong> Ce QR code est unique et strictement personnel.</p>
            <p>Il est réservé à l'usage exclusif de l'utilisateur autorisé.</p>
            <p>Toute tentative d'utilisation non autorisée sera enregistrée et signalée.</p>
        </div>
        <div class="security-notice">
            <i class="fas fa-shield-alt"></i> Sécurité renforcée : Seul ce QR code est accepté par le système.
        </div>
        <a href="{{ url_for('login') }}" class="back-btn">
            <i class="fas fa-arrow-left"></i> Retour à la page de connexion
        </a>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Générer le QR code
            var qrData = "{{ qr_data }}";
            var qrContainer = document.getElementById('qrcode');

            // Utiliser la bibliothèque qrcode-generator
            var qr = qrcode(0, 'M');
            qr.addData(qrData);
            qr.make();

            // Afficher le QR code
            qrContainer.innerHTML = qr.createImgTag(5);
        });
    </script>
</body>
</html>
