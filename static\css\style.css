:root {
    --primary-color: #4B5320; /* Vert militaire */
    --secondary-color: #6B8E23; /* <PERSON> Drab - inchangé */
    --accent-color: #8B4513; /* Saddle Brown - inchangé */
    --background-color: #F5F5DC; /* Beige - inchangé */
    --text-color: #2E2E2E; /* Gris foncé - inchangé */
    --success-color: #3B5323; /* Camouflage Green - inchangé */
    --warning-color: #D2B48C; /* Tan - inchangé */
    --camo-light: #7D8C65; /* Vert clair camouflage */
    --camo-mid: #4A5D23; /* Vert moyen camouflage */
    --camo-dark: #2F3A17; /* Vert foncé camouflage */
    --camo-tan: #A89976; /* Beige camouflage */
    --camo-brown: #5D4B35; /* Brun camouflage */
}

body {
    font-family: 'Poppins', sans-serif;
    position: relative;
    background-color: var(--background-color);
    margin: 0;
    padding: 0;
    color: var(--text-color);
    min-height: 100vh;
}

/* Pattern de camouflage avancé sur le body */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -1;
    background:
        radial-gradient(ellipse at 50% 50%, var(--camo-mid) 20%, transparent 21%),
        radial-gradient(ellipse at 25% 25%, var(--camo-dark) 15%, transparent 16%),
        radial-gradient(ellipse at 75% 75%, var(--camo-light) 24%, transparent 25%),
        radial-gradient(ellipse at 80% 20%, var(--camo-brown) 12%, transparent 13%),
        radial-gradient(ellipse at 15% 85%, var(--camo-tan) 18%, transparent 19%),
        var(--camo-light);
    background-size: 200px 200px;
    opacity: 0.95;
    background-attachment: fixed;
}

.navbar {
    background: var(--primary-color);
    position: relative;
    overflow: hidden;
    padding: 1rem 2rem;
    box-shadow: 0 2px 15px rgba(0,0,0,0.3);
    z-index: 100;
}

/* Pattern de camouflage sur la navbar */
.navbar::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: 
        linear-gradient(45deg, var(--camo-dark) 25%, transparent 25%),
        linear-gradient(135deg, var(--camo-dark) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, var(--camo-dark) 75%),
        linear-gradient(135deg, transparent 75%, var(--camo-dark) 75%);
    background-size: 40px 40px;
    background-position: 0 0, 20px 0, 20px -20px, 0px 20px;
    opacity: 0.4;
}

.navbar-brand {
    color: white !important;
    font-size: 1.5rem;
    font-weight: 700;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    letter-spacing: 1px;
}

.nav-link {
    color: rgba(255,255,255,0.85) !important;
    transition: color 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    color: white !important;
    text-shadow: 0 0 8px rgba(255,255,255,0.5);
}

.container {
    padding: 1rem 1.25rem;
    max-width: 94%;
    margin: 0 auto;
    background-color: rgba(245, 245, 220, 0.85);
    backdrop-filter: blur(5px);
    border-radius: 12px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
    position: relative;
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

/* Bordure camouflage pour le container */
.container::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 8px solid transparent;
    border-image: linear-gradient(45deg, 
        var(--camo-dark) 0%, 
        var(--camo-mid) 25%, 
        var(--camo-light) 50%, 
        var(--camo-brown) 75%, 
        var(--camo-dark) 100%) 1;
    border-radius: 8px;
    pointer-events: none;
    z-index: 1;
}

.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    border: none;
    position: relative;
    overflow: hidden;
}

/* Effet camouflage subtil sur les cartes */
.card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 30% 30%, var(--camo-light) 1%, transparent 3%),
        radial-gradient(circle at 70% 65%, var(--camo-mid) 1%, transparent 3%),
        radial-gradient(circle at 20% 80%, var(--camo-dark) 1%, transparent 3%),
        radial-gradient(circle at 90% 10%, var(--camo-tan) 1%, transparent 3%);
    background-size: 80px 80px;
    opacity: 0.08;
    z-index: 0;
    pointer-events: none;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.card-header {
    background: var(--primary-color);
    position: relative;
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 1rem;
    font-weight: 600;
    z-index: 1;
}

/* Pattern camouflage pour card header */
.card-header::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(90deg, var(--camo-dark) 10%, transparent 10%, transparent 90%, var(--camo-dark) 90%),
        linear-gradient(0deg, var(--camo-mid) 15%, transparent 15%, transparent 85%, var(--camo-mid) 85%);
    opacity: 0.3;
    z-index: -1;
}

/* Boutons inchangés comme demandé */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary {
    background: var(--secondary-color);
    border: none;
}

.btn-primary:hover {
    background: #556B2F;
    transform: translateY(-2px);
}

.btn-danger {
    background: var(--accent-color);
    border: none;
}

.btn-danger:hover {
    background: #5C3317;
    transform: translateY(-2px);
}

.form-control {
    border-radius: 5px;
    border: 2px solid #ddd;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: none;
}

/* Dashboard styles */
.stat-card {
    text-align: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    z-index: 1;
}

/* Fond camouflage pour les cartes statistiques */
.stat-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    z-index: -2;
}

.stat-card::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(45deg, var(--camo-dark) 25%, transparent 25%, transparent 75%, var(--camo-dark) 75%),
        linear-gradient(45deg, var(--camo-mid) 25%, transparent 25%, transparent 75%, var(--camo-mid) 75%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
    opacity: 0.15;
    z-index: -1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 1rem 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
}

.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

/* Bordure stylisée pour les graphiques */
.chart-container::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px solid transparent;
    border-image: linear-gradient(45deg, 
        var(--camo-mid) 0%, 
        var(--camo-light) 50%, 
        var(--camo-dark) 100%) 1;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
@font-face {
    font-family: 'Black Ops One';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url(font\black.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Table styles */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.table thead th {
    background: var(--primary-color);
    position: relative;
    color: white;
    border: none;
    overflow: hidden;
}

/* Pattern pour les en-têtes de tableau */
.table thead th::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(45deg, transparent 45%, var(--camo-dark) 45%, var(--camo-dark) 55%, transparent 55%);
    background-size: 10px 10px;
    opacity: 0.2;
    z-index: 0;
}

.table tbody tr:hover {
    background: rgba(107, 142, 35, 0.1);
}

/* Alert styles */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.5s ease;
    position: relative;
    overflow: hidden;
}

.alert::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px);
    z-index: 0;
    pointer-events: none;
}

.alert-success {
    background: var(--success-color);
    color: white;
}

.alert-danger {
    background: var(--accent-color);
    color: white;
}

/* Progress bars */
.progress {
    height: 10px;
    border-radius: 5px;
    margin: 1rem 0;
    overflow: hidden;
    background-color: #f0f0e0;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.progress-bar {
    background: var(--secondary-color);
    position: relative;
    overflow: hidden;
}

/* Effet pour les barres de progression */
.progress-bar::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        linear-gradient(90deg, 
            transparent 0%, 
            rgba(255,255,255,0.2) 50%, 
            transparent 100%);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
        max-width: 100%;
        margin: 0.5rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }
    
    .chart-container {
        margin-bottom: 1rem;
        padding: 1rem;
        height: auto;
        min-height: 250px;
    }
    
    body::before {
        background-size: 100px 100px;
    }
    
    /* Améliorations pour les cartes */
    .card {
        margin-bottom: 1rem;
        border-radius: 8px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Améliorations pour les boutons */
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        margin: 0.25rem;
    }
    
    /* Améliorations pour les formulaires */
    .form-control {
        font-size: 1rem;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    /* Améliorations pour les tableaux */
    .table-responsive {
        border-radius: 8px;
        overflow: hidden;
    }
    
    .table {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }
    
    /* Améliorations pour la navigation */
    .navbar {
        padding: 0.5rem 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
    }
    
    /* S'assurer que le bouton hamburger est visible sur mobile */
    .navbar-toggler {
        display: block !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        padding: 0.25rem 0.5rem !important;
    }
    
    .navbar-toggler-icon {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        background-size: 100% !important;
        width: 1.5em !important;
        height: 1.5em !important;
        display: inline-block !important;
    }
    
    /* Améliorations pour les alertes */
    .alert {
        padding: 0.75rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    /* Améliorations pour les grilles */
    .row {
        margin: 0;
    }
    
    .col-md-6,
    .col-lg-4,
    .col-xl-3 {
        padding: 0.25rem;
    }
}