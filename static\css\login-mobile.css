/* Login Mobile CSS - Corrections pour la page de login sur mobile */

/* R<PERSON>gles générales pour mobile */
@media (max-width: 768px) {
    /* Permettre le défilement sur mobile */
    body {
        overflow-y: auto !important;
        overflow-x: hidden;
        min-height: 100vh;
        height: auto;
    }
    
    /* Container principal */
    .container {
        flex-direction: column;
        padding: 8px;
        min-height: 100vh;
        height: auto;
        box-sizing: border-box;
    }
    
    /* Section gauche - Titre */
    .left-section {
        flex: none;
        padding: 10px 3px;
        text-align: center;
        order: 1;
    }
    
    .left-section h1 {
        font-size: 1.8em;
        margin-bottom: 15px;
        line-height: 1.2;
    }
    
    /* Section droite - Formulaire */
    .right-section {
        flex: none;
        padding: 5px;
        order: 2;
        align-items: center;
        width: 100%;
        box-sizing: border-box;
    }
    
    /* Boîte de login */
    .login-box {
        width: 100%;
        max-width: 100%;
        padding: 15px;
        margin: 5px;
        border-radius: 10px;
        box-sizing: border-box;
    }
    
    /* En-tête du login */
    .login-header img {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
    }
    
    .login-header h2 {
        font-size: 1.5em;
        margin-bottom: 8px;
    }
    
    /* Horloge numérique mobile - Ajustements */
    .digital-clock {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 8px 12px !important;
        min-width: auto !important;
        width: auto !important;
        height: auto !important;
        margin: 10px auto !important;
        font-size: 0.9rem !important;
    }
    
    .digital-clock-time {
        font-size: 1.2rem !important;
        margin-right: 4px !important;
    }
    
    .digital-clock-seconds {
        font-size: 0.8rem !important;
        margin-left: 2px !important;
        position: static !important;
        top: auto !important;
        right: auto !important;
    }
    
    .digital-clock-ampm {
        font-size: 0.7rem !important;
        margin-left: 4px !important;
        position: static !important;
        top: auto !important;
        left: auto !important;
    }
    
    .digital-clock-date {
        font-size: 0.7rem !important;
        margin-left: 8px !important;
        position: static !important;
        bottom: auto !important;
        opacity: 1 !important;
        transform: none !important;
    }
    
    /* Champs de formulaire */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group input {
        padding: 12px 12px 12px 40px;
        font-size: 16px; /* Évite le zoom sur iOS */
        border-radius: 5px;
    }
    
    .form-group i {
        left: 12px;
        font-size: 18px;
    }
    
    /* Bouton de connexion */
    .login-btn {
        padding: 15px;
        font-size: 16px;
        border-radius: 5px;
    }
    
    /* Options d'authentification */
    .auth-options {
        margin-top: 15px;
    }
    
    .auth-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .auth-btn {
        width: 100%;
        padding: 12px;
        font-size: 14px;
        min-width: auto;
    }
    
    /* Alertes */
    .alert {
        padding: 12px;
        margin-bottom: 15px;
        font-size: 14px;
    }
    
    /* Modals */
    .qr-modal-content {
        width: 95%;
        padding: 20px;
        margin: 10px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .qr-modal-header h3 {
        font-size: 1.2em;
    }
    
    /* Bouton de fermeture du modal */
    .qr-close {
        top: -10px;
        right: -10px;
        width: 30px;
        height: 30px;
    }
    
    /* Zone de scan QR */
    #qr-video-container {
        width: 100%;
        max-width: 300px;
        height: 200px;
    }
    
    #qr-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
    }
    
    /* Zone de capture faciale */
    #facial-video-container {
        width: 100%;
        max-width: 300px;
        height: 200px;
    }
    
    #facial-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
    }
}

/* Règles pour très petits écrans */
@media (max-width: 576px) {
    .container {
        padding: 8px;
    }
    
    .left-section {
        padding: 8px 3px;
    }
    
    .left-section h1 {
        font-size: 1.5em;
        letter-spacing: 2px;
    }
    
    .right-section {
        padding: 3px;
    }
    
    .login-box {
        padding: 10px;
    }
    
    .login-header img {
        width: 35px;
        height: 35px;
    }
    
    .login-header h2 {
        font-size: 1.3em;
    }
    
    /* Ajustements supplémentaires pour les très petits écrans */
    .login-box {
        padding: 12px !important;
        margin: 5px !important;
    }
    
    .login-header {
        margin-bottom: 15px !important;
    }
    
    /* Contrôle strict des logos pour éviter qu'ils sortent du cadre */
    .login-header img {
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
        display: block !important;
        margin: 0 auto 10px !important;
    }
    
    /* Ajustement du conteneur de l'en-tête */
    .login-header {
        max-width: 100% !important;
        overflow: hidden !important;
        text-align: center !important;
    }
    
    .form-group input {
        padding: 10px 10px 10px 35px;
        font-size: 16px;
    }
    
    .form-group i {
        left: 10px;
        font-size: 16px;
    }
    
    .login-btn {
        padding: 12px;
        font-size: 14px;
    }
    
    .auth-btn {
        padding: 10px;
        font-size: 13px;
    }
    
    .alert {
        padding: 10px;
        font-size: 13px;
    }
}

/* Règles pour les écrans en mode paysage sur mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .container {
        flex-direction: row;
        padding: 10px;
    }
    
    .left-section {
        flex: 1;
        padding: 10px;
        order: 1;
    }
    
    .left-section h1 {
        font-size: 1.2em;
        margin-bottom: 10px;
    }
    
    .right-section {
        flex: 1;
        padding: 10px;
        order: 2;
    }
    
    .login-box {
        padding: 15px;
    }
    
    .login-header img {
        width: 60px;
        height: 60px;
        margin-bottom: 5px;
    }
    
    .login-header h2 {
        font-size: 1.1em;
        margin-bottom: 5px;
    }
    
    .form-group {
        margin-bottom: 10px;
    }
    
    .form-group input {
        padding: 8px 8px 8px 30px;
        font-size: 14px;
    }
    
    .form-group i {
        left: 8px;
        font-size: 14px;
    }
    
    .login-btn {
        padding: 8px;
        font-size: 12px;
    }
    
    .auth-buttons {
        flex-direction: row;
        gap: 5px;
    }
    
    .auth-btn {
        padding: 8px;
        font-size: 11px;
        min-width: 80px;
    }
}

/* Améliorations pour l'accessibilité mobile */
@media (max-width: 768px) {
    /* Augmenter la taille des zones tactiles */
    .form-group input,
    .login-btn,
    .auth-btn {
        min-height: 44px; /* Recommandation Apple pour les zones tactiles */
    }
    
    /* Améliorer la lisibilité */
    .form-group input::placeholder {
        font-size: 16px;
    }
    
    /* Améliorer les contrastes */
    .login-box {
        background: rgba(75, 83, 32, 0.3);
        border-width: 3px;
    }
    
    .form-group input {
        background: rgba(0, 0, 0, 0.2);
        border-width: 2px;
    }
    
    /* Améliorer les états de focus */
    .form-group input:focus {
        border-width: 3px;
        box-shadow: 0 0 15px rgba(139, 115, 85, 0.5);
    }
    
    .login-btn:focus,
    .auth-btn:focus {
        outline: 2px solid #e6c78b;
        outline-offset: 2px;
    }
}

/* Corrections spécifiques pour les modals sur mobile */
@media (max-width: 768px) {
    .qr-modal {
        padding: 10px;
    }
    
    .qr-modal-content {
        border-radius: 10px;
        max-height: 85vh;
    }
    
    /* Améliorer la navigation dans les modals */
    .qr-modal-content {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* Défilement fluide sur iOS */
    }
    
    /* Boutons dans les modals */
    .qr-modal .btn {
        min-height: 44px;
        font-size: 16px;
        margin: 5px;
    }
    
    /* Messages d'erreur dans les modals */
    .qr-modal .alert {
        margin: 10px 0;
        font-size: 14px;
    }
}

/* Corrections pour les animations sur mobile */
@media (max-width: 768px) {
    /* Réduire les animations pour améliorer les performances */
    .login-box {
        animation: none;
    }
    
    .login-btn::before {
        animation: none;
    }
    
    .ops {
        animation: none;
    }
    
    .ops span:last-child {
        animation: none;
    }
    
    /* Garder seulement les animations essentielles */
    .form-group input:focus,
    .login-btn:hover,
    .auth-btn:hover {
        transition: all 0.2s ease;
    }
}

/* Corrections pour les polices sur mobile */
@media (max-width: 768px) {
    /* Réduire la taille des polices pour économiser l'espace */
    .ops {
        font-size: 0.9em;
        letter-spacing: 2px;
    }
    
    .login-header h2 {
        letter-spacing: 2px;
    }
    
    .login-btn {
        letter-spacing: 1px;
    }
    
    .auth-btn {
        letter-spacing: 1px;
    }
}

/* Corrections pour les bordures et effets sur mobile */
@media (max-width: 768px) {
    /* Simplifier les bordures pour de meilleures performances */
    .border-frame {
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        border-width: 1px;
    }
    
    .border-frame::before {
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        border-width: 1px;
    }
    
    .login-box::before,
    .login-box::after {
        width: 15px;
        height: 15px;
        border-width: 2px;
    }
    
    .login-box::before {
        top: -8px;
        left: -8px;
    }
    
    .login-box::after {
        bottom: -8px;
        right: -8px;
    }
}

/* Corrections pour les images de fond sur mobile */
@media (max-width: 768px) {
    .background-slider img {
        object-fit: cover;
        object-position: center;
    }
    
    /* Optimiser les images de fond pour mobile */
    .background-slider {
        background-color: rgba(75, 83, 32, 0.8);
    }
}

/* Corrections pour les effets de particules sur mobile */
@media (max-width: 768px) {
    /* Désactiver les effets de particules pour améliorer les performances */
    .ops .particle {
        display: none;
    }
    
    .ops::before {
        display: none;
    }
}

/* Améliorations pour la navigation au clavier sur mobile */
@media (max-width: 768px) {
    /* Améliorer la navigation au clavier */
    .form-group input:focus,
    .login-btn:focus,
    .auth-btn:focus {
        outline: 2px solid #e6c78b;
        outline-offset: 2px;
    }
    
    /* Améliorer l'ordre de tabulation */
    .form-group input {
        tabindex: 1;
    }
    
    .login-btn {
        tabindex: 2;
    }
    
    .auth-btn {
        tabindex: 3;
    }
} 