{% extends "rh/base_rh.html" %}

{% block title %}Gestion Médicale - {{ militaire.nom }} {{ militaire.prenom }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            Gestion Médicale - {{ militaire.grade_actuel.libelle if militaire.grade_actuel else 'Grade' }} {{ militaire.nom }} {{ militaire.prenom }}
                        </h4>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i> Retour à la Fiche
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Situation Médicale Générale -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-md me-2"></i>Situation Médicale Générale</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="situation_medicale">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Groupe Sanguin</label>
                                <select name="groupe_sanguin_id" class="form-control">
                                    <option value="">Sélectionner</option>
                                    {% for groupe in groupes_sanguins %}
                                    <option value="{{ groupe.id_groupe }}" 
                                            {% if situation_medicale and situation_medicale.groupe_sanguin_id == groupe.id_groupe %}selected{% endif %}>
                                        {{ groupe.libelle }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Taille (cm)</label>
                                <input type="number" name="taille_cm" class="form-control" 
                                       value="{{ situation_medicale.taille_cm if situation_medicale else '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Poids (kg)</label>
                                <input type="number" step="0.1" name="poids_kg" class="form-control" 
                                       value="{{ situation_medicale.poids_kg if situation_medicale else '' }}">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">Aptitude</label>
                                <select name="aptitude" class="form-control">
                                    <option value="">Sélectionner</option>
                                    <option value="Apte" {% if situation_medicale and situation_medicale.aptitude == 'Apte' %}selected{% endif %}>Apte</option>
                                    <option value="Inapte" {% if situation_medicale and situation_medicale.aptitude == 'Inapte' %}selected{% endif %}>Inapte</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Observations</label>
                                <textarea name="observations" class="form-control" rows="3">{{ situation_medicale.observations if situation_medicale else '' }}</textarea>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Mettre à Jour
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique Médical avec Modification Directe -->
    <div class="row">
        <!-- Hospitalisations -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between">
                    <h6><i class="fas fa-hospital me-2"></i>Hospitalisations ({{ hospitalisations|length }})</h6>
                    <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#modalHospitalisation">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="card-body">
                    {% if hospitalisations %}
                        {% for hosp in hospitalisations %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ hosp.date_entree.strftime('%d/%m/%Y') }}</strong>
                                    {% if hosp.date_sortie %}
                                        → {{ hosp.date_sortie.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        → <span class="badge bg-warning">En cours</span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">{{ hosp.motif }}</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="modifierHospitalisation({{ hosp.id_hospitalisation }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune hospitalisation enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Vaccinations -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between">
                    <h6><i class="fas fa-syringe me-2"></i>Vaccinations ({{ vaccinations|length }})</h6>
                    <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#modalVaccination">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="card-body">
                    {% if vaccinations %}
                        {% for vacc in vaccinations %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ vacc.date_vaccination.strftime('%d/%m/%Y') }}</strong>
                                    <br>
                                    <small class="text-muted">{{ vacc.type_vaccin }}</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="modifierVaccination({{ vacc.id_vaccination }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucune vaccination enregistrée</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- PTC -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between">
                    <h6><i class="fas fa-user-injured me-2"></i>PTC ({{ ptcs|length }})</h6>
                    <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#modalPTC">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div class="card-body">
                    {% if ptcs %}
                        {% for ptc in ptcs %}
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ ptc.date_debut.strftime('%d/%m/%Y') }}</strong>
                                    {% if ptc.date_fin %}
                                        → {{ ptc.date_fin.strftime('%d/%m/%Y') }}
                                    {% else %}
                                        → <span class="badge bg-danger">En cours</span>
                                    {% endif %}
                                    <br>
                                    <small class="text-muted">{{ ptc.motif }}</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-warning" onclick="modifierPTC({{ ptc.id_ptc }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">Aucun PTC enregistré</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- MODALS POUR MODIFICATION DIRECTE -->

    <!-- Modal PTC -->
    <div class="modal fade" id="modalPTC" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-user-injured me-2"></i>Gestion PTC</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="nouveau_ptc">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Début</label>
                                <input type="date" name="date_debut" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Fin</label>
                                <input type="date" name="date_fin" class="form-control">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Motif</label>
                                <textarea name="motif" class="form-control" rows="3" required></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Hospitalisation -->
    <div class="modal fade" id="modalHospitalisation" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-hospital me-2"></i>Nouvelle Hospitalisation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="nouvelle_hospitalisation">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Entrée</label>
                                <input type="date" name="date_entree" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Sortie</label>
                                <input type="date" name="date_sortie" class="form-control">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Motif</label>
                                <textarea name="motif" class="form-control" rows="3" required></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Vaccination -->
    <div class="modal fade" id="modalVaccination" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-syringe me-2"></i>Nouvelle Vaccination</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="nouvelle_vaccination">
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Date Vaccination</label>
                                <input type="date" name="date_vaccination" class="form-control" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">Type de Vaccin</label>
                                <input type="text" name="type_vaccin" class="form-control" required>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">Observations</label>
                                <textarea name="observations" class="form-control" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
