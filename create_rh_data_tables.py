#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de création des tables de données RH selon architecture_rh.md
Création des 15 tables de données avec structure exacte
"""

import mysql.connector
from mysql.connector import Error
import sys

def create_connection():
    """Créer une connexion à la base de données MySQL"""
    try:
        connection = mysql.connector.connect(
            host='localhost',
            database='gestion_vehicules',
            user='root',
            password='',
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )
        if connection.is_connected():
            print("✅ Connexion à MySQL réussie - Base de données: gestion_vehicules")
            return connection
    except Error as e:
        print(f"❌ Erreur de connexion à MySQL: {e}")
        return None

def create_data_tables(connection):
    """Créer les 15 tables de données selon architecture_rh.md"""
    cursor = connection.cursor()
    
    try:
        print("\n🗄️ Création des tables de données RH...")
        
        # 2.1 personnel - Table principale
        print("📝 Création de personnel...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS personnel (
                matricule VARCHAR(20) PRIMARY KEY,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                nom_arabe VARCHAR(100) NOT NULL,
                prenom_arabe VARCHAR(100) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                sexe_id INT NOT NULL,
                categorie_id INT NOT NULL,
                groupe_sanguin_id INT NOT NULL,
                numero_cin VARCHAR(20) NOT NULL,
                date_delivrance_cin DATE NOT NULL,
                date_expiration_cin DATE NOT NULL,
                gsm VARCHAR(20) NOT NULL,
                telephone_domicile VARCHAR(20) NULL,
                taille DECIMAL(5,2) NOT NULL,
                lieu_residence VARCHAR(150) NOT NULL,
                arme_id INT NOT NULL,
                specialite_id INT NULL,
                unite_id INT NOT NULL,
                grade_actuel_id INT NOT NULL,
                fonction VARCHAR(100) NOT NULL,
                date_prise_fonction DATE NOT NULL,
                ccp VARCHAR(50) NOT NULL,
                compte_bancaire VARCHAR(50) NULL,
                numero_somme VARCHAR(50) NOT NULL,
                date_engagement DATE NOT NULL,
                nom_pere VARCHAR(100) NOT NULL,
                prenom_pere VARCHAR(100) NOT NULL,
                nom_mere VARCHAR(100) NOT NULL,
                prenom_mere VARCHAR(100) NOT NULL,
                adresse_parents VARCHAR(200) NOT NULL,
                situation_fam_id INT NOT NULL,
                nombre_enfants INT NULL,
                numero_passport VARCHAR(50) NULL,
                date_delivrance_passport DATE NULL,
                date_expiration_passport DATE NULL,
                gsm_urgence VARCHAR(20) NOT NULL,
                degre_parente_id INT NOT NULL,
                FOREIGN KEY (sexe_id) REFERENCES referentiel_genre(id_genre),
                FOREIGN KEY (categorie_id) REFERENCES referentiel_categorie(id_categorie),
                FOREIGN KEY (groupe_sanguin_id) REFERENCES referentiel_groupe_sanguin(id_groupe),
                FOREIGN KEY (arme_id) REFERENCES referentiel_arme(id_arme),
                FOREIGN KEY (specialite_id) REFERENCES referentiel_specialite(id_specialite),
                FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite),
                FOREIGN KEY (grade_actuel_id) REFERENCES referentiel_grade(id_grade),
                FOREIGN KEY (situation_fam_id) REFERENCES referentiel_situation_familiale(id_sitfam),
                FOREIGN KEY (degre_parente_id) REFERENCES referentiel_degre_parente(id_degre)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2.2 personnel_langue - Table de liaison
        print("📝 Création de personnel_langue...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS personnel_langue (
                matricule VARCHAR(20) NOT NULL,
                langue_id INT NOT NULL,
                PRIMARY KEY (matricule, langue_id),
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (langue_id) REFERENCES referentiel_langue(id_langue)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2.3 conjoint - Table conjoint(e)
        print("📝 Création de conjoint...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS conjoint (
                id_conjoint INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL UNIQUE,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                nom_arabe VARCHAR(100) NOT NULL,
                prenom_arabe VARCHAR(100) NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                lieu_naissance_arabe VARCHAR(100) NOT NULL,
                adresse VARCHAR(200) NOT NULL,
                adresse_arabe VARCHAR(200) NOT NULL,
                date_mariage DATE NOT NULL,
                lieu_mariage VARCHAR(100) NOT NULL,
                profession VARCHAR(100) NOT NULL,
                profession_arabe VARCHAR(100) NOT NULL,
                numero_cin VARCHAR(20) NOT NULL,
                gsm VARCHAR(20) NOT NULL,
                nom_pere VARCHAR(100) NOT NULL,
                prenom_pere VARCHAR(100) NOT NULL,
                nom_arabe_pere VARCHAR(100) NOT NULL,
                prenom_arabe_pere VARCHAR(100) NOT NULL,
                nom_mere VARCHAR(100) NOT NULL,
                prenom_mere VARCHAR(100) NOT NULL,
                nom_arabe_mere VARCHAR(100) NOT NULL,
                prenom_arabe_mere VARCHAR(100) NOT NULL,
                profession_pere VARCHAR(100) NOT NULL,
                profession_mere VARCHAR(100) NOT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2.4 enfant
        print("📝 Création de enfant...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS enfant (
                id_enfant INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                nom VARCHAR(100) NOT NULL,
                prenom VARCHAR(100) NOT NULL,
                sexe_id INT NOT NULL,
                date_naissance DATE NOT NULL,
                lieu_naissance VARCHAR(100) NOT NULL,
                date_deces DATE NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (sexe_id) REFERENCES referentiel_genre(id_genre)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2.5 situation_medicale
        print("📝 Création de situation_medicale...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS situation_medicale (
                id_sitmed INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL UNIQUE,
                maladies TEXT NOT NULL,
                date_hospitalisation DATE NOT NULL,
                lieu_hospitalisation VARCHAR(100) NOT NULL,
                aptitude ENUM('apte','inapte') NOT NULL,
                observations TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        
        # 2.6 vaccination
        print("📝 Création de vaccination...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vaccination (
                id_vaccination INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_vaccination DATE NOT NULL,
                objet VARCHAR(100) NOT NULL,
                observation TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.7 ptc
        print("📝 Création de ptc...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ptc (
                id_ptc INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_ptc DATE NOT NULL,
                duree INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                objet VARCHAR(100) NOT NULL,
                observations TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.8 permission
        print("📝 Création de permission...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permission (
                id_permission INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                adresse VARCHAR(200) NOT NULL,
                numero_serie VARCHAR(50) NOT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.9 desertion
        print("📝 Création de desertion...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS desertion (
                id_desertion INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_absence DATE NOT NULL,
                date_desertion DATE NOT NULL,
                date_retour DATE NOT NULL,
                date_arret_solde DATE NULL,
                date_prise_solde DATE NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.10 detachement
        print("📝 Création de detachement...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS detachement (
                id_detachement INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_debut DATE NOT NULL,
                adresse_detachement VARCHAR(200) NOT NULL,
                pays VARCHAR(100) NOT NULL,
                date_fin DATE NOT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.11 mutation_fonction
        print("📝 Création de mutation_fonction...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mutation_fonction (
                id_mutation INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                service_id INT NOT NULL,
                fonction VARCHAR(100) NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (service_id) REFERENCES referentiel_arme(id_arme)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.12 sejour_ops
        print("📝 Création de sejour_ops...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sejour_ops (
                id_sejour_ops INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                unite_id INT NOT NULL,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (unite_id) REFERENCES referentiel_unite(id_unite)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.13 liberation
        print("📝 Création de liberation...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS liberation (
                id_liberation INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                motif VARCHAR(200) NOT NULL,
                date_liberation DATE NOT NULL,
                observation TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.14 avancement
        print("📝 Création de avancement...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS avancement (
                id_avancement INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                grade_precedent_id INT NOT NULL,
                grade_suivant_id INT NOT NULL,
                date_avancement DATE NOT NULL,
                conditions TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule),
                FOREIGN KEY (grade_precedent_id) REFERENCES referentiel_grade(id_grade),
                FOREIGN KEY (grade_suivant_id) REFERENCES referentiel_grade(id_grade)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        # 2.15 sanction
        print("📝 Création de sanction...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sanction (
                id_sanction INT AUTO_INCREMENT PRIMARY KEY,
                matricule VARCHAR(20) NOT NULL,
                date_sanction DATE NOT NULL,
                type_sanction ENUM('sanction','punition') NOT NULL,
                duree INT NULL,
                motif VARCHAR(200) NOT NULL,
                observation TEXT NULL,
                FOREIGN KEY (matricule) REFERENCES personnel(matricule)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)

        connection.commit()
        print("✅ Toutes les tables de données RH créées avec succès")

    except Error as e:
        print(f"❌ Erreur lors de la création des tables de données: {e}")
        connection.rollback()
        raise e
    finally:
        cursor.close()

if __name__ == "__main__":
    connection = create_connection()
    if connection:
        try:
            create_data_tables(connection)
            print("\n🎉 Création complète des 25 tables RH terminée avec succès!")
        except Exception as e:
            print(f"\n💥 Erreur fatale: {e}")
            sys.exit(1)
        finally:
            connection.close()
            print("🔌 Connexion fermée")
    else:
        print("💥 Impossible de se connecter à la base de données")
        sys.exit(1)
