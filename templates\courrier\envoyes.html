{% extends "base.html" %}
{% block title %}Courriers Envoyés - Gestion du Courrier{% endblock %}
{% block content %}
<div class="container-fluid mt-4">


    <!-- Actions et Tableau -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header modal-card-header d-flex justify-content-between align-items-center" style="background: #6B8E23; color: white;">
                    <h5 class="mb-0 text-white"><i class="fas fa-list me-2"></i>Liste des Courriers Envoyés</h5>
                    <button class="btn" style="background: rgba(255,255,255,0.15); color: white; border: 1px solid rgba(255,255,255,0.3);" data-bs-toggle="modal" data-bs-target="#modalAjouterCourrier">
                        <i class="fas fa-plus me-2"></i>Ajouter Courrier
                    </button>
                </div>
                <div class="card-body">
                    <!-- Filtres rapides -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="filter-division">
                                <option value="">Toutes les divisions</option>
                                <option value="courrier">Division Courrier</option>
                                <option value="instruction">Division Instruction</option>
                                <option value="technique">Division Technique</option>
                                <option value="rh">Division RH</option>
                                <option value="mcpo">Division MCPO</option>
                                <option value="informatique">Division Informatique</option>
                                <option value="planification">Division Planification</option>
                                <option value="asa">Division ASA</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filter-nature">
                                <option value="">Toutes les natures</option>
                                <option value="message">Message</option>
                                <option value="nds">NDS</option>
                                <option value="note_royale">Note Royale</option>
                                <option value="decision">Décision</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="filter-search" placeholder="Rechercher par ID, objet, destinataire...">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Tableau -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="table-courriers">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Division</th>
                                    <th>N° Écrit</th>
                                    <th>Nature</th>
                                    <th>Objet</th>
                                    <th>Destinataire</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="tbody-courriers">
                                <!-- Contenu dynamique -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Ajouter Courrier -->
<div class="modal fade" id="modalAjouterCourrier" tabindex="-1" aria-labelledby="modalAjouterCourrierLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: #f8f9fa; color: #495057; border-bottom: 1px solid #dee2e6;">
                <h5 class="modal-title" id="modalAjouterCourrierLabel">
                    <i class="fas fa-plus me-2"></i>Ajouter un Courrier Envoyé
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-courrier-envoye">
                    <div class="row">
                        <!-- Colonne 1 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header modal-card-header" style="background: #6B8E23; color: white;">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">ID Courrier *</label>
                                        <input type="text" class="form-control" id="id_courrier" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">N° Écrit Division *</label>
                                        <input type="text" class="form-control" id="numero_ecrit" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Division émettrice *</label>
                                        <select class="form-select" id="division_emettrice" required>
                                            <option value="">Choisir...</option>
                                            <option value="courrier">Division Courrier</option>
                                            <option value="instruction">Division Instruction</option>
                                            <option value="technique">Division Technique</option>
                                            <option value="rh">Division RH</option>
                                            <option value="mcpo">Division MCPO</option>
                                            <option value="informatique">Division Informatique</option>
                                            <option value="planification">Division Planification</option>
                                            <option value="asa">Division ASA</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Date de départ *</label>
                                        <input type="date" class="form-control" id="date_depart" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Nature du courrier *</label>
                                        <select class="form-select" id="nature" required>
                                            <option value="">Choisir...</option>
                                            <option value="message">Message</option>
                                            <option value="nds">NDS</option>
                                            <option value="note_royale">Note Royale</option>
                                            <option value="decision">Décision</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Colonne 2 -->
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header modal-card-header" style="background: #6B8E23; color: white;">
                                    <h6 class="mb-0"><i class="fas fa-envelope me-2"></i>Contenu et Destination</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Objet *</label>
                                        <textarea class="form-control" id="objet" rows="4" required placeholder="Objet du courrier..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Organe destinataire *</label>
                                        <input type="text" class="form-control" id="destinataire" required placeholder="Organe externe destinataire">
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Exemple: Commandement Régional Sud, État-Major Général, etc.
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Observations</label>
                                        <textarea class="form-control" id="observations" rows="3" placeholder="Observations particulières..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="ajouterCourrier()">
                    <i class="fas fa-paper-plane me-2"></i>Valider l'Envoi
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.card-header h6 {
    font-weight: 600;
}

.table th {
    font-weight: 600;
    font-size: 0.875em;
}

.form-text {
    font-size: 0.875em;
    color: #6c757d;
}
</style>

<script>
// Variables globales
let courriersEnvoyes = [];

// Initialisation robuste
function initializePage() {
    console.log('🚀 Initialisation de la page courriers envoyés...');

    // Vérifier que les éléments DOM critiques existent
    const requiredElements = ['tbody-courriers'];
    let allElementsFound = true;

    for (const elementId of requiredElements) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`❌ Élément manquant: ${elementId}`);
            allElementsFound = false;
        } else {
            console.log(`✅ Élément trouvé: ${elementId}`);
        }
    }

    if (!allElementsFound) {
        console.error('❌ Éléments DOM manquants, nouvelle tentative dans 500ms...');
        setTimeout(initializePage, 500);
        return;
    }

    // Charger les données
    loadMockData().then(() => {
        console.log('✅ Données chargées, mise à jour de l\'affichage...');
        updateCounters();
        renderTable();
        setupEvents();

        // Date par défaut
        const dateInput = document.getElementById('date_depart');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }

        console.log('✅ Initialisation terminée, courriers chargés:', courriersEnvoyes.length);
    }).catch(error => {
        console.error('❌ Erreur lors du chargement des données:', error);
    });
}

// Points d'entrée pour l'initialisation
document.addEventListener('DOMContentLoaded', initializePage);
window.addEventListener('load', function() {
    if (courriersEnvoyes.length === 0) {
        console.log('🔄 Réinitialisation depuis window.load...');
        initializePage();
    }
});

// Charger les données depuis l'API
async function loadMockData() {
    console.log('📡 Chargement des courriers envoyés depuis l\'API...');
    try {
        const response = await fetch('/api/courriers/envoyes');
        if (response.ok) {
            courriersEnvoyes = await response.json();
            console.log('✅ Courriers envoyés chargés:', courriersEnvoyes.length);
        } else {
            console.error('❌ Erreur lors du chargement des courriers:', response.statusText);
            courriersEnvoyes = [];
        }
    } catch (error) {
        console.error('❌ Erreur lors du chargement des courriers:', error);
        courriersEnvoyes = [];
    }
}

// Mettre à jour les compteurs
function updateCounters() {
    const total = courriersEnvoyes.length;
    const today = courriersEnvoyes.filter(c => c.date_depart === new Date().toISOString().split('T')[0]).length;

    // Mise à jour sécurisée des compteurs
    const countTotalEl = document.getElementById('count-total');
    const countTodayEl = document.getElementById('count-today');

    if (countTotalEl) {
        countTotalEl.textContent = total;
    }

    if (countTodayEl) {
        countTodayEl.textContent = today;
    }

    console.log(`📊 Compteurs mis à jour: ${total} total, ${today} aujourd'hui`);
}

// Configurer les événements
function setupEvents() {
    // Filtres
    document.getElementById('filter-division').addEventListener('change', renderTable);
    document.getElementById('filter-nature').addEventListener('change', renderTable);
    document.getElementById('filter-search').addEventListener('input', renderTable);
}

// Rendre le tableau
function renderTable() {
    const tbody = document.getElementById('tbody-courriers');
    tbody.innerHTML = '';

    // Appliquer les filtres
    let courriers = [...courriersEnvoyes];

    const filterDivision = document.getElementById('filter-division').value;
    const filterNature = document.getElementById('filter-nature').value;
    const filterSearch = document.getElementById('filter-search').value.toLowerCase();

    if (filterDivision) {
        courriers = courriers.filter(c => c.division_emettrice === filterDivision);
    }

    if (filterNature) {
        courriers = courriers.filter(c => c.nature === filterNature);
    }

    if (filterSearch) {
        courriers = courriers.filter(c =>
            c.id.toLowerCase().includes(filterSearch) ||
            c.objet.toLowerCase().includes(filterSearch) ||
            c.destinataire.toLowerCase().includes(filterSearch) ||
            c.numero_ecrit.toLowerCase().includes(filterSearch)
        );
    }

    courriers.forEach((courrier, index) => {
        const natureBadge = getNatureBadge(courrier.nature);
        const divisionBadge = getDivisionBadge(courrier.division_emettrice);

        const row = `
            <tr>
                <td><strong>${courrier.id}</strong></td>
                <td>${formatDate(courrier.date_depart)}</td>
                <td>${divisionBadge}</td>
                <td><code>${courrier.numero_ecrit}</code></td>
                <td>${natureBadge}</td>
                <td class="text-truncate" style="max-width: 250px;" title="${courrier.objet}">${courrier.objet}</td>
                <td>${courrier.destinataire}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="voirDetails(${index})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="scannerCourrier(${index})" title="Scanner">
                        <i class="fas fa-scan"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Ajouter un courrier
async function ajouterCourrier() {
    // Récupérer les données du formulaire
    const id = document.getElementById('id_courrier').value;
    const numeroEcrit = document.getElementById('numero_ecrit').value;
    const divisionEmettrice = document.getElementById('division_emettrice').value;
    const dateDepart = document.getElementById('date_depart').value;
    const nature = document.getElementById('nature').value;
    const objet = document.getElementById('objet').value;
    const destinataire = document.getElementById('destinataire').value;
    const observations = document.getElementById('observations').value;

    // Validation détaillée
    const champsManquants = [];
    if (!id) champsManquants.push('ID Courrier');
    if (!numeroEcrit) champsManquants.push('N° Écrit Division');
    if (!divisionEmettrice) champsManquants.push('Division émettrice');
    if (!dateDepart) champsManquants.push('Date de départ');
    if (!nature) champsManquants.push('Nature du courrier');
    if (!objet) champsManquants.push('Objet');
    if (!destinataire) champsManquants.push('Organe destinataire');

    if (champsManquants.length > 0) {
        alert('Veuillez remplir les champs obligatoires suivants :\n• ' + champsManquants.join('\n• '));
        console.log('🔍 Debug validation:');
        console.log('ID:', id);
        console.log('Numéro écrit:', numeroEcrit);
        console.log('Division émettrice:', divisionEmettrice);
        console.log('Date départ:', dateDepart);
        console.log('Nature:', nature);
        console.log('Objet:', objet);
        console.log('Destinataire:', destinataire);
        return;
    }

    // Vérifier les doublons
    if (courriersEnvoyes.some(c => c.id === id)) {
        alert('⚠️ ATTENTION: Un courrier avec cet ID existe déjà!');
        return;
    }

    // Créer le nouveau courrier
    const nouveauCourrier = {
        id,
        numero_ecrit: numeroEcrit,
        division_emettrice: divisionEmettrice,
        date_depart: dateDepart,
        nature,
        objet,
        destinataire,
        observations
    };

    try {
        // Envoyer à l'API
        const response = await fetch('/api/courriers/envoyes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(nouveauCourrier)
        });

        if (response.ok) {
            const courrierAjoute = await response.json();

            // Recharger les données depuis l'API
            await loadMockData();

            // Réinitialiser et fermer
            document.getElementById('form-courrier-envoye').reset();
            document.getElementById('date_depart').value = new Date().toISOString().split('T')[0];

            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAjouterCourrier'));
            modal.hide();

            updateCounters();
            renderTable();

            alert('✅ Courrier envoyé ajouté avec succès!');
        } else {
            const error = await response.json();
            alert('❌ Erreur: ' + error.error);
        }
    } catch (error) {
        console.error('Erreur lors de l\'ajout du courrier:', error);
        alert('❌ Erreur lors de l\'ajout du courrier');
    }
}

// Fonctions utilitaires
function getNatureBadge(nature) {
    const badges = {
        'message': '<span class="badge bg-info">Message</span>',
        'nds': '<span class="badge bg-primary">NDS</span>',
        'note_royale': '<span class="badge bg-warning text-dark">Note Royale</span>',
        'decision': '<span class="badge bg-success">Décision</span>'
    };
    return badges[nature] || '<span class="badge bg-light text-dark">-</span>';
}

function getDivisionBadge(division) {
    const badges = {
        'courrier': '<span class="badge bg-dark">Courrier</span>',
        'instruction': '<span class="badge bg-primary">Instruction</span>',
        'technique': '<span class="badge bg-success">Technique</span>',
        'rh': '<span class="badge bg-info">RH</span>',
        'mcpo': '<span class="badge bg-warning text-dark">MCPO</span>',
        'informatique': '<span class="badge bg-secondary">Informatique</span>',
        'planification': '<span class="badge bg-danger">Planification</span>',
        'asa': '<span class="badge bg-dark">ASA</span>'
    };
    return badges[division] || '<span class="badge bg-light text-dark">-</span>';
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}

function resetFilters() {
    document.getElementById('filter-division').value = '';
    document.getElementById('filter-nature').value = '';
    document.getElementById('filter-search').value = '';
    renderTable();
}

function voirDetails(index) {
    const courrier = courriersEnvoyes[index];

    let details = `
        <div class="modal fade" id="modalDetails" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Détails du Courrier ${courrier.id}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6"><strong>ID:</strong> ${courrier.id}</div>
                            <div class="col-md-6"><strong>N° Écrit:</strong> <code>${courrier.numero_ecrit}</code></div>
                            <div class="col-md-6"><strong>Division émettrice:</strong> ${getDivisionBadge(courrier.division_emettrice)}</div>
                            <div class="col-md-6"><strong>Date de départ:</strong> ${formatDate(courrier.date_depart)}</div>
                            <div class="col-md-6"><strong>Nature:</strong> ${getNatureBadge(courrier.nature)}</div>
                            <div class="col-12 mt-2"><strong>Objet:</strong> ${courrier.objet}</div>
                            <div class="col-12 mt-2"><strong>Destinataire:</strong> ${courrier.destinataire}</div>
                            <div class="col-12 mt-2"><strong>Observations:</strong> ${courrier.observations || 'Aucune'}</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprimer l'ancienne modale si elle existe
    const existingModal = document.getElementById('modalDetails');
    if (existingModal) {
        existingModal.remove();
    }

    // Ajouter la nouvelle modale
    document.body.insertAdjacentHTML('beforeend', details);

    // Afficher la modale
    const modal = new bootstrap.Modal(document.getElementById('modalDetails'));
    modal.show();
}

function scannerCourrier(index) {
    const courrier = courriersEnvoyes[index];
    alert(`📄 Fonction de scan pour le courrier ${courrier.id}\n\nCette fonctionnalité permettra de:\n- Scanner le document physique\n- L'associer au courrier électronique\n- Le rendre accessible pour archivage`);
}
</script>

<style>
/* Désactiver le pattern de camouflage pour les en-têtes des modals */
.modal-card-header::before {
    display: none !important;
}
</style>
{% endblock %}
