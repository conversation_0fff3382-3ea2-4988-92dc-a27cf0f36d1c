"""
Script de test des fonctionnalités RH
Teste toutes les fonctionnalités de l'application
Division des Ressources Humaines - Inspection de l'Artillerie
"""

from datetime import datetime, date, timedelta
from app import app
from db import db
from gestion_vehicules.rh.models import *

def creer_militaire_test():
    """Crée un militaire de test pour vérifier les fonctionnalités"""
    try:
        with app.app_context():
            print("👤 Création d'un militaire de test...")
            
            # Récupération des IDs de référence
            genre_m = ReferentielGenre.query.filter_by(libelle='Masculin').first()
            categorie_off = ReferentielCategorie.query.filter_by(libelle='Officier').first()
            groupe_o_plus = ReferentielGroupeSanguin.query.filter_by(libelle='O+').first()
            service_art = ReferentielService.query.filter_by(code_court='ART').first()
            unite_1gar = ReferentielUnite.query.filter_by(code='1GAR').first()
            grade_cpt = ReferentielGrade.query.filter_by(code_grade='CPT').first()
            etat_marie = ReferentielEtatMatrimonial.query.filter_by(libelle='Marié(e)').first()
            lien_epoux = ReferentielLienParente.query.filter_by(libelle='Époux/Épouse').first()
            
            # Création du militaire de test
            militaire = Personnel(
                matricule="TEST001",
                nom="ALAMI",
                prenom="MOHAMMED",
                nom_ar="العلمي",
                prenom_ar="محمد",
                date_naissance=date(1985, 5, 15),
                lieu_naissance="Rabat",
                genre_id=genre_m.id_genre,
                categorie_id=categorie_off.id_categorie,
                groupe_sanguin_id=groupe_o_plus.id_groupe,
                cin_numero="AB123456",
                cin_date_delivrance=date(2003, 5, 15),
                cin_date_expiration=date(2028, 5, 15),
                gsm="0612345678",
                telephone_domicile="0537123456",
                taille_cm=175,
                lieu_residence="123 Avenue Hassan II, Rabat",
                service_id=service_art.id_service,
                specialite_id=None,
                unite_id=unite_1gar.id_unite,
                grade_actuel_id=grade_cpt.id_grade,
                fonction="Commandant de Compagnie",
                date_prise_fonction=date(2020, 1, 1),
                date_engagement=date(2008, 9, 1),
                ccp_numero="123456789012",
                compte_bancaire_numero="1234567890123456",
                somme_numero="SM12345678",
                nom_pere="HASSAN",
                prenom_pere="AHMED",
                nom_mere="FATIMA",
                prenom_mere="AICHA",
                adresse_parents="456 Rue Al Massira, Casablanca",
                etat_matrimonial_id=etat_marie.id_etat,
                nombre_enfants=2,
                passeport_numero="AB123456",
                passeport_date_delivrance=date(2020, 1, 1),
                passeport_date_expiration=date(2030, 1, 1),
                gsm_urgence="0698765432",
                lien_parente_id=lien_epoux.id_lien
            )
            
            db.session.add(militaire)
            db.session.commit()
            
            print("✅ Militaire de test créé: ALAMI Mohammed (TEST001)")
            return militaire.matricule
            
    except Exception as e:
        print(f"❌ Erreur création militaire: {str(e)}")
        db.session.rollback()
        return None

def creer_donnees_complementaires(matricule):
    """Crée des données complémentaires pour tester toutes les fonctionnalités"""
    try:
        with app.app_context():
            print("📋 Création des données complémentaires...")
            
            # 1. Situation médicale
            situation = SituationMedicale(
                matricule=matricule,
                aptitude="Apte",
                date_derniere_visite=date.today() - timedelta(days=90),
                observations_generales="Situation médicale normale, apte au service"
            )
            db.session.add(situation)
            
            # 2. Historique des grades
            grade_ltn = ReferentielGrade.query.filter_by(code_grade='LTN').first()
            grade_cpt = ReferentielGrade.query.filter_by(code_grade='CPT').first()
            
            # Grade initial (Lieutenant)
            historique1 = HistoriqueGrade(
                matricule=matricule,
                grade_id=grade_ltn.id_grade,
                date_debut=date(2008, 9, 1),
                date_fin=date(2015, 12, 31),
                numero_decision="DEC2008001",
                observations="Grade initial à l'engagement"
            )
            db.session.add(historique1)
            
            # Grade actuel (Capitaine)
            historique2 = HistoriqueGrade(
                matricule=matricule,
                grade_id=grade_cpt.id_grade,
                date_debut=date(2016, 1, 1),
                numero_decision="DEC2016001",
                observations="Promotion au grade de Capitaine"
            )
            db.session.add(historique2)
            
            # 3. Conjoint
            conjoint = Conjoint(
                matricule=matricule,
                nom="BENALI",
                prenom="KHADIJA",
                nom_ar="بن علي",
                prenom_ar="خديجة",
                date_naissance=date(1988, 3, 20),
                lieu_naissance="Casablanca",
                cin_numero="CD789012",
                cin_date_delivrance=date(2006, 3, 20),
                cin_date_expiration=date(2031, 3, 20),
                nom_pere="OMAR",
                prenom_pere="YOUSSEF",
                nom_mere="AMINA",
                prenom_mere="LATIFA",
                profession="Enseignante",
                lieu_travail="École Primaire Al Massira",
                gsm="0687654321",
                date_mariage=date(2012, 6, 15),
                lieu_mariage="Rabat, Maroc"
            )
            db.session.add(conjoint)
            
            # 4. Enfants
            genre_m = ReferentielGenre.query.filter_by(libelle='Masculin').first()
            genre_f = ReferentielGenre.query.filter_by(libelle='Féminin').first()
            
            enfant1 = Enfant(
                matricule=matricule,
                nom="ALAMI",
                prenom="YOUSSEF",
                nom_ar="العلمي",
                prenom_ar="يوسف",
                genre_id=genre_m.id_genre,
                date_naissance=date(2015, 8, 10),
                lieu_naissance="Rabat"
            )
            db.session.add(enfant1)
            
            enfant2 = Enfant(
                matricule=matricule,
                nom="ALAMI",
                prenom="ZINEB",
                nom_ar="العلمي",
                prenom_ar="زينب",
                genre_id=genre_f.id_genre,
                date_naissance=date(2018, 12, 5),
                lieu_naissance="Rabat"
            )
            db.session.add(enfant2)
            
            # 5. Hospitalisation
            hospitalisation = Hospitalisation(
                matricule=matricule,
                date_entree=date(2023, 3, 15),
                date_sortie=date(2023, 3, 18),
                etablissement="Hôpital Militaire Mohammed V",
                motif="Intervention chirurgicale mineure",
                diagnostic="Appendicectomie",
                traitement="Chirurgie laparoscopique",
                observations="Récupération complète"
            )
            db.session.add(hospitalisation)
            
            # 6. Vaccination
            vaccination = Vaccination(
                matricule=matricule,
                nom_vaccin="COVID-19",
                date_vaccination=date(2021, 4, 20),
                numero_lot="CV2021001",
                lieu_vaccination="Infirmerie 1GAR",
                medecin_vaccinateur="Dr. RACHIDI",
                rappel_prevu=date(2024, 4, 20),
                observations="Première dose - Aucune réaction"
            )
            db.session.add(vaccination)
            
            # 7. PTC
            ptc = PTC(
                matricule=matricule,
                date_debut=date(2023, 7, 1),
                date_fin=date(2023, 7, 15),
                duree_jours=15,
                objet="Congé de récupération post-mission",
                lieu_ptc="Domicile",
                numero_decision="PTC2023001",
                observations="Congé accordé après mission opérationnelle"
            )
            db.session.add(ptc)
            
            # 8. Permission
            type_permission = ReferentielTypeAbsence.query.filter_by(libelle='Permission ordinaire').first()
            permission = Permission(
                matricule=matricule,
                type_absence_id=type_permission.id_type,
                date_debut=date(2023, 8, 1),
                date_fin=date(2023, 8, 15),
                duree_jours=15,
                adresse_permission="456 Rue Al Massira, Casablanca",
                numero_serie="PERM2023001",
                motif="Congé annuel",
                observations="Permission estivale"
            )
            db.session.add(permission)
            
            # 9. Mutation
            unite_1gar = ReferentielUnite.query.filter_by(code='1GAR').first()
            unite_2gar = ReferentielUnite.query.filter_by(code='2GAR').first()
            mutation = MutationInterBie(
                matricule=matricule,
                unite_origine_id=unite_2gar.id_unite,
                unite_destination_id=unite_1gar.id_unite,
                fonction_origine="Chef de Section",
                fonction_destination="Commandant de Compagnie",
                date_mutation=date(2020, 1, 1),
                numero_decision="MUT2020001",
                motif="Promotion et nouvelle affectation",
                observations="Mutation avec promotion"
            )
            db.session.add(mutation)
            
            # 10. Langue parlée
            langue_fr = ReferentielLangue.query.filter_by(code_iso='FR').first()
            langue_en = ReferentielLangue.query.filter_by(code_iso='EN').first()
            
            langue1 = PersonnelLangue(
                matricule=matricule,
                langue_id=langue_fr.id_langue,
                niveau="Courant",
                observations="Langue maternelle"
            )
            db.session.add(langue1)
            
            langue2 = PersonnelLangue(
                matricule=matricule,
                langue_id=langue_en.id_langue,
                niveau="Intermédiaire",
                observations="Formation militaire"
            )
            db.session.add(langue2)
            
            db.session.commit()
            print("✅ Données complémentaires créées avec succès!")
            
    except Exception as e:
        print(f"❌ Erreur création données complémentaires: {str(e)}")
        db.session.rollback()

def tester_recherche():
    """Teste la fonctionnalité de recherche"""
    try:
        with app.app_context():
            print("🔍 Test de la recherche...")
            
            # Test recherche par nom
            resultats = Personnel.query.filter(Personnel.nom.like('%ALAMI%')).all()
            print(f"   • Recherche par nom 'ALAMI': {len(resultats)} résultat(s)")
            
            # Test recherche par matricule
            resultats = Personnel.query.filter(Personnel.matricule == 'TEST001').all()
            print(f"   • Recherche par matricule 'TEST001': {len(resultats)} résultat(s)")
            
            # Test recherche par grade
            grade_cpt = ReferentielGrade.query.filter_by(code_grade='CPT').first()
            resultats = Personnel.query.filter(Personnel.grade_actuel_id == grade_cpt.id_grade).all()
            print(f"   • Recherche par grade 'Capitaine': {len(resultats)} résultat(s)")
            
            print("✅ Tests de recherche réussis!")
            
    except Exception as e:
        print(f"❌ Erreur test recherche: {str(e)}")

def verifier_relations():
    """Vérifie que toutes les relations fonctionnent"""
    try:
        with app.app_context():
            print("🔗 Vérification des relations...")
            
            militaire = Personnel.query.filter_by(matricule='TEST001').first()
            if militaire:
                print(f"   • Militaire: {militaire.nom_complet}")
                print(f"   • Grade: {militaire.grade_actuel.libelle if militaire.grade_actuel else 'N/A'}")
                print(f"   • Unité: {militaire.unite.libelle if militaire.unite else 'N/A'}")
                print(f"   • Service: {militaire.service.libelle if militaire.service else 'N/A'}")
                
                # Vérifier les relations
                conjoint = Conjoint.query.filter_by(matricule=militaire.matricule).first()
                enfants = Enfant.query.filter_by(matricule=militaire.matricule).all()
                situation = SituationMedicale.query.filter_by(matricule=militaire.matricule).first()
                
                print(f"   • Conjoint: {'Oui' if conjoint else 'Non'}")
                print(f"   • Enfants: {len(enfants)}")
                print(f"   • Situation médicale: {'Oui' if situation else 'Non'}")
                
                print("✅ Relations vérifiées!")
            else:
                print("❌ Militaire de test non trouvé!")
                
    except Exception as e:
        print(f"❌ Erreur vérification relations: {str(e)}")

def main():
    """Fonction principale de test"""
    print("🧪 TEST COMPLET DES FONCTIONNALITÉS RH")
    print("=" * 60)
    
    # 1. Créer un militaire de test
    matricule = creer_militaire_test()
    if not matricule:
        print("❌ Impossible de créer le militaire de test")
        return
    
    # 2. Créer les données complémentaires
    creer_donnees_complementaires(matricule)
    
    # 3. Tester la recherche
    tester_recherche()
    
    # 4. Vérifier les relations
    verifier_relations()
    
    print("\n🎉 TESTS TERMINÉS!")
    print("🔗 Testez maintenant l'interface:")
    print(f"   • Dashboard: http://localhost:3000/rh")
    print(f"   • Recherche: http://localhost:3000/rh/recherche")
    print(f"   • Fiche complète: http://localhost:3000/rh/personnel/TEST001")
    print(f"   • Nouveau militaire: http://localhost:3000/rh/nouveau_militaire")

if __name__ == "__main__":
    main()
