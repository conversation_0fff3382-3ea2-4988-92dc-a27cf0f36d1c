{% extends "stages/base_stages.html" %}

{% block title %}Ajouter une Notification{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Ajouter une Notification</h1>
    </div>
    <div class="card">
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <label for="stagiaire" class="form-label">Stagiaire</label>
                    <select class="form-select" id="stagiaire" name="stagiaire" required>
                        <option value="">Sélectionner...</option>
                        {% for s in stagiaires %}
                            <option value="{{ s.id_stagiaire }}">{{ s.nom }} {{ s.prenom }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label for="contenu" class="form-label">Contenu de la notification</label>
                    <textarea class="form-control" id="contenu" name="contenu" rows="3" required></textarea>
                </div>
                <hr>
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('liste_notifications') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 