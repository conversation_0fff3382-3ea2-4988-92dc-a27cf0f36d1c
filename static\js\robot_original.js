// Modèle 3D original du robot
// Sauvegarde du code pour restauration ultérieure

let scene, camera, renderer, robot;
const ROTATION_SPEED = 0.01;

function initScene() {
    scene = new THREE.Scene();
    scene.background = null;

    camera = new THREE.PerspectiveCamera(45, 300/400, 0.1, 1000);
    camera.position.set(0, 0.5, 6);
    camera.lookAt(0, 0, 0);

    const container = document.getElementById('ai-agent-container');
    renderer = new THREE.WebGLRenderer({ 
        antialias: true, 
        alpha: true,
        preserveDrawingBuffer: false
    });
    renderer.setSize(300, 400);
    renderer.setClearColor(0x000000, 0);
    renderer.setPixelRatio(window.devicePixelRatio);
    container.appendChild(renderer.domElement);

    // Éclairage optimisé
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(1, 2, 3);
    scene.add(directionalLight);

    // Création du robot
    const head = new THREE.Group();

    // Corps du robot avec une meilleure échelle
    const bodyGeometry = new THREE.CylinderGeometry(0.5, 0.7, 1.8, 8);
    const bodyMaterial = new THREE.MeshPhongMaterial({
        color: 0x4B5320, // Vert militaire
        shininess: 70,
        specular: 0x6B8E23,
        transparent: true,
        opacity: 0.9
    });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    head.add(body);

    // Tête ajustée
    const headGeometry = new THREE.SphereGeometry(0.5, 16, 16);
    const headMaterial = new THREE.MeshPhongMaterial({
        color: 0x6B8E23, // Vert olive
        shininess: 100,
        specular: 0xA9A9A9,
        transparent: true,
        opacity: 0.9
    });
    const headMesh = new THREE.Mesh(headGeometry, headMaterial);
    headMesh.position.y = 1.2;
    head.add(headMesh);

    // Yeux
    const eyeGeometry = new THREE.SphereGeometry(0.1, 16, 16);
    const eyeMaterial = new THREE.MeshPhongMaterial({
        color: 0x00FF00,
        emissive: 0x00FF00,
        shininess: 100
    });

    // Ajuster la position des yeux
    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(-0.25, 1.4, 0.4);
    head.add(leftEye);

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(0.25, 1.4, 0.4);
    head.add(rightEye);

    // Antennes
    const antennaGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.3, 8);
    const antennaMaterial = new THREE.MeshPhongMaterial({
        color: 0x2F4F4F,
        shininess: 30
    });

    // Ajuster les antennes
    const leftAntenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
    leftAntenna.position.set(-0.3, 2.0, 0);
    leftAntenna.rotation.x = Math.PI / 6;
    head.add(leftAntenna);

    const rightAntenna = new THREE.Mesh(antennaGeometry, antennaMaterial);
    rightAntenna.position.set(0.3, 2.0, 0);
    rightAntenna.rotation.x = -Math.PI / 6;
    head.add(rightAntenna);

    // Épaules plus larges
    const shoulderGeometry = new THREE.BoxGeometry(1.8, 0.4, 0.4);
    const shoulderMaterial = new THREE.MeshPhongMaterial({
        color: 0x4B5320,
        shininess: 50
    });
    const shoulders = new THREE.Mesh(shoulderGeometry, shoulderMaterial);
    shoulders.position.y = 0.5;
    head.add(shoulders);

    // Position globale du robot ajustée
    head.scale.set(1.3, 1.3, 1.3);
    head.position.y = -0.5;
    head.rotation.x = 0.1;
    robot = head;
    scene.add(robot);

    animate();
}

function animate() {
    requestAnimationFrame(animate);
    
    if (robot) {
        robot.rotation.y += ROTATION_SPEED * 0.8;
        
        // Animation de flottement plus subtile
        robot.position.y = -0.5 + Math.sin(Date.now() * 0.0008) * 0.1;
    }
    
    renderer.render(scene, camera);
}

function animateRobot(type) {
    if (!robot) return;
    
    switch(type) {
        case 'thinking':
            // Animation de réflexion - inclinaison de la tête
            gsap.to(robot.rotation, {
                x: 0.2,
                duration: 0.5,
                yoyo: true,
                repeat: 2
            });
            break;
            
        case 'speaking':
            // Animation de parole - léger rebond
            gsap.to(robot.position, {
                y: 0.3,
                duration: 0.2,
                yoyo: true,
                repeat: 1
            });
            break;
            
        case 'greeting':
            // Animation de salutation - rotation complète
            gsap.to(robot.rotation, {
                y: robot.rotation.y + Math.PI * 2,
                duration: 1,
                ease: "power1.inOut"
            });
            break;
    }
}
