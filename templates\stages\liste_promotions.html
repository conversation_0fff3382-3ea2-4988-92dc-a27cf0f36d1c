{% extends "stages/base_stages.html" %}

{% block title %}Liste des Promotions{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Gestion des Promotions</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('ajouter_promotion') }}" class="btn btn-lg btn-gradient-primary shadow-lg position-relative d-flex align-items-center px-4 py-2" style="background: linear-gradient(90deg, #007bff 0%, #00c6ff 100%); color: #fff; border: none; transition: transform 0.2s, box-shadow 0.2s; font-size: 1.2rem;" data-bs-toggle="tooltip" data-bs-placement="left" title="Créer une nouvelle promotion">
                <span class="me-2 animate__animated animate__tada animate__infinite infinite">
                    <i class="fas fa-plus-circle fa-lg"></i>
                </span>
                <span>Ajouter une Promotion</span>
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">Nom de la Promotion</th>
                            <th scope="col">Année</th>
                            <th scope="col">Filière</th>
                            <th scope="col" class="text-center">Nombre de Stagiaires</th>
                            <th scope="col" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for promotion in promotions %}
                        <tr>
                            <td>{{ promotion.nom }}</td>
                            <td>{{ promotion.annee }}</td>
                            <td>{{ promotion.filiere if promotion.filiere else 'N/A' }}</td>
                            <td class="text-center">{{ promotion.inscriptions|length }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('details_promotion', id=promotion.id_promotion) }}" class="btn btn-info btn-sm" title="Voir les stagiaires">
                                    <i class="fas fa-users"></i>
                                </a>
                                <a href="{{ url_for('modifier_promotion', id=promotion.id_promotion) }}" class="btn btn-warning btn-sm" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ url_for('supprimer_promotion', id=promotion.id_promotion) }}" method="post" style="display:inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?');">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="5" class="text-center">Aucune promotion trouvée.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });
    var btn = document.querySelector('.btn-gradient-primary');
    if(btn) {
        btn.addEventListener('mouseenter', function() {
            btn.style.transform = 'scale(1.06)';
            btn.style.boxShadow = '0 8px 24px rgba(0, 198, 255, 0.25)';
        });
        btn.addEventListener('mouseleave', function() {
            btn.style.transform = 'scale(1)';
            btn.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.15)';
        });
    }
});
</script>
{% endblock %} 