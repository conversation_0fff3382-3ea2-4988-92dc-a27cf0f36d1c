from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_from_directory, send_file
from datetime import datetime, timedelta
from sqlalchemy import or_
import random  # Ajout de l'import random
from functools import wraps
import os
from werkzeug.utils import secure_filename
import io
import base64
import pandas as pd
from random import choice, randint
from datetime import date, timedelta
from fpdf import FPDF

# Import des blueprints
from stages import stages_bp
from rh_blueprint import rh_bp

# Configuration de l'upload de fichiers
UPLOAD_FOLDER = 'uploads/documents'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.secret_key = 'votre_cle_secrete_ici'  # Nécessaire pour les messages flash et la session

# Enregistrement des blueprints
app.register_blueprint(stages_bp)
app.register_blueprint(rh_bp)

# Filtre Jinja2 personnalisé pour l'encodage Base64
@app.template_filter('b64encode')
def b64encode_filter(data):
    if data:
        return base64.b64encode(data).decode('utf-8')
    return None

# Import et initialisation de la base de données
from db import db, init_app, VehiculeGAR, Entretien, CourrierArrive, CourrierEnvoye, CourrierDivisionAction, CourrierDivisionInfo, Courrier
from rh_models import *
init_app(app)

# Définition des modèles de base de données
class Vehicule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    type_vehicule = db.Column(db.String(20), nullable=False)  # VL, PL, Engin chenillé, SA
    marque = db.Column(db.String(50), nullable=False)
    matricule = db.Column(db.String(50), nullable=False, unique=True)  # Anciennement 'modele', maintenant avec contrainte d'unicité
    type_panne = db.Column(db.String(100), nullable=False)
    date_panne = db.Column(db.Date, nullable=False)
    description = db.Column(db.Text, nullable=True)
    statut = db.Column(db.String(20), default='En panne')
    unite = db.Column(db.String(20), nullable=True)  # Unité militaire (13GAR, 14GAR, etc.)

    def to_dict(self):
        return {
            'id': self.id,
            'type_vehicule': self.type_vehicule,
            'marque': self.marque,
            'modele': self.matricule,  # Pour maintenir la compatibilité avec le code existant
            'matricule': self.matricule,  # Nouvelle clé
            'type_panne': self.type_panne,
            'date_panne': self.date_panne.strftime('%Y-%m-%d'),
            'description': self.description,
            'statut': self.statut,
            'unite': self.unite
        }

class VehiculeHistorique(db.Model):
    __tablename__ = 'vehicule_historique'

    id = db.Column(db.Integer, primary_key=True)
    vehicule_id = db.Column(db.Integer, db.ForeignKey('vehicule.id', ondelete='CASCADE'), nullable=False)
    date_changement = db.Column(db.DateTime, nullable=False, default=datetime.now)
    statut = db.Column(db.String(20), nullable=False)
    type_panne = db.Column(db.String(100), nullable=True)
    date_panne = db.Column(db.Date, nullable=True)
    description = db.Column(db.Text, nullable=True)

    # Relation avec le véhicule
    vehicule = db.relationship('Vehicule', backref=db.backref('historique', lazy=True, cascade="all, delete-orphan"))

    def to_dict(self):
        return {
            'id': self.id,
            'vehicule_id': self.vehicule_id,
            'date_changement': self.date_changement.strftime('%Y-%m-%d %H:%M:%S'),
            'date_panne': self.date_panne.strftime('%Y-%m-%d') if self.date_panne else None,
            'type_panne': self.type_panne,
            'statut': self.statut,
            'description': self.description
        }





# Types de véhicules et leurs marques
TYPES_VEHICULES = {
    'VL': ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan'],
    'PL': ['JBD', 'Kaiser', 'Renault'],
    'Engin chenillé': ['M109 155mm', 'M110 203mm', 'Vulcan'],
    'SA': ['PULS', 'HIMARS', 'CESAR']
}

# Types de pannes mécaniques par type de véhicule
TYPES_PANNES = {
    'VL': [
        # Moteur
        'Fuite d\'huile moteur',
        'Surchauffe moteur',
        'Défaillance du système d\'injection',
        'Problème de démarrage',
        'Courroie de distribution cassée',
        'Joint de culasse défectueux',
        'Perte de puissance moteur',
        'Consommation excessive d\'huile',
        'Bruit anormal du moteur',
        'Fumée blanche à l\'échappement',
        'Fumée noire à l\'échappement',

        # Transmission
        'Embrayage défectueux',
        'Problème de transmission',
        'Boîte de vitesses bloquée',
        'Difficulté à passer les vitesses',
        'Bruit anormal de la transmission',
        'Fuite d\'huile de transmission',
        'Usure prématurée de l\'embrayage',

        # Freinage
        'Défaillance du système de freinage',
        'Usure des plaquettes de frein',
        'Disques de frein voilés',
        'Fuite de liquide de frein',
        'ABS défectueux',
        'Frein à main défaillant',

        # Direction et suspension
        'Problème de direction assistée',
        'Amortisseurs usés',
        'Rotules de suspension défectueuses',
        'Barre stabilisatrice cassée',
        'Craquements dans la suspension',
        'Alignement des roues incorrect',

        # Électrique
        'Batterie déchargée',
        'Alternateur défectueux',
        'Démarreur défaillant',
        'Court-circuit électrique',
        'Problème de faisceau électrique',
        'Défaillance des phares tactiques',
        'Système radio défectueux',

        # Carburant
        'Pompe à carburant défaillante',
        'Filtre à carburant colmaté',
        'Fuite du système d\'alimentation',
        'Injecteurs encrassés',
        'Problème de régulation de pression'
    ],

    'PL': [
        # Moteur
        'Défaillance du turbocompresseur',
        'Fuite du circuit de refroidissement',
        'Problème de suralimentation',
        'Joint de culasse défectueux',
        'Surchauffe moteur diesel',
        'Fuite d\'huile moteur',
        'Perte de puissance moteur',
        'Consommation excessive de carburant',
        'Bruit anormal du bloc moteur',
        'Fumée excessive à l\'échappement',
        'Défaillance du système EGR',
        'Problème de démarrage à froid',

        # Transmission
        'Problème de boîte de vitesses',
        'Synchroniseur usé',
        'Embrayage hydraulique défectueux',
        'Fuite d\'huile de transmission',
        'Arbre de transmission endommagé',
        'Différentiel bloqué',
        'Bruit anormal de la transmission',

        # Système pneumatique
        'Défaillance du système pneumatique',
        'Compresseur d\'air défectueux',
        'Fuite d\'air dans le circuit',
        'Valve pneumatique bloquée',
        'Sécheur d\'air défaillant',
        'Pression d\'air insuffisante',

        # Freinage
        'Défaillance du système de freinage',
        'Usure des tambours de frein',
        'Défaillance du frein moteur',
        'Fuite du circuit de freinage',
        'Réglage incorrect des freins',
        'Défaillance du ralentisseur',

        # Direction et suspension
        'Problème de direction assistée',
        'Problème de suspension renforcée',
        'Lames de ressort cassées',
        'Amortisseurs défectueux',
        'Barre stabilisatrice endommagée',
        'Coussinets de suspension usés',

        # Hydraulique
        'Fuite du circuit hydraulique',
        'Pompe hydraulique défaillante',
        'Vérin hydraulique endommagé',
        'Pression hydraulique insuffisante',
        'Soupape de décharge bloquée',

        # Électrique
        'Défaillance du système électrique',
        'Alternateur haute capacité défectueux',
        'Problème de démarreur renforcé',
        'Court-circuit dans le faisceau',
        'Défaillance du tableau de bord',
        'Problème de communication CAN'
    ],

    'Engin chenillé': [
        # Système de chenilles
        'Défaillance du système de chenilles',
        'Patins de chenille endommagés',
        'Tension incorrecte des chenilles',
        'Barbotin usé ou endommagé',
        'Galets de roulement défectueux',
        'Roue tendeuse bloquée',
        'Désalignement des chenilles',
        'Bruit anormal du train de roulement',
        'Usure excessive des maillons',

        # Moteur
        'Défaillance du moteur diesel lourd',
        'Surchauffe du bloc moteur blindé',
        'Problème d\'alimentation carburant haute pression',
        'Défaillance du système de refroidissement renforcé',
        'Fuite d\'huile moteur haute performance',
        'Problème de démarrage en conditions extrêmes',
        'Défaillance du turbocompresseur blindé',
        'Perte de puissance moteur',

        # Transmission blindée
        'Problème de transmission blindée',
        'Défaillance du convertisseur de couple',
        'Surchauffe de la boîte de transfert',
        'Fuite d\'huile de transmission',
        'Défaillance des embrayages multidisques',
        'Problème de changement de rapport',
        'Bruit anormal de la transmission',

        # Direction
        'Problème de direction différentielle',
        'Défaillance du système de freinage directionnel',
        'Fuite du circuit hydraulique de direction',
        'Défaillance des leviers de direction',
        'Problème de pivotement sur place',

        # Hydraulique
        'Défaillance du système hydraulique',
        'Pompe hydraulique principale défectueuse',
        'Fuite des vérins hydrauliques',
        'Surchauffe du fluide hydraulique',
        'Pression hydraulique insuffisante',
        'Contamination du circuit hydraulique',

        # Électrique et électronique
        'Défaillance du système électrique blindé',
        'Problème de batterie haute capacité',
        'Court-circuit dans le système électrique protégé',
        'Défaillance du système de communication interne',
        'Problème d\'alimentation des systèmes électroniques',
        'Défaillance des capteurs de position',

        # Refroidissement
        'Problème de refroidissement',
        'Radiateur blindé obstrué',
        'Pompe à eau défectueuse',
        'Ventilateur de refroidissement endommagé',
        'Thermostat bloqué',
        'Fuite du circuit de refroidissement'
    ],

    'SA': [
        # Système de recul
        'Défaillance du système de recul hydraulique',
        'Vérins de recul endommagés',
        'Fuite du circuit hydraulique de recul',
        'Amortisseurs de recul défectueux',
        'Récupérateurs hydrauliques défaillants',
        'Problème de synchronisation du recul',

        # Système d\'élévation
        'Problème du mécanisme d\'élévation',
        'Défaillance des vérins d\'élévation',
        'Fuite hydraulique du système d\'élévation',
        'Blocage du mécanisme d\'élévation',
        'Usure des engrenages d\'élévation',
        'Problème de verrouillage en position',

        # Stabilisation
        'Défaillance du système de stabilisation',
        'Vérins stabilisateurs endommagés',
        'Capteurs de niveau défectueux',
        'Problème de verrouillage de position',
        'Défaillance des béquilles hydrauliques',
        'Instabilité en position de tir',

        # Pointage
        'Défaillance du système de pointage',
        'Problème de précision du pointage',
        'Défaillance des moteurs de pointage',
        'Blocage du système de visée',
        'Défaillance des encodeurs de position',
        'Problème de calibration du pointage',

        # Rotation
        'Défaillance du système de rotation',
        'Problème de la couronne d\'orientation',
        'Moteur de rotation défectueux',
        'Blocage du système de rotation',
        'Bruit anormal lors de la rotation',
        'Usure des roulements de rotation',

        # Électrique et électronique
        'Problème de circuit électrique spécialisé',
        'Défaillance du système de contrôle de tir',
        'Problème d\'alimentation des systèmes électroniques',
        'Défaillance des calculateurs balistiques',
        'Court-circuit dans le système électrique',
        'Défaillance des capteurs de position',

        # Hydraulique
        'Défaillance de la centrale hydraulique',
        'Fuite du circuit hydraulique principal',
        'Surchauffe du fluide hydraulique',
        'Contamination du circuit hydraulique',
        'Pression hydraulique insuffisante',
        'Défaillance des distributeurs hydrauliques',

        # Mécanique générale
        'Problème de culasse mobile',
        'Défaillance du système de chargement',
        'Usure des joints d\'étanchéité',
        'Défaillance du système de refroidissement',
        'Problème de verrouillage de sécurité'
    ]
}

@app.context_processor
def inject_now():
    return {'now': datetime.now()}

def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Accepter n'importe quelles credentials
        if username and password:
            session['logged_in'] = True
            session['username'] = username
            flash('Connexion réussie !', 'success')
            return redirect(url_for('liste_vehicules'))
        else:
            flash('Veuillez remplir tous les champs !', 'danger')
    
    return render_template('login.html')

@app.route('/login_qr', methods=['POST'])
def login_qr():
    # Récupérer les données du code QR
    data = request.get_json()
    qr_data = data.get('qr_data', '')

    # Code QR unique autorisé pour l'utilisateur redouane
    # Ce code est spécifique et ne doit pas être modifié
    authorized_qr_code = "redouane_FAR_auth_7a9b3c2d1e"

    # Vérifier si le QR code correspond exactement au code autorisé
    if qr_data and qr_data == authorized_qr_code:
        # Authentifier en tant que redouane
        username = "redouane"

        # Initialiser la session avec l'utilisateur redouane
        session['logged_in'] = True
        session['username'] = username

        # Enregistrer dans les logs pour le suivi
        app.logger.info(f"Connexion réussie via QR code pour l'utilisateur {username}")

        return jsonify({
            'success': True,
            'message': 'Authentification réussie'
        })
    else:
        # Rejeter tout autre QR code
        app.logger.warning(f"Tentative de connexion avec QR code non autorisé: {qr_data}")

        return jsonify({
            'success': False,
            'message': 'Code QR non autorisé. Veuillez utiliser uniquement le QR code officiel.'
        })



@app.route('/login_facial', methods=['POST'])
def login_facial():
    # Récupérer les données de reconnaissance faciale
    data = request.get_json()
    facial_data = data.get('facial_data', '')

    # Pour cette implémentation, nous acceptons n'importe quel visage
    # Dans une implémentation réelle, vous vérifieriez les données biométriques

    if facial_data:
        # Authentifier en tant que "utilisateur X"
        username = "utilisateur X"

        # Initialiser la session
        session['logged_in'] = True
        session['username'] = username

        return jsonify({
            'success': True,
            'message': 'Authentification faciale réussie'
        })
    else:
        # Rejeter si aucune donnée faciale n'est fournie
        return jsonify({
            'success': False,
            'message': 'Données faciales manquantes'
        })

@app.route('/logout')
def logout():
    session.clear()
    flash('Vous avez été déconnecté', 'info')
    return redirect(url_for('index'))

@app.route('/')
def index():
    return render_template('dashboard.html')

@app.route('/dashboard')
@login_required
def dashboard():
    # Statistiques de base
    total_vehicules = Vehicule.query.count()
    vehicules_vl = Vehicule.query.filter_by(type_vehicule='VL').count()
    vehicules_pl = Vehicule.query.filter_by(type_vehicule='PL').count()
    vehicules_sa = Vehicule.query.filter_by(type_vehicule='SA').count()
    vehicules_chenilles = Vehicule.query.filter_by(type_vehicule='Engin chenillé').count()

    # Statistiques par unité (GAR)
    gar_stats = {}
    gar_operational = {}
    gar_list = []  # Liste pour stocker les GAR dans l'ordre
    total_pannes = Vehicule.query.filter_by(statut='En panne').count()  # Total des pannes toutes unités confondues

    # Récupérer les unités distinctes dans la base de données
    unites_distinctes = db.session.query(VehiculeGAR.unite).distinct().all()
    
    # Trier les unités par numéro de GAR
    unites_triees = []
    for unite in unites_distinctes:
        unite_nom = unite[0]
        if unite_nom:
            # Extraire le numéro du GAR
            try:
                # Utiliser une expression régulière pour extraire le numéro
                import re
                match = re.search(r'(\d+)GAR', unite_nom)
                if match:
                    numero_gar = int(match.group(1))
                    unites_triees.append((numero_gar, unite_nom))
                else:
                    # Si ce n'est pas un GAR numéroté, mettre à la fin
                    unites_triees.append((999, unite_nom))
            except ValueError:
                # Si ce n'est pas un GAR numéroté, mettre à la fin
                unites_triees.append((999, unite_nom))
    
    # Trier par numéro de GAR (tri numérique)
    unites_triees.sort(key=lambda x: x[0])
    
    # Créer une liste ordonnée des GAR
    for numero_gar, unite_nom in unites_triees:
        # Compter tous les véhicules de cette unité dans VehiculeGAR
        total_unite = VehiculeGAR.query.filter_by(unite=unite_nom).count()
        if total_unite > 0:
            gar_stats[unite_nom] = total_unite

            # Compter uniquement les véhicules avec le statut 'En panne' pour cette unité
            vehicules_en_panne = Vehicule.query.filter_by(unite=unite_nom, statut='En panne').count()

            # Si aucun véhicule n'est en panne, le degré opérationnel est de 100%
            if vehicules_en_panne == 0:
                gar_operational[unite_nom] = 100.0
            # Si tous les véhicules sont en panne, le degré opérationnel est de 0%
            elif vehicules_en_panne == total_unite:
                gar_operational[unite_nom] = 0.0
            # Sinon, calculer le pourcentage de véhicules opérationnels
            else:
                vehicules_operationnels = total_unite - vehicules_en_panne
                pourcentage = (vehicules_operationnels / total_unite) * 100
                gar_operational[unite_nom] = round(pourcentage, 1)

            # Ne pas ajouter les GAR sans pannes à la liste
            if vehicules_en_panne > 0:
                # Calculer le pourcentage de pannes par rapport au total des pannes
                pourcentage_pannes = (vehicules_en_panne / total_pannes * 100) if total_pannes > 0 else 0
                
                gar_list.append({
                    'nom': unite_nom,
                    'total': total_unite,
                    'en_panne': vehicules_en_panne,
                    'operationnel': gar_operational[unite_nom],
                    'pourcentage_pannes': round(pourcentage_pannes, 1)  # Arrondi à 1 décimale
                })

    # Statistiques par marque
    marques_stats = {}
    for type_vehicule, marques in TYPES_VEHICULES.items():
        for marque in marques:
            count = Vehicule.query.filter_by(marque=marque).count()
            if count > 0:
                marques_stats[marque] = count

    # Statistiques par statut
    statuts_stats = {}
    statuts = ['En panne', 'Indisponible', 'En réparation', 'Réparé']
    for statut in statuts:
        count = Vehicule.query.filter_by(statut=statut).count()
        if count > 0:
            statuts_stats[statut] = count

    return render_template('dashboard_vehicules.html',
                         total_vehicules=total_vehicules,
                         vehicules_vl=vehicules_vl,
                         vehicules_pl=vehicules_pl,
                         vehicules_sa=vehicules_sa,
                         vehicules_chenilles=vehicules_chenilles,
                         gar_stats=gar_stats,
                         gar_operational=gar_operational,
                         gar_list=gar_list,  # Passer la liste triée au template
                         marques_stats=marques_stats,
                         statuts_stats=statuts_stats)

@app.route('/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter():
    if request.method == 'POST':
        # Récupération des données du formulaire
        type_vehicule = request.form.get('type_vehicule')
        marque = request.form.get('marque')
        matricule = request.form.get('modele')  # Le champ du formulaire est toujours 'modele'
        type_panne = request.form.get('type_panne')
        date_panne = request.form.get('date_panne')
        description = request.form.get('description', '')
        unite = request.form.get('unite')
        statut = request.form.get('statut', 'En panne')

        # Validation des données
        if not all([type_vehicule, marque, matricule, type_panne, date_panne, statut]):
            flash('Tous les champs obligatoires doivent être remplis.', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si le matricule existe dans la table vehicule_gar
        vehicule_gar = VehiculeGAR.query.filter_by(matricule=matricule).first()
        if not vehicule_gar:
            flash(f'Erreur : Le matricule "{matricule}" n\'existe pas dans la liste des véhicules GAR.', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si le type de véhicule correspond
        if vehicule_gar.type_vehicule != type_vehicule:
            flash(f'Erreur : Le matricule "{matricule}" est attribué à un véhicule de type "{vehicule_gar.type_vehicule}". Impossible de l\'enregistrer comme "{type_vehicule}".', 'danger')
            return redirect(url_for('ajouter'))

        # Vérifier si "Autre (précisez)" est sélectionné et qu'un type de panne personnalisé est fourni
        if type_panne == 'autre':
            custom_panne = request.form.get('custom_panne', '').strip()
            if not custom_panne:
                flash('Veuillez spécifier le type de panne personnalisé.', 'danger')
                return redirect(url_for('ajouter'))
            type_panne = custom_panne
        
        # Vérifier si le même type de panne existe déjà pour ce véhicule
        panne_existante = Vehicule.query.filter_by(matricule=matricule, type_panne=type_panne).first()
        if panne_existante:
            flash(f'Erreur : Une panne de type "{type_panne}" est déjà enregistrée pour le véhicule avec matricule "{matricule}".', 'danger')
            return redirect(url_for('ajouter'))

        # Utiliser les informations du véhicule GAR
        type_vehicule = vehicule_gar.type_vehicule
        marque = vehicule_gar.marque
        unite = vehicule_gar.unite

        # Création du nouveau véhicule
        nouveau_vehicule = Vehicule(
            type_vehicule=type_vehicule,
            marque=marque,
            matricule=matricule,
            type_panne=type_panne,
            date_panne=datetime.strptime(date_panne, '%Y-%m-%d').date(),
            description=description,
            statut=statut,
            unite=unite
        )

        # Ajout à la base de données
        db.session.add(nouveau_vehicule)
        db.session.commit()

        # Créer une entrée dans l'historique pour l'état initial avec la date de panne
        try:
            # Utiliser la date de panne comme date de changement pour l'entrée initiale
            date_panne_obj = datetime.strptime(date_panne, '%Y-%m-%d').date()

            historique = VehiculeHistorique(
                vehicule_id=nouveau_vehicule.id,
                statut='En panne',
                type_panne=nouveau_vehicule.type_panne,
                date_panne=nouveau_vehicule.date_panne,
                date_changement=datetime.combine(date_panne_obj, datetime.now().time()),
                description=nouveau_vehicule.description if nouveau_vehicule.description else f'Véhicule ajouté avec panne : {nouveau_vehicule.type_panne}'
            )
            db.session.add(historique)
            db.session.commit()
            print(f"Historique créé pour le véhicule {nouveau_vehicule.id}")
        except Exception as e:
            print(f"Erreur lors de la création de l'historique: {e}")

        flash(f'Nouvelle panne ajoutée avec succès pour le véhicule avec matricule "{matricule}" !', 'success')
        return redirect(url_for('liste_vehicules'))

    return render_template('ajouter.html', types_vehicules=TYPES_VEHICULES, types_pannes=TYPES_PANNES)

@app.route('/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier(id):
    vehicule = Vehicule.query.get_or_404(id)

    if request.method == 'POST':
        # Récupérer l'ancien statut avant la modification
        ancien_statut = vehicule.statut

        # Récupérer le nouveau statut et la date de modification
        nouveau_statut = request.form.get('statut')
        date_modification_str = request.form.get('date_modification')

        # Validation des données
        if not all([nouveau_statut, date_modification_str]):
            flash('Tous les champs obligatoires doivent être remplis.', 'danger')
            return redirect(url_for('modifier', id=id))

        # Convertir la date de modification en objet date
        try:
            date_modification = datetime.strptime(date_modification_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Format de date invalide.', 'danger')
            return redirect(url_for('modifier', id=id))

        # Mettre à jour uniquement le statut du véhicule
        vehicule.statut = nouveau_statut

        # Vérifier si le statut a changé
        if ancien_statut != nouveau_statut:
            try:
                # Créer une entrée dans l'historique avec les détails de la panne et la date de modification saisie
                historique = VehiculeHistorique(
                    vehicule_id=vehicule.id,
                    statut=nouveau_statut,
                    type_panne=vehicule.type_panne,
                    date_panne=vehicule.date_panne,
                    date_changement=datetime.combine(date_modification, datetime.now().time()),  # Utiliser la date saisie avec l'heure actuelle
                    description=request.form.get('description', '') if request.form.get('description', '') else f'Changement de statut : {nouveau_statut}'
                )
                db.session.add(historique)
                db.session.commit()
                print(f"Historique créé pour le changement de statut du véhicule {vehicule.id}")
            except Exception as e:
                print(f"Erreur lors de la création de l'historique: {e}")
                # Ne pas bloquer la modification du véhicule si l'historique échoue

        # Mise à jour dans la base de données
        db.session.commit()

        # Message de succès adapté au type de statut
        status_messages = {
            'Réparé': f'Véhicule marqué comme réparé le {date_modification.strftime("%d/%m/%Y")} !',
            'Indisponible': f'Véhicule marqué comme indisponible le {date_modification.strftime("%d/%m/%Y")} !',
            'En réparation': f'Véhicule mis en réparation le {date_modification.strftime("%d/%m/%Y")} !',
            'En panne': f'Statut du véhicule mis à jour le {date_modification.strftime("%d/%m/%Y")} !'
        }

        flash(status_messages.get(nouveau_statut, 'Statut du véhicule mis à jour avec succès !'), 'success')
        return redirect(url_for('liste_vehicules'))

    # Passer la date actuelle au template
    now = datetime.now()

    return render_template('modifier.html',
                          vehicule=vehicule,
                          types_vehicules=TYPES_VEHICULES,
                          types_pannes=TYPES_PANNES,
                          now=now)

@app.route('/supprimer/<int:id>', methods=['GET', 'POST'])
@login_required
def supprimer(id):
    vehicule = Vehicule.query.get_or_404(id)
    return render_template('supprimer.html', vehicule=vehicule)

@app.route('/gestion_stages')
def gestion_stages():
    return redirect(url_for('dashboard_stages'))

@app.route('/gestion_mcpo')
def gestion_mcpo():
    return render_template('dashboard.html', message="Module de Gestion MCPO en cours de développement")

@app.route('/gestion_rh')
def gestion_rh():
    """Route pour gestion RH - Redirection vers la recherche de personnel"""
    return redirect(url_for('rh.recherche_personnel'))



@app.route('/gestion_courrier')
def gestion_courrier():
    return render_template('courrier/index.html')

@app.route('/courrier/arrives')
def courrier_arrives():
    return render_template('courrier/arrives.html')

@app.route('/courrier/envoyes')
def courrier_envoyes():
    return render_template('courrier/envoyes.html')

@app.route('/courrier_vehicules')
def courrier_vehicules():
    return render_template('courrier_vehicules.html')



@app.route('/diagnostic')
def diagnostic():
    """Page de diagnostic pour déboguer l'interface"""
    return send_from_directory('.', 'diagnostic_interface.html')

@app.route('/test_simple')
def test_simple():
    """Page de test simple pour déboguer l'interface"""
    return render_template('courrier/test_simple.html')

# ===================================================================
# ROUTES API POUR LA GESTION DES COURRIERS
# ===================================================================

@app.route('/api/courriers', methods=['GET'])
def api_get_courriers():
    courriers = Courrier.query.order_by(Courrier.date_courrier.desc()).all()
    return jsonify([c.to_dict() for c in courriers])

@app.route('/api/courriers/envoyes', methods=['GET'])
def api_get_courriers_envoyes():
    """Récupérer tous les courriers envoyés"""
    try:
        courriers = CourrierEnvoye.query.order_by(CourrierEnvoye.date_depart.desc()).all()
        return jsonify([courrier.to_dict() for courrier in courriers])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/courriers/envoyes', methods=['POST'])
def api_add_courrier_envoye():
    """Ajouter un nouveau courrier envoyé"""
    try:
        data = request.get_json()

        # Vérifier si le courrier existe déjà
        if CourrierEnvoye.query.filter_by(id_courrier=data['id']).first():
            return jsonify({'error': 'Un courrier avec cet ID existe déjà'}), 400

        # Créer le nouveau courrier
        courrier = CourrierEnvoye(
            id_courrier=data['id'],
            numero_ecrit=data['numero_ecrit'],
            division_emettrice=data['division_emettrice'],
            date_depart=datetime.strptime(data['date_depart'], '%Y-%m-%d').date(),
            nature=data['nature'],
            objet=data['objet'],
            destinataire=data['destinataire'],
            observations=data.get('observations')
        )

        db.session.add(courrier)
        db.session.commit()
        return jsonify(courrier.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/api/courriers/arrives', methods=['GET'])
def api_get_courriers_arrives():
    """Récupérer tous les courriers arrivés"""
    try:
        courriers = CourrierArrive.query.order_by(CourrierArrive.date_arrivee.desc()).all()
        return jsonify([courrier.to_dict() for courrier in courriers])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/courriers/arrives', methods=['POST'])
def api_add_courrier_arrive():
    """Ajouter un nouveau courrier arrivé"""
    try:
        data = request.get_json()

        # Vérifier si le courrier existe déjà
        if CourrierArrive.query.filter_by(id_courrier=data['id']).first():
            return jsonify({'error': 'Un courrier avec cet ID existe déjà'}), 400

        # Créer le nouveau courrier
        courrier = CourrierArrive(
            id_courrier=data['id'],
            urgence=data['urgence'],
            nature=data['nature'],
            date_arrivee=datetime.strptime(data['date_arrivee'], '%Y-%m-%d').date(),
            date_signature=datetime.strptime(data['date_signature'], '%Y-%m-%d').date() if data.get('date_signature') else None,
            numero_ecrit=data['numero_ecrit'],
            expediteur=data['expediteur'],
            objet=data['objet'],
            classification=data.get('classification'),
            annotation=data.get('annotation')
        )

        db.session.add(courrier)
        db.session.flush()  # Pour obtenir l'ID du courrier

        # Ajouter les divisions d'action
        if 'divisions_action' in data:
            for division in data['divisions_action']:
                div_action = CourrierDivisionAction(
                    courrier_id=courrier.id,
                    division=division
                )
                db.session.add(div_action)

        # Ajouter les divisions d'information
        if 'divisions_info' in data:
            for division in data['divisions_info']:
                div_info = CourrierDivisionInfo(
                    courrier_id=courrier.id,
                    division=division
                )
                db.session.add(div_info)

        db.session.commit()
        return jsonify(courrier.to_dict()), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@app.route('/init_courriers_db')
def init_courriers_db():
    """Initialiser les tables pour les courriers"""
    try:
        # Créer les tables si elles n'existent pas
        db.create_all()

        # Ajouter quelques données de test si les tables sont vides
        if CourrierArrive.query.count() == 0:
            # Courriers arrivés de test
            courrier1 = CourrierArrive(
                id_courrier='CA-001',
                urgence='urgent',
                nature='message',
                date_arrivee=datetime(2024, 1, 15).date(),
                date_signature=datetime(2024, 1, 14).date(),
                numero_ecrit='MSG-2024-001',
                expediteur='Commandement Régional Sud',
                objet='Demande de rapport mensuel des activités',
                classification='restreint',
                annotation='Traitement prioritaire demandé'
            )
            db.session.add(courrier1)
            db.session.flush()

            # Ajouter les divisions pour le courrier 1
            db.session.add(CourrierDivisionAction(courrier_id=courrier1.id, division='instruction'))
            db.session.add(CourrierDivisionAction(courrier_id=courrier1.id, division='technique'))


            courrier2 = CourrierArrive(
                id_courrier='CA-002',
                urgence='routine',
                nature='nds',
                date_arrivee=datetime(2024, 1, 16).date(),
                date_signature=datetime(2024, 1, 15).date(),
                numero_ecrit='NDS-2024-002',
                expediteur='État-Major Général',
                objet='Instructions pour la formation continue',
                classification='public',
                annotation='Diffusion large recommandée'
            )
            db.session.add(courrier2)
            db.session.flush()

            # Ajouter les divisions pour le courrier 2
            db.session.add(CourrierDivisionAction(courrier_id=courrier2.id, division='instruction'))

            db.session.add(CourrierDivisionInfo(courrier_id=courrier2.id, division='technique'))

        if CourrierEnvoye.query.count() == 0:
            # Courriers envoyés de test
            courrier_env1 = CourrierEnvoye(
                id_courrier='CE-001',
                numero_ecrit='DIV-2024-001',
                division_emettrice='instruction',
                date_depart=datetime(2024, 1, 16).date(),
                nature='nds',
                objet='Réponse au rapport mensuel des activités de formation',
                destinataire='Commandement Régional Sud',
                observations='Envoi urgent demandé'
            )
            db.session.add(courrier_env1)

            courrier_env2 = CourrierEnvoye(
                id_courrier='CE-002',
                numero_ecrit='TECH-2024-001',
                division_emettrice='technique',
                date_depart=datetime(2024, 1, 17).date(),
                nature='message',
                objet='État des véhicules en maintenance et besoins en pièces de rechange',
                destinataire='Direction Centrale du Matériel',
                observations=''
            )
            db.session.add(courrier_env2)

        db.session.commit()
        flash('✅ Base de données des courriers initialisée avec succès!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'❌ Erreur lors de l\'initialisation: {str(e)}', 'danger')

    return redirect(url_for('gestion_courrier'))

@app.route('/gestion_informatique')
def gestion_informatique():
    return render_template('dashboard.html', message="Module de Gestion de l'Informatique en cours de développement")

@app.route('/gestion_planification')
def gestion_planification():
    return render_template('dashboard.html', message="Module de Gestion Planification en cours de développement")

@app.route('/gestion_asa')
def gestion_asa():
    return render_template('dashboard.html', message="Module de Gestion ASA en cours de développement")

@app.route('/get_marques/<type_vehicule>')
@login_required
def get_marques(type_vehicule):
    return jsonify(TYPES_VEHICULES.get(type_vehicule, []))

@app.route('/get_pannes/<type_vehicule>')
@login_required
def get_pannes(type_vehicule):
    return jsonify(TYPES_PANNES.get(type_vehicule, []))

@app.route('/chatbot')
@login_required
def chatbot():
    return render_template('chatbot.html')

@app.route('/qr_codes')
@login_required
def qr_codes():
    return render_template('qr_codes.html')

@app.route('/redouane_qr')
def redouane_qr():
    # Cette route affiche le QR code spécifique pour l'authentification de redouane
    # Ce code est unique et ne doit pas être modifié
    qr_data = "redouane_FAR_auth_7a9b3c2d1e"
    return render_template('redouane_qr.html', qr_data=qr_data)

@app.route('/historique')
@login_required
def historique():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    vehicule = None
    historique = None

    if search_matricule:
        # Récupérer tous les véhicules avec ce matricule (peut représenter plusieurs pannes)
        vehicules = Vehicule.query.filter_by(matricule=search_matricule).all()

        if vehicules:
            # Utiliser le premier véhicule pour les informations générales
            vehicule = vehicules[0]

            # Récupérer l'historique complet pour tous les véhicules avec ce matricule
            historique_details = []

            # Dictionnaire pour regrouper les entrées par type de panne et date de panne
            pannes_dict = {}

            # Récupérer toutes les pannes et leurs dates
            for v in vehicules:
                # Clé unique pour chaque panne: combinaison du type de panne et de la date de panne
                panne_key = f"{v.type_panne}_{v.date_panne.strftime('%Y-%m-%d')}"

                if panne_key not in pannes_dict:
                    pannes_dict[panne_key] = {
                        'vehicule_id': v.id,
                        'type_panne': v.type_panne,
                        'date_panne': v.date_panne,
                        'entries': []
                    }

            # Trier les pannes par date (de la plus ancienne à la plus récente)
            sorted_pannes = sorted(pannes_dict.values(), key=lambda x: x['date_panne'])

            # Pour chaque panne, récupérer son historique de changements de statut
            for panne in sorted_pannes:
                # Récupérer l'historique pour cette panne spécifique
                historique_entries = VehiculeHistorique.query\
                    .filter_by(vehicule_id=panne['vehicule_id'], type_panne=panne['type_panne'])\
                    .order_by(VehiculeHistorique.date_changement)\
                    .all()

                # Si aucune entrée n'est trouvée avec le type de panne spécifié, essayer sans filtre de type de panne
                # (pour la compatibilité avec les anciennes entrées)
                if not historique_entries:
                    historique_entries = VehiculeHistorique.query\
                        .filter_by(vehicule_id=panne['vehicule_id'])\
                        .order_by(VehiculeHistorique.date_changement)\
                        .all()

                # Ajouter les entrées d'historique
                for entry in historique_entries:
                    # Vérifier si l'entrée correspond à cette panne
                    entry_type_panne = entry.type_panne or panne['type_panne']
                    if entry_type_panne == panne['type_panne']:
                        historique_details.append({
                            'date_changement': entry.date_changement,
                            'date_panne': entry.date_panne or panne['date_panne'],
                            'type_panne': entry_type_panne,
                            'statut': entry.statut,
                            'description': entry.description,
                            'panne_date': panne['date_panne']  # Pour le tri par panne
                        })

            # Trier l'historique d'abord par date de panne, puis par date de changement
            historique_details.sort(key=lambda x: (x['panne_date'], x['date_changement']))

    return render_template('historique.html',
                           vehicule=vehicule,
                           historique=historique_details if vehicule else None,
                           search_matricule=search_matricule)

@app.route('/api/historique/<int:vehicule_id>')
@login_required
def api_historique(vehicule_id):
    vehicule = Vehicule.query.get(vehicule_id)
    if not vehicule:
        return jsonify({"error": "Véhicule non trouvé"}), 404

    # Récupérer l'historique du véhicule dans l'ordre chronologique (du plus ancien au plus récent)
    historique_query = VehiculeHistorique.query.filter_by(vehicule_id=vehicule_id).order_by(VehiculeHistorique.date_changement.asc()).all()

    # Convertir les objets en dictionnaires et s'assurer que le type de panne est inclus
    historique = []
    for h in historique_query:
        entry_dict = h.to_dict()
        # Si le type de panne n'est pas défini dans l'entrée d'historique, utiliser celui du véhicule
        if not entry_dict['type_panne']:
            entry_dict['type_panne'] = vehicule.type_panne
        historique.append(entry_dict)

    return jsonify({
        "vehicule": vehicule.to_dict(),
        "historique": historique
    })

@app.route('/chat', methods=['POST'])
@login_required
def chat():
    data = request.get_json()
    message = data.get('message', '').lower()

    # Analyse du message et génération de la réponse
    response = process_message(message)

    return jsonify({'response': response})

def process_message(message):
    # Recherche de véhicules
    if any(word in message for word in ['liste', 'afficher', 'voir', 'montrer']):
        vehicules_query = Vehicule.query.all()
        vehicules = [v.to_dict() for v in vehicules_query]

        if not vehicules:
            return "Il n'y a aucun véhicule enregistré dans le système."

        response = "Voici la liste des véhicules :<br><br>"
        for v in vehicules:
            response += f"""
            <div class="vehicle-card">
                <strong>Type:</strong> {v['type_vehicule']}<br>
                <strong>Marque:</strong> {v['marque']}<br>
                <strong>Matricule:</strong> {v['matricule']}<br>
                <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                <strong>Type de panne:</strong> {v['type_panne']}<br>
                <strong>Date de panne:</strong> {v['date_panne']}<br>
                <strong>Statut:</strong> {v['statut']}
            </div>
            """
        return response

    # Comptage des véhicules par statut
    elif any(word in message for word in ['combien', 'nombre', 'total']):
        # Recherche par statut
        statuts = ['en panne', 'indisponible', 'en réparation', 'réparé']
        for statut in statuts:
            if statut in message.lower():
                # Normaliser le statut (première lettre en majuscule)
                statut_norm = statut.capitalize()
                if statut == 'en panne':
                    statut_norm = 'En panne'
                elif statut == 'en réparation':
                    statut_norm = 'En réparation'

                count = Vehicule.query.filter_by(statut=statut_norm).count()
                if count == 0:
                    return f"Il n'y a actuellement aucun véhicule {statut}."
                elif count == 1:
                    return f"Il y a actuellement 1 véhicule {statut}."
                else:
                    return f"Il y a actuellement {count} véhicules {statut}."

        # Si aucun statut spécifique n'est mentionné
        if 'panne' in message:
            en_panne = Vehicule.query.filter_by(statut='En panne').count()
            if en_panne == 0:
                return "Il n'y a actuellement aucun véhicule en panne."
            elif en_panne == 1:
                return "Il y a actuellement 1 véhicule en panne."
            else:
                return f"Il y a actuellement {en_panne} véhicules en panne."
        else:
            total = Vehicule.query.count()
            return f"Il y a au total {total} véhicule(s) enregistré(s)."

    # Statistiques sur les marques
    elif 'marque' in message:
        total = Vehicule.query.count()
        if total == 0:
            return "Il n'y a aucun véhicule enregistré pour analyser les marques."

        marques = {}
        for type_v, marques_list in TYPES_VEHICULES.items():
            for marque in marques_list:
                count = Vehicule.query.filter_by(marque=marque).count()
                if count > 0:
                    marques[marque] = count

        response = "Voici la répartition par marque :<br><br>"
        for marque, count in sorted(marques.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total) * 100
            response += f"""
            <div class="vehicle-card">
                <strong>{marque}:</strong> {count} véhicule(s) ({percentage:.1f}%)
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: {percentage}%"></div>
                </div>
            </div>
            """
        return response

    # Recherche par unité (GAR) - Traitement unifié
    elif any(word in message for word in ['gar', 'unité', 'unite', 'unites', 'unités', 'groupement']):
        # Extraire le numéro de GAR du message
        import re

        # Ajouter du débogage
        print(f"Message GAR: {message}")

        # Essayer d'abord de trouver un motif comme "14 GAR" ou "unité 14"
        gar_match = re.search(r'(\d+)\s*(?:gar|unité|unite)', message.lower())
        if gar_match:
            print(f"Match 1: {gar_match.group(1)}")

        # Si ça ne marche pas, essayer de trouver un motif comme "véhicules du 14 GAR" ou "unité numéro 14"
        if not gar_match:
            gar_match = re.search(r'(?:gar|unité|unite).*?(\d+)', message.lower())
            if gar_match:
                print(f"Match 2: {gar_match.group(1)}")

        # Si ça ne marche toujours pas, chercher un numéro entre 10 et 30 (plage typique des GAR)
        if not gar_match and any(word in message.lower() for word in ['véhicule', 'vehicule', 'véhicules', 'vehicules']):
            num_match = re.findall(r'\b(\d+)\b', message.lower())
            for num in num_match:
                if 10 <= int(num) <= 30:
                    gar_match = re.match(r'(\d+)', num)
                    if gar_match:
                        print(f"Match 3: {gar_match.group(1)}")
                    break

        # Si on trouve un numéro de GAR spécifique
        if gar_match:
            gar_num = gar_match.group(1)
            print(f"GAR trouvé: {gar_num}")

            # Recherche flexible pour tenir compte des variations de format
            vehicules_query = Vehicule.query.filter(
                Vehicule.unite.like(f"%{gar_num}%") &
                Vehicule.unite.like("%GAR%")
            ).all()
            vehicules = [v.to_dict() for v in vehicules_query]

            if not vehicules:
                return f"Il n'y a aucun véhicule assigné à l'unité {gar_num} GAR."

            response = f"Voici les véhicules de l'unité {gar_num} GAR :<br><br>"
            for v in vehicules:
                response += f"""
                <div class="vehicle-card">
                    <strong>Type:</strong> {v['type_vehicule']}<br>
                    <strong>Marque:</strong> {v['marque']}<br>
                    <strong>Matricule:</strong> {v['matricule']}<br>
                    <strong>Type de panne:</strong> {v['type_panne']}<br>
                    <strong>Date de panne:</strong> {v['date_panne']}<br>
                    <strong>Statut:</strong> {v['statut']}
                </div>
                """
            return response

        # Si on demande les statistiques par GAR sans spécifier un numéro
        elif any(word in message.lower() for word in ['statistique', 'stats', 'répartition', 'repartition']):
            total = Vehicule.query.count()
            if total == 0:
                return "Il n'y a aucun véhicule enregistré pour analyser les unités."

            # Récupérer les unités distinctes
            unites_distinctes = db.session.query(Vehicule.unite).distinct().all()
            unites = {}

            for unite in unites_distinctes:
                unite_nom = unite[0]
                if unite_nom:
                    count = Vehicule.query.filter_by(unite=unite_nom).count()
                    if count > 0:
                        unites[unite_nom] = count
                else:
                    # Compter les véhicules sans unité assignée
                    count = Vehicule.query.filter(or_(Vehicule.unite == None, Vehicule.unite == '')).count()
                    if count > 0:
                        unites["Non assigné"] = count

            response = "Voici la répartition par unité (GAR) :<br><br>"
            for unite, count in sorted(unites.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                response += f"""
                <div class="vehicle-card">
                    <strong>{unite}:</strong> {count} véhicule(s) ({percentage:.1f}%)
                    <div class="progress">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: {percentage}%"></div>
                    </div>
                </div>
                """
            return response

        # Si on demande juste les GAR sans préciser
        else:
            # Récupérer les unités distinctes
            unites_distinctes = db.session.query(Vehicule.unite).distinct().all()
            unites = {}

            for unite in unites_distinctes:
                unite_nom = unite[0]
                if unite_nom:
                    count = Vehicule.query.filter_by(unite=unite_nom).count()
                    if count > 0:
                        unites[unite_nom] = count
                else:
                    # Compter les véhicules sans unité assignée
                    count = Vehicule.query.filter(or_(Vehicule.unite == None, Vehicule.unite == '')).count()
                    if count > 0:
                        unites["Non assigné"] = count

            if not unites:
                return "Il n'y a aucune unité (GAR) avec des véhicules assignés."

            response = "Voici la répartition des véhicules par unité (GAR) :<br><br>"
            for unite, count in sorted(unites.items(), key=lambda x: x[1], reverse=True):
                response += f"""
                <div class="vehicle-card">
                    <strong>{unite}:</strong> {count} véhicule(s)
                </div>
                """
            return response

    # Recherche par type de véhicule
    elif any(type_v.lower() in message for type_v in TYPES_VEHICULES.keys()):
        for type_v in TYPES_VEHICULES.keys():
            if type_v.lower() in message:
                vehicules_query = Vehicule.query.filter_by(type_vehicule=type_v).all()
                vehicules = [v.to_dict() for v in vehicules_query]

                if not vehicules:
                    return f"Il n'y a aucun véhicule de type {type_v} enregistré."

                response = f"Voici les véhicules de type {type_v} :<br><br>"
                for v in vehicules:
                    response += f"""
                    <div class="vehicle-card">
                        <strong>Marque:</strong> {v['marque']}<br>
                        <strong>Matricule:</strong> {v['matricule']}<br>
                        <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                        <strong>Type de panne:</strong> {v['type_panne']}<br>
                        <strong>Date de panne:</strong> {v['date_panne']}<br>
                        <strong>Statut:</strong> {v['statut']}
                    </div>
                    """
                return response

    # Cette section a été fusionnée avec la section "Recherche par unité (GAR) - Traitement unifié" ci-dessus

    # Recherche par statut
    elif any(statut.lower() in message.lower() for statut in ['en panne', 'indisponible', 'en réparation', 'réparé']):
        statuts = ['en panne', 'indisponible', 'en réparation', 'réparé']
        for statut in statuts:
            if statut in message.lower():
                # Normaliser le statut (première lettre en majuscule)
                statut_norm = statut.capitalize()
                if statut == 'en panne':
                    statut_norm = 'En panne'
                elif statut == 'en réparation':
                    statut_norm = 'En réparation'

                # Si le message contient des mots comme "liste", "afficher", etc., montrer les véhicules
                if any(word in message.lower() for word in ['liste', 'afficher', 'voir', 'montrer', 'montre', 'affiche', 'donne', 'quels', 'quelles', 'véhicule', 'vehicule', 'véhicules', 'vehicules']):
                    vehicules_query = Vehicule.query.filter_by(statut=statut_norm).all()
                    vehicules = [v.to_dict() for v in vehicules_query]

                    if not vehicules:
                        return f"Il n'y a aucun véhicule avec le statut '{statut_norm}'."

                    response = f"Voici les véhicules avec le statut '{statut_norm}' :<br><br>"
                    for v in vehicules:
                        response += f"""
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Matricule:</strong> {v['matricule']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Type de panne:</strong> {v['type_panne']}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}
                        </div>
                        """
                    return response
                # Sinon, compter les véhicules avec ce statut
                else:
                    count = Vehicule.query.filter_by(statut=statut_norm).count()
                    if count == 0:
                        return f"Il n'y a actuellement aucun véhicule {statut}."
                    elif count == 1:
                        return f"Il y a actuellement 1 véhicule {statut}."
                    else:
                        return f"Il y a actuellement {count} véhicules {statut}."

    # Recherche par type de panne
    elif any(panne.lower() in message for panne in [p.lower() for type_pannes in TYPES_PANNES.values() for p in type_pannes]):
        for type_v, pannes_list in TYPES_PANNES.items():
            for panne in pannes_list:
                if panne.lower() in message.lower():
                    vehicules_query = Vehicule.query.filter_by(type_panne=panne).all()
                    vehicules = [v.to_dict() for v in vehicules_query]

                    if not vehicules:
                        return f"Il n'y a aucun véhicule avec une panne de type '{panne}'."

                    response = f"Voici les véhicules avec une panne de type '{panne}' :<br><br>"
                    for v in vehicules:
                        response += f"""
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Matricule:</strong> {v['matricule']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}<br>
                            <strong>Statut:</strong> {v['statut']}
                        </div>
                        """
                    return response

    # Recherche par matricule
    elif any(word in message.lower() for word in ['matricule', 'immatriculation', 'numéro', 'numero']):
        import re

        # Mots à exclure de la recherche de matricule
        mots_exclus = ['matricule', 'immatriculation', 'numéro', 'numero', 'du', 'le', 'la', 'les', 'des', 'un', 'une',
                      'recherche', 'cherche', 'trouve', 'montre', 'affiche', 'voir', 'vehicule', 'véhicule', 'historique']

        # Extraire tous les mots du message
        mots = re.findall(r'\b[A-Za-z0-9-]+\b', message)

        # Filtrer les mots exclus
        matricules_potentiels = [mot for mot in mots if mot.lower() not in mots_exclus]

        # Si nous avons des matricules potentiels
        if matricules_potentiels:
            # Prendre le premier matricule potentiel
            matricule = matricules_potentiels[0]

            # Afficher le matricule trouvé pour le débogage
            print(f"Matricule trouvé: {matricule}")

            # Vérifier si le matricule est présent dans le message et n'est pas juste un mot comme "matricule"
            if matricule.lower() not in mots_exclus:
                # Recherche du véhicule par matricule
                vehicule = Vehicule.query.filter(Vehicule.matricule.like(f"%{matricule}%")).first()

                if vehicule:
                    v = vehicule.to_dict()

                    # Récupérer l'historique réel depuis la table vehicule_historique
                    try:
                        # Récupérer l'historique depuis la base de données
                        historique_query = VehiculeHistorique.query.filter_by(vehicule_id=vehicule.id).order_by(VehiculeHistorique.date_changement).all()

                        # Convertir les objets en dictionnaires pour faciliter l'affichage
                        historique = []
                        for entry in historique_query:
                            historique.append({
                                "date": entry.date_changement.strftime('%Y-%m-%d'),
                                "statut": entry.statut,
                                "description": entry.description
                            })

                        print(f"Historique réel trouvé pour le véhicule {vehicule.id}: {len(historique)} entrées")

                        # Si aucun historique n'est trouvé, générer un historique simulé
                        if not historique:
                            print(f"Aucun historique trouvé pour le véhicule {vehicule.id}, génération d'un historique simulé")
                            if v['statut'] == 'En panne':
                                historique = [
                                    {"date": v['date_panne'], "statut": "En panne", "description": "Panne initiale signalée (simulé)"}
                                ]
                            elif v['statut'] == 'En réparation':
                                historique = [
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                     "statut": "En panne", "description": "Panne initiale signalée (simulé)"},
                                    {"date": v['date_panne'], "statut": "En réparation", "description": "Début des réparations (simulé)"}
                                ]
                            elif v['statut'] == 'Réparé':
                                historique = [
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=14)).strftime('%Y-%m-%d'),
                                     "statut": "En panne", "description": "Panne initiale signalée (simulé)"},
                                    {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                     "statut": "En réparation", "description": "Début des réparations (simulé)"},
                                    {"date": v['date_panne'], "statut": "Réparé", "description": "Réparations terminées (simulé)"}
                                ]
                            else:
                                historique = [
                                    {"date": v['date_panne'], "statut": v['statut'], "description": "État initial (simulé)"}
                                ]
                    except Exception as e:
                        print(f"Erreur lors de la récupération de l'historique: {e}")
                        # En cas d'erreur, générer un historique simulé
                        if v['statut'] == 'En panne':
                            historique = [
                                {"date": v['date_panne'], "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"}
                            ]
                        elif v['statut'] == 'En réparation':
                            historique = [
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                 "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"},
                                {"date": v['date_panne'], "statut": "En réparation", "description": "Début des réparations (simulé après erreur)"}
                            ]
                        elif v['statut'] == 'Réparé':
                            historique = [
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=14)).strftime('%Y-%m-%d'),
                                 "statut": "En panne", "description": "Panne initiale signalée (simulé après erreur)"},
                                {"date": (datetime.strptime(v['date_panne'], '%Y-%m-%d') - timedelta(days=7)).strftime('%Y-%m-%d'),
                                 "statut": "En réparation", "description": "Début des réparations (simulé après erreur)"},
                                {"date": v['date_panne'], "statut": "Réparé", "description": "Réparations terminées (simulé après erreur)"}
                            ]
                        else:
                            historique = [
                                {"date": v['date_panne'], "statut": v['statut'], "description": "État initial (simulé après erreur)"}
                            ]

                    # Construire la réponse avec l'état actuel et l'historique
                    response = f"""
                    <div class="vehicle-detail">
                        <h4>État actuel du véhicule matricule {v['matricule']}</h4>
                        <div class="vehicle-card">
                            <strong>Type:</strong> {v['type_vehicule']}<br>
                            <strong>Marque:</strong> {v['marque']}<br>
                            <strong>Unité:</strong> {v['unite'] if v['unite'] else 'Non assigné'}<br>
                            <strong>Type de panne:</strong> {v['type_panne']}<br>
                            <strong>Date de panne:</strong> {v['date_panne']}<br>
                            <strong>Statut actuel:</strong> <span class="badge bg-{'success' if v['statut'] == 'Réparé' else ('warning text-dark' if v['statut'] == 'Indisponible' else ('info' if v['statut'] == 'En réparation' else 'danger'))}">{v['statut']}</span><br>
                            <strong>Description:</strong> {v['description'] if v['description'] else 'Aucune description disponible'}
                        </div>

                        <h4 class="mt-4">Historique du véhicule</h4>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                    """

                    # Ajouter chaque entrée d'historique
                    for entry in historique:
                        status_class = 'success' if entry["statut"] == 'Réparé' else ('warning' if entry["statut"] == 'Indisponible' else ('info' if entry["statut"] == 'En réparation' else 'danger'))
                        response += f"""
                                <tr>
                                    <td>{entry["date"]}</td>
                                    <td><span class="badge bg-{status_class}">{entry["statut"]}</span></td>
                                    <td>{entry["description"]}</td>
                                </tr>
                        """

                    response += """
                            </tbody>
                        </table>
                    </div>
                    """

                    return response
                else:
                    return f"Aucun véhicule avec le matricule '{matricule}' n'a été trouvé."

    # Réponse par défaut
    return """Je peux vous aider à :
    - Voir la liste des véhicules
    - Compter les véhicules par statut (en panne, indisponible, en réparation, réparé)
    - Voir les statistiques par marque ou par unité (GAR)
    - Rechercher des véhicules par type, type de panne ou statut
    - Afficher les véhicules d'une unité spécifique
    - Rechercher un véhicule par matricule et voir son historique

    Exemples de questions :
    - "Combien y a-t-il de véhicules en panne ?"
    - "Montre-moi les véhicules indisponibles"
    - "Affiche les statistiques par GAR"
    - "Quels sont les véhicules du 13 GAR ?"
    - "Recherche le véhicule matricule ABC123"
    - "Montre-moi l'historique du véhicule XYZ789"

    Que souhaitez-vous savoir ?"""

# Routes pour la gestion des véhicules GAR
@app.route('/liste_vehicules_gar')
def liste_vehicules_gar():
    vehicules = VehiculeGAR.query.all()
    return render_template('liste_vehicules_gar.html', vehicules=vehicules)

@app.route('/ajouter_vehicule_gar', methods=['GET', 'POST'])
def ajouter_vehicule_gar():
    if request.method == 'POST':
        matricule = request.form.get('matricule')
        unite = request.form.get('unite')
        type_vehicule = request.form.get('type_vehicule')
        marque = request.form.get('marque')

        # Vérification si le matricule existe déjà
        if VehiculeGAR.query.filter_by(matricule=matricule).first():
            flash('Un véhicule avec ce matricule existe déjà.', 'danger')
            return redirect(url_for('ajouter_vehicule_gar'))

        nouveau_vehicule = VehiculeGAR(
            matricule=matricule,
            unite=unite,
            type_vehicule=type_vehicule,
            marque=marque
        )

        try:
            db.session.add(nouveau_vehicule)
            db.session.commit()
            flash('Véhicule ajouté avec succès!', 'success')
            return redirect(url_for('liste_vehicules_gar'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de l\'ajout du véhicule: ' + str(e), 'danger')
            return redirect(url_for('ajouter_vehicule_gar'))

    return render_template('ajouter_vehicule_gar.html')

@app.route('/modifier_vehicule_gar/<int:id>', methods=['GET', 'POST'])
def modifier_vehicule_gar(id):
    vehicule = VehiculeGAR.query.get_or_404(id)
    
    if request.method == 'POST':
        vehicule.matricule = request.form.get('matricule')
        vehicule.unite = request.form.get('unite')
        vehicule.type_vehicule = request.form.get('type_vehicule')
        vehicule.marque = request.form.get('marque')
        
        try:
            db.session.commit()
            flash('Véhicule modifié avec succès!', 'success')
            return redirect(url_for('liste_vehicules_gar'))
        except:
            db.session.rollback()
            flash('Erreur lors de la modification du véhicule', 'danger')
            
    return render_template('modifier_vehicule_gar.html', vehicule=vehicule)

@app.route('/supprimer_vehicule_gar/<int:id>')
def supprimer_vehicule_gar(id):
    vehicule = VehiculeGAR.query.get_or_404(id)
    try:
        db.session.delete(vehicule)
        db.session.commit()
        flash('Véhicule supprimé avec succès!', 'success')
    except:
        db.session.rollback()
        flash('Erreur lors de la suppression du véhicule', 'danger')
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/verifier_vehicule')
def verifier_vehicule():
    matricule = request.args.get('matricule')

    if not matricule:
        return jsonify({'success': False, 'message': 'Matricule non fourni'}), 400

    try:
        # Recherche exacte
        vehicule = VehiculeGAR.query.filter_by(matricule=matricule).first()
        
        if not vehicule:
            # Si pas de correspondance exacte, recherche avec LIKE
            vehicule = VehiculeGAR.query.filter(VehiculeGAR.matricule.like(f"%{matricule}%")).first()

        if vehicule:
            return jsonify({
                'success': True,
                'vehicule': {
                    'id': vehicule.id,
                    'matricule': vehicule.matricule,
                    'unite': vehicule.unite,
                    'type_vehicule': vehicule.type_vehicule,
                    'marque': vehicule.marque
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Aucun véhicule trouvé avec le matricule "{matricule}".'
            })

    except Exception as e:
        print(f"Erreur lors de la vérification du véhicule : {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erreur interne lors de la vérification du véhicule : {str(e)}'
        }), 500

@app.route('/initialiser_vehicules_gar')
@login_required
def initialiser_vehicules_gar():
    try:
        # Supprimer tous les véhicules GAR existants
        VehiculeGAR.query.delete()
        
        # Liste des marques pour chaque type de véhicule
        marques_vl = ['Toyota', 'Atlas 3', 'Atlas 4', 'Atlas 5', 'VLRA', 'Hummer', 'Nissan']
        marques_pl = ['JBD', 'Kaiser', 'Renault']
        
        # Compteur de matricule global
        matricule_base = 1000
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 10 VL
            for i in range(10):
                matricule = str(matricule_base + i)  # Format: 1000, 1001, 1002, etc.
                marque = marques_vl[i % len(marques_vl)]
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='VL',
                    marque=marque
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour les PL
            matricule_base += 10
            
            # Ajouter 5 PL
            for i in range(5):
                matricule = str(matricule_base + i)  # Format: 1010, 1011, 1012, etc.
                marque = marques_pl[i % len(marques_pl)]
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='PL',
                    marque=marque
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 5
        
        db.session.commit()
        flash('Véhicules GAR initialisés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'initialisation des véhicules GAR : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/ajouter_engins_chenilles')
@login_required
def ajouter_engins_chenilles():
    try:
        # Compteur de matricule global
        matricule_base = 1390
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 6 engins chenillés M109
            for i in range(6):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='Engin chenillé',
                    marque='M109 155mm'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 6
        
        db.session.commit()
        flash('Engins chenillés ajoutés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout des engins chenillés : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/ajouter_vehicules_speciaux')
@login_required
def ajouter_vehicules_speciaux():
    try:
        # Compteur de matricule global (commence après les M109)
        matricule_base = 1390 + (26 * 6)  # 26 GAR * 6 M109 par GAR
        
        # Pour chaque GAR (de 1 à 26)
        for gar_num in range(1, 27):
            gar = f"{gar_num}GAR"
            
            # Ajouter 3 M110
            for i in range(3):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='Engin chenillé',
                    marque='M110 203mm'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour les SA
            matricule_base += 3
            
            # Ajouter 3 SA
            for i in range(3):
                matricule = str(matricule_base + i)
                
                vehicule = VehiculeGAR(
                    matricule=matricule,
                    unite=gar,
                    type_vehicule='SA',
                    marque='PULS'
                )
                db.session.add(vehicule)
            
            # Mettre à jour le compteur de matricule pour le prochain GAR
            matricule_base += 3
        
        db.session.commit()
        flash('Véhicules spéciaux (M110 et SA) ajoutés avec succès !', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'ajout des véhicules spéciaux : {str(e)}', 'danger')
    
    return redirect(url_for('liste_vehicules_gar'))

@app.route('/get_unites')
def get_unites():
    try:
        # Récupérer toutes les unités distinctes de la table vehicule_gar
        unites = db.session.query(VehiculeGAR.unite).distinct().all()
        
        # Convertir le résultat en liste et extraire les numéros de GAR
        unites_triees = []
        for unite in unites:
            if unite[0]:
                # Extraire le numéro du GAR
                import re
                match = re.search(r'(\d+)GAR', unite[0])
                if match:
                    numero_gar = int(match.group(1))
                    unites_triees.append((numero_gar, unite[0]))
                else:
                    # Si ce n'est pas un GAR numéroté, mettre à la fin
                    unites_triees.append((999, unite[0]))
        
        # Trier par numéro de GAR
        unites_triees.sort(key=lambda x: x[0])
        
        # Extraire uniquement les noms des unités dans l'ordre
        unites_list = [unite[1] for unite in unites_triees]
        
        return jsonify(unites_list)
    except Exception as e:
        print(f"Erreur lors de la récupération des unités: {str(e)}")
        return jsonify([]), 500

@app.route('/generer_donnees_test')
def generer_donnees_test():
    """
    Ajoute des données de test supplémentaires dans la table de gestion des stages (Stage ou Stagiaire),
    en se basant sur la structure et les exemples déjà présents dans la base.
    """
    from db import db
    try:
        # Importer le modèle Stage ou Stagiaire selon la structure de votre app
        # Ici, on suppose un modèle Stage avec les champs classiques
        from gestion_vehicules.models import Stage
    except ImportError:
        flash("Erreur : Le modèle Stage n'est pas défini dans gestion_vehicules.models.", 'danger')
        return redirect(url_for('dashboard_stages'))

    # Exemples de données similaires à celles déjà insérées
    stages_a_ajouter = [
        Stage(nom="El Amrani", prenom="Yassine", sujet="Développement d'un module de gestion", date_debut=date(2025, 6, 1), date_fin=date(2025, 7, 15), encadrant="Capitaine Benslimane", unite="14GAR"),
        Stage(nom="Boukari", prenom="Sara", sujet="Optimisation du suivi des véhicules", date_debut=date(2025, 5, 20), date_fin=date(2025, 7, 2), encadrant="Lieutenant El Fassi", unite="13GAR"),
        Stage(nom="Benali", prenom="Omar", sujet="Mise en place d'un système de notification", date_debut=date(2025, 6, 10), date_fin=date(2025, 7, 20), encadrant="Adjudant Chef Redouane", unite="15GAR"),
        Stage(nom="El Idrissi", prenom="Imane", sujet="Gestion documentaire numérique", date_debut=date(2025, 6, 5), date_fin=date(2025, 7, 18), encadrant="Capitaine Benslimane", unite="14GAR"),
        Stage(nom="Moujahid", prenom="Hamza", sujet="Déploiement d'un chatbot interne", date_debut=date(2025, 6, 15), date_fin=date(2025, 7, 25), encadrant="Lieutenant El Fassi", unite="13GAR"),
    ]

    try:
        for stage in stages_a_ajouter:
            db.session.add(stage)
        db.session.commit()
        flash("Données de test stages ajoutées avec succès !", 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de l'ajout des données de test stages : {str(e)}", 'danger')
    return redirect(url_for('dashboard_stages'))
    try:
        # Supprimer les données existantes
        Vehicule.query.delete()
        VehiculeHistorique.query.delete()
        db.session.commit()

        # Récupérer tous les véhicules de la table vehicule_gar
        vehicules_gar = VehiculeGAR.query.all()
        
        if not vehicules_gar:
            flash('Aucun véhicule trouvé dans la table vehicule_gar. Veuillez d\'abord initialiser les véhicules GAR.', 'warning')
            return redirect(url_for('index'))

        # Types de pannes par type de véhicule
        types_pannes = {
            'VL': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'PL': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'Engin chenillé': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ],
            'SA': [
                'Fuite d\'huile moteur', 'Surchauffe moteur', 'Défaillance du système d\'injection',
                'Problème de démarrage', 'Courroie de distribution cassée', 'Joint de culasse défectueux',
                'Perte de puissance moteur', 'Consommation excessive d\'huile', 'Bruit anormal du moteur',
                'Fumée blanche à l\'échappement', 'Fumée noire à l\'échappement', 'Embrayage défectueux',
                'Problème de transmission', 'Boîte de vitesses bloquée', 'Difficulté à passer les vitesses',
                'Bruit anormal de la transmission', 'Fuite d\'huile de transmission',
                'Usure prématurée de l\'embrayage', 'Défaillance du système de freinage',
                'Usure des plaquettes de frein', 'Disques de frein voilés', 'Fuite de liquide de frein',
                'ABS défectueux', 'Frein à main défaillant', 'Problème de direction assistée',
                'Amortisseurs usés', 'Rotules de suspension défectueuses', 'Barre stabilisatrice cassée',
                'Craquements dans la suspension', 'Alignement des roues incorrect', 'Batterie déchargée',
                'Alternateur défectueux', 'Démarreur défaillant', 'Court-circuit électrique',
                'Problème de faisceau électrique', 'Défaillance des phares tactiques',
                'Système radio défectueux', 'Pompe à carburant défaillante'
            ]
        }

        # Statuts possibles
        statuts = ['En panne', 'Indisponible', 'En réparation', 'Réparé']

        # Sélectionner aléatoirement 55 véhicules de la table vehicule_gar
        vehicules_selectionnes = random.sample(vehicules_gar, min(55, len(vehicules_gar)))

        # Générer les pannes pour les véhicules sélectionnés
        for vehicule_gar in vehicules_selectionnes:
            type_panne = random.choice(types_pannes[vehicule_gar.type_vehicule])
            statut = random.choice(statuts)
            
            # Générer une date aléatoire dans les 30 derniers jours
            date_panne = datetime.now() - timedelta(days=random.randint(0, 30))
            
            # Créer le véhicule avec les informations de vehicule_gar
            vehicule = Vehicule(
                type_vehicule=vehicule_gar.type_vehicule,
                marque=vehicule_gar.marque,
                matricule=vehicule_gar.matricule,
                type_panne=type_panne,
                date_panne=date_panne.date(),
                description=f"Description de la panne pour {vehicule_gar.matricule}",
                statut=statut,
                unite=vehicule_gar.unite
            )
            db.session.add(vehicule)
            db.session.flush()  # Pour obtenir l'ID du véhicule

            # Créer l'historique
            historique = VehiculeHistorique(
                vehicule_id=vehicule.id,
                statut=statut,
                type_panne=type_panne,
                date_panne=date_panne.date(),
                description=f"État initial pour {vehicule_gar.matricule}"
            )
            db.session.add(historique)

        db.session.commit()
        flash(f'{len(vehicules_selectionnes)} pannes de test ont été générées avec succès!', 'success')
        return redirect(url_for('index'))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la génération des données de test: {str(e)}', 'danger')
        return redirect(url_for('index'))

# Routes pour la gestion des entretiens
@app.route('/entretiens')
@login_required
def entretiens():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    # Construire la requête de base
    query = Entretien.query.join(VehiculeGAR)

    # Filtrer par matricule si une recherche est effectuée
    if search_matricule:
        query = query.filter(VehiculeGAR.matricule.like(f"%{search_matricule}%"))
        
        # Récupérer les résultats
        entretiens = query.order_by(Entretien.date_entretien.desc()).all()
        
        if entretiens:
            # Message de succès avec le nombre d'entretiens trouvés
            count = len(entretiens)
            flash(f"{count} entretien{'s' if count > 1 else ''} trouvé{'s' if count > 1 else ''} pour le matricule \"{search_matricule}\"", 'success')
        else:
            # Message si aucun entretien n'est trouvé
            flash(f'Aucun entretien trouvé pour le matricule "{search_matricule}"', 'warning')
            # Charger tous les entretiens si aucun résultat
            entretiens = Entretien.query.order_by(Entretien.date_entretien.desc()).all()
    else:
        # Sans recherche, charger tous les entretiens
        entretiens = query.order_by(Entretien.date_entretien.desc()).all()

    # Charger la liste des véhicules pour le formulaire d'ajout
    vehicules = VehiculeGAR.query.all()

    return render_template('entretiens.html', 
                         entretiens=entretiens, 
                         vehicules=vehicules,
                         search_matricule=search_matricule)

@app.route('/entretiens/ajouter', methods=['POST'])
@login_required
def ajouter_entretien():
    if request.method == 'POST':
        try:
            # Récupération des données du formulaire
            vehicule_id = request.form.get('vehicule_id')
            type_entretien = request.form.get('type_entretien')
            if not type_entretien:
                type_entretien = 'Vidange'
            date_entretien = datetime.strptime(request.form.get('date_entretien'), '%Y-%m-%d')
            kilometrage = int(request.form.get('kilometrage'))
            kilometrage_prochain = int(request.form.get('kilometrage_prochain')) if request.form.get('kilometrage_prochain') else None
            description = request.form.get('description')
            # Récupérer toutes les pièces cochées et les joindre par des virgules
            pieces_remplacees_list = request.form.getlist('pieces_remplacees[]')
            pieces_remplacees = ', '.join(pieces_remplacees_list) if pieces_remplacees_list else None
            prochain_entretien = datetime.strptime(request.form.get('prochain_entretien'), '%Y-%m-%d') if request.form.get('prochain_entretien') else None
            statut = request.form.get('statut')

            # Création du nouvel entretien
            nouvel_entretien = Entretien(
                vehicule_id=vehicule_id,
                type_entretien=type_entretien,
                date_entretien=date_entretien,
                kilometrage=kilometrage,
                kilometrage_prochain=kilometrage_prochain,
                description=description,
                pieces_remplacees=pieces_remplacees,
                prochain_entretien=prochain_entretien,
                statut=statut
            )

            # Ajout à la base de données
            db.session.add(nouvel_entretien)
            db.session.commit()

            flash('Entretien ajouté avec succès !', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'ajout de l\'entretien : {str(e)}', 'danger')

    return redirect(url_for('entretiens'))

@app.route('/entretiens/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_entretien(id):
    entretien = Entretien.query.get_or_404(id)
    vehicules = VehiculeGAR.query.all()

    if request.method == 'POST':
        try:
            # Mise à jour des données
            entretien.vehicule_id = request.form.get('vehicule_id')
            entretien.type_entretien = request.form.get('type_entretien')
            entretien.date_entretien = datetime.strptime(request.form.get('date_entretien'), '%Y-%m-%d')
            entretien.kilometrage = int(request.form.get('kilometrage'))
            entretien.kilometrage_prochain = int(request.form.get('kilometrage_prochain')) if request.form.get('kilometrage_prochain') else None
            entretien.description = request.form.get('description')
            # Récupérer toutes les pièces cochées et les joindre par des virgules
            pieces_remplacees_list = request.form.getlist('pieces_remplacees[]')
            entretien.pieces_remplacees = ', '.join(pieces_remplacees_list) if pieces_remplacees_list else None
            entretien.prochain_entretien = datetime.strptime(request.form.get('prochain_entretien'), '%Y-%m-%d') if request.form.get('prochain_entretien') else None
            entretien.statut = request.form.get('statut')

            # Mise à jour dans la base de données
            db.session.commit()

            flash('Entretien modifié avec succès !', 'success')
            return redirect(url_for('entretiens'))
        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de la modification de l\'entretien : {str(e)}', 'danger')

    return render_template('modifier_entretien.html', entretien=entretien, vehicules=vehicules)

@app.route('/mise_a_jour_kilometrage/<int:vehicule_id>', methods=['POST'])
@login_required
def mise_a_jour_kilometrage(vehicule_id):
    try:
        vehicule = VehiculeGAR.query.get_or_404(vehicule_id)
        nouveau_kilometrage = int(request.form.get('kilometrage'))
        
        # Mise à jour du kilométrage du véhicule
        vehicule.kilometrage = nouveau_kilometrage
        
        # Mise à jour du dernier kilométrage connu pour les entretiens
        dernier_entretien = Entretien.query.filter_by(vehicule_id=vehicule_id)\
            .order_by(Entretien.date_entretien.desc()).first()
            
        if dernier_entretien:
            dernier_entretien.kilometrage = nouveau_kilometrage
        
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/entretiens/supprimer/<int:id>', methods=['POST'])
@login_required
def supprimer_entretien(id):
    try:
        entretien = Entretien.query.get_or_404(id)
        db.session.delete(entretien)
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/entretiens/verification_km')
def verifier_kilometrages():
    # Récupérer les véhicules qui ont besoin d'un entretien
    entretiens = Entretien.query.all()
    vehicules_a_verifier = []
    
    for entretien in entretiens:
        vehicule = VehiculeGAR.query.get(entretien.vehicule_id)
        if vehicule:
            difference = entretien.prochain_kilometrage - vehicule.kilometrage_actuel
            if difference <= 1000:
                vehicules_a_verifier.append({
                    'matricule': vehicule.matricule,
                    'kilometrage_actuel': vehicule.kilometrage_actuel,
                    'kilometrage_prochain': entretien.prochain_kilometrage,
                    'difference': difference
                })
    
    return jsonify(vehicules_a_verifier)

@app.route('/vehicules')
@login_required
def liste_vehicules():
    # Récupérer le paramètre de recherche s'il existe
    search_matricule = request.args.get('search_matricule', '')

    # Filtrer les véhicules si un matricule est spécifié
    if (search_matricule):
        vehicules_query = Vehicule.query.filter(Vehicule.matricule.like(f"%{search_matricule}%")).order_by(Vehicule.date_panne.asc()).all()
        if vehicules_query:
            # Si des véhicules sont trouvés, afficher un message indiquant le nombre de pannes trouvées
            pannes_count = len(vehicules_query)
            if pannes_count == 1:
                flash(f'1 panne trouvée pour le matricule "{search_matricule}"', 'success')
            else:
                flash(f'{pannes_count} pannes trouvées pour le matricule "{search_matricule}"', 'success')
        else:
            flash(f'Aucun véhicule trouvé avec le matricule "{search_matricule}"', 'warning')
            vehicules_query = Vehicule.query.order_by(Vehicule.date_panne.asc()).all()  # Afficher tous les véhicules si aucun résultat
    else:
        vehicules_query = Vehicule.query.order_by(Vehicule.date_panne.asc()).all()

    # Convertir les véhicules en dictionnaires et trier par GAR
    vehicules = [v.to_dict() for v in vehicules_query]
    
    # Fonction pour extraire et trier correctement les numéros de GAR
    def get_gar_number(vehicule):
        if not vehicule['unite']:
            return (999, '')  # Mettre les véhicules sans GAR à la fin
        
        import re
        # Recherche du motif 1GAR, 2GAR, etc. (insensible à la casse)
        match = re.search(r'(\d+)GAR', vehicule['unite'], re.IGNORECASE)
        if match:
            # Retourne un tuple (numéro, texte) pour un tri correct
            return (int(match.group(1)), vehicule['unite'])
        return (999, vehicule['unite'])  # Mettre les autres unités à la fin
    
    # Trier les véhicules par numéro de GAR (tri naturel)
    vehicules.sort(key=get_gar_number)

    return render_template('index.html', vehicules=vehicules, search_matricule=search_matricule)

# ===================================================================
# DÉFINITION DES MODÈLES POUR LA GESTION DES STAGES (selon le nouveau cahier des charges)
# ===================================================================

class Stagiaire(db.Model):
    __tablename__ = 'stagiaire'
    id_stagiaire = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    date_naissance = db.Column(db.Date, nullable=False)
    # Champ pour le chemin de la photo
    photo = db.Column(db.String(255), nullable=True)  # Stocke le chemin du fichier
    # Ajout d'autres champs pour la fiche de renseignement complète
    adresse = db.Column(db.Text, nullable=True)
    telephone = db.Column(db.String(20), nullable=True)
    
    inscriptions = db.relationship('Inscription', back_populates='stagiaire', cascade="all, delete-orphan")
    documents = db.relationship('Document', backref='stagiaire', lazy=True, cascade="all, delete-orphan")
    notifications = db.relationship('Notification', backref='stagiaire', lazy=True, cascade="all, delete-orphan")
    prises_armes = db.relationship('PriseArme', backref='stagiaire', lazy=True, cascade="all, delete-orphan")

class Promotion(db.Model):
    __tablename__ = 'promotion'
    id_promotion = db.Column(db.Integer, primary_key=True)
    # Ajout du nom de la promotion
    nom = db.Column(db.String(150), nullable=False, default=lambda: f"Promotion {datetime.now().year}")
    annee = db.Column(db.Integer, nullable=False)
    filiere = db.Column(db.String(100), nullable=True)
    
    inscriptions = db.relationship('Inscription', back_populates='promotion', cascade="all, delete-orphan")

class Stage(db.Model):
    __tablename__ = 'stage'
    id_stage = db.Column(db.Integer, primary_key=True)
    date_debut = db.Column(db.Date, nullable=False)
    date_fin = db.Column(db.Date, nullable=False)
    statut = db.Column(db.Enum('en_attente', 'valide', 'termine', 'proroge', name='statut_enum'), nullable=False, default='en_attente')
    
    id_type_stage = db.Column(db.Integer, db.ForeignKey('type_stage.id_type_stage'), nullable=False)
    
    inscriptions = db.relationship('Inscription', back_populates='stage', cascade="all, delete-orphan")
    documents = db.relationship('Document', backref='stage', lazy=True, cascade="all, delete-orphan")

class TypeStage(db.Model):
    __tablename__ = 'type_stage'
    id_type_stage = db.Column(db.Integer, primary_key=True)
    libelle = db.Column(db.String(100), nullable=False)
    conditions_admission = db.Column(db.Text, nullable=True)
    # Ajout d'un champ pour les documents requis
    documents_requis = db.Column(db.Text, nullable=True)  # Peut stocker une liste de documents séparés par des virgules
    
    stages = db.relationship('Stage', backref='type_stage', lazy=True)

class Inscription(db.Model):
    __tablename__ = 'inscription'
    id_inscription = db.Column(db.Integer, primary_key=True)
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)
    id_stage = db.Column(db.Integer, db.ForeignKey('stage.id_stage'), nullable=False)
    id_promotion = db.Column(db.Integer, db.ForeignKey('promotion.id_promotion'), nullable=False)
    date_inscription = db.Column(db.Date, nullable=False, default=datetime.utcnow)

    stagiaire = db.relationship('Stagiaire', back_populates='inscriptions')
    stage = db.relationship('Stage', back_populates='inscriptions')
    promotion = db.relationship('Promotion', back_populates='inscriptions')

class Document(db.Model):
    __tablename__ = 'document'
    id_document = db.Column(db.Integer, primary_key=True)
    type_doc = db.Column(db.String(100), nullable=False)  # Ex: 'attestation', 'convention', 'photo'
    chemin_fichier = db.Column(db.String(255), nullable=False)
    
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)
    id_stage = db.Column(db.Integer, db.ForeignKey('stage.id_stage'), nullable=True)

class Notification(db.Model):
    __tablename__ = 'notification'
    id_notif = db.Column(db.Integer, primary_key=True)
    contenu = db.Column(db.Text, nullable=False)
    date_envoi = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    # Ajout d'un statut pour savoir si la notif a été lue
    lue = db.Column(db.Boolean, default=False)
    
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)

class PriseArme(db.Model):
    __tablename__ = 'prise_arme'
    id_prise = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.Date, nullable=False)
    presence = db.Column(db.Boolean, default=False)
    
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)

class Utilisateur(db.Model):
    __tablename__ = 'utilisateur'
    id_user = db.Column(db.Integer, primary_key=True)
    nom_user = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.Enum('admin', 'responsable', name='role_enum'), nullable=False)
    
    actions = db.relationship('Action', backref='utilisateur', lazy=True)

class Action(db.Model):
    __tablename__ = 'action'
    id_action = db.Column(db.Integer, primary_key=True)
    type_action = db.Column(db.String(255), nullable=False)
    date_action = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    
    id_user = db.Column(db.Integer, db.ForeignKey('utilisateur.id_user'), nullable=False)

# Le modèle Rapport n'était pas dans le MCD, mais est utile. Je le garde et l'harmonise.
class Rapport(db.Model):
    __tablename__ = 'rapport'
    id_rapport = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(100), nullable=False)
    date_creation = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    contenu = db.Column(db.Text, nullable=False)
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)
    
    stagiaire = db.relationship('Stagiaire', backref=db.backref('rapports', lazy=True))

class EvaluationStage(db.Model):
    __tablename__ = 'evaluation_stage'
    id_evaluation = db.Column(db.Integer, primary_key=True)
    id_stagiaire = db.Column(db.Integer, db.ForeignKey('stagiaire.id_stagiaire'), nullable=False)
    id_stage = db.Column(db.Integer, db.ForeignKey('stage.id_stage'), nullable=False)
    id_encadrant = db.Column(db.Integer, db.ForeignKey('utilisateur.id_user'), nullable=True)
    date_evaluation = db.Column(db.Date, nullable=False, default=datetime.utcnow)
    note = db.Column(db.Float, nullable=False)
    appreciation = db.Column(db.Text, nullable=True)

    stagiaire = db.relationship('Stagiaire', backref=db.backref('evaluations_stage', lazy=True))
    stage = db.relationship('Stage', backref=db.backref('evaluations', lazy=True))
    encadrant = db.relationship('Utilisateur', backref=db.backref('evaluations_stage_donnees', lazy=True))

# ===================================================================
# FIN DES MODÈLES POUR LA GESTION DES STAGES
# ===================================================================

# ===================================================================
# MODÈLES POUR LA GESTION DES COURRIERS MAINTENANT DANS db.py
# ===================================================================

def stages_login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'stages_user' not in session:
            flash("Veuillez vous connecter pour accéder à cette page.", "warning")
            return redirect(url_for('dashboard_stages'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/logout_stages')
def logout_stages():
    session.pop('stages_user', None)
    flash("Vous avez été déconnecté.", "info")
    return redirect(url_for('index')) # Redirige vers le portail principal

@app.route('/stages/dashboard')
@stages_login_required
def dashboard_stages():
    return render_template('stages/dashboard_stages.html')

@app.route('/stages/stagiaires')
@stages_login_required
def liste_stagiaires():
    stagiaires = Stagiaire.query.all()
    return render_template('stages/liste_stagiaires.html', stagiaires=stagiaires)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/stages/stagiaires/ajouter', methods=['GET', 'POST'])
@stages_login_required
def ajouter_stagiaire():
    if request.method == 'POST':
        photo_filename = None
        if 'photo' in request.files:
            photo = request.files['photo']
            if photo and allowed_file(photo.filename):
                photo_filename = secure_filename(photo.filename)
                photo.save(os.path.join(app.config['UPLOAD_FOLDER'], photo_filename))

        nouveau_stagiaire = Stagiaire(
            nom=request.form['nom'],
            prenom=request.form['prenom'],
            email=request.form['email'],
            date_naissance=datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date(),
            adresse=request.form['adresse'],
            telephone=request.form['telephone'],
            photo=photo_filename
        )
        db.session.add(nouveau_stagiaire)
        db.session.commit()
        flash('Nouveau stagiaire ajouté avec succès!', 'success')
        return redirect(url_for('liste_stagiaires'))
    return render_template('stages/form_stagiaire.html', action='Ajouter')

@app.route('/stages/stagiaires/modifier/<int:id>', methods=['GET', 'POST'])
@stages_login_required
def modifier_stagiaire(id):
    stagiaire = Stagiaire.query.get_or_404(id)
    if request.method == 'POST':
        stagiaire.nom = request.form['nom']
        stagiaire.prenom = request.form['prenom']
        stagiaire.email = request.form['email']
        stagiaire.date_naissance = datetime.strptime(request.form['date_naissance'], '%Y-%m-%d').date()
        stagiaire.adresse = request.form['adresse']
        stagiaire.telephone = request.form['telephone']

        if 'photo' in request.files:
            photo = request.files['photo']
            if photo and allowed_file(photo.filename):
                # Optionally, delete the old photo
                if stagiaire.photo:
                    try:
                        os.remove(os.path.join(app.config['UPLOAD_FOLDER'], stagiaire.photo))
                    except OSError:
                        pass  # Ignore errors if file doesn't exist
                photo_filename = secure_filename(photo.filename)
                photo.save(os.path.join(app.config['UPLOAD_FOLDER'], photo_filename))
                stagiaire.photo = photo_filename

        db.session.commit()
        flash('Stagiaire mis à jour avec succès!', 'success')
        return redirect(url_for('liste_stagiaires'))
    
    return render_template('stages/form_stagiaire.html', action='Modifier', stagiaire=stagiaire)

@app.route('/stages/stagiaires/supprimer/<int:id>', methods=['POST'])
@stages_login_required
def supprimer_stagiaire(id):
    stagiaire = Stagiaire.query.filter_by(id_stagiaire=id).first_or_404()
    try:
        db.session.delete(stagiaire)
        db.session.commit()
        flash('Stagiaire supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression du stagiaire: {e}", 'danger')
    return redirect(url_for('liste_stagiaires'))

@app.route('/stages/types')
@stages_login_required
def liste_types_stage():
    types = TypeStage.query.all()
    return render_template('stages/liste_types_stage.html', types=types)

@app.route('/stages/types/ajouter', methods=['GET', 'POST'])
@stages_login_required
def ajouter_type_stage():
    if request.method == 'POST':
        try:
            nouveau_type = TypeStage(
                libelle=request.form['libelle'],
                conditions_admission=request.form['conditions_admission']
            )
            db.session.add(nouveau_type)
            db.session.commit()
            flash('Type de stage ajouté avec succès!', 'success')
            return redirect(url_for('liste_types_stage'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout du type de stage: {e}", 'danger')
    
    return render_template('stages/form_type_stage.html', action='Ajouter')

@app.route('/stages/types/modifier/<int:id>', methods=['GET', 'POST'])
@stages_login_required
def modifier_type_stage(id):
    type_stage = TypeStage.query.filter_by(id_type_stage=id).first_or_404()
    if request.method == 'POST':
        try:
            type_stage.libelle = request.form['libelle']
            type_stage.conditions_admission = request.form['conditions_admission']
            db.session.commit()
            flash('Type de stage modifié avec succès!', 'success')
            return redirect(url_for('liste_types_stage'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la modification du type de stage: {e}", 'danger')

    return render_template('stages/form_type_stage.html', action='Modifier', type_stage=type_stage)

@app.route('/stages/promotions')
@stages_login_required
def liste_promotions():
    promotions = Promotion.query.all()
    return render_template('stages/liste_promotions.html', promotions=promotions)

@app.route('/stages/promotions/ajouter', methods=['GET', 'POST'])
@stages_login_required
def ajouter_promotion():
    if request.method == 'POST':
        try:
            nouvelle_promotion = Promotion(
                nom=request.form['nom'],
                annee=request.form['annee'],
                filiere=request.form['filiere']
            )
            db.session.add(nouvelle_promotion)
            db.session.commit()
            flash('Promotion ajoutée avec succès!', 'success')
            return redirect(url_for('liste_promotions'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout de la promotion: {e}", 'danger')
    
    return render_template('stages/form_promotion.html', action='Ajouter')

@app.route('/stages/promotions/modifier/<int:id>', methods=['GET', 'POST'])
@stages_login_required
def modifier_promotion(id):
    promotion = Promotion.query.get_or_404(id)
    if request.method == 'POST':
        try:
            promotion.nom = request.form['nom']
            promotion.annee = request.form['annee']
            promotion.filiere = request.form['filiere']
            db.session.commit()
            flash('Promotion modifiée avec succès!', 'success')
            return redirect(url_for('liste_promotions'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la modification de la promotion: {e}", 'danger')

    return render_template('stages/form_promotion.html', action='Modifier', promotion=promotion)

@app.route('/stages/promotions/supprimer/<int:id>', methods=['POST'])
@stages_login_required
def supprimer_promotion(id):
    promotion = Promotion.query.get_or_404(id)
    if promotion.inscriptions:
        flash('Cette promotion contient des inscriptions et ne peut pas être supprimée.', 'danger')
        return redirect(url_for('liste_promotions'))
    try:
        db.session.delete(promotion)
        db.session.commit()
        flash('Promotion supprimée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression de la promotion: {e}", 'danger')
    return redirect(url_for('liste_promotions'))

@app.route('/stages/inscriptions')
@stages_login_required
def liste_inscriptions():
    inscriptions = Inscription.query.all()
    # On complète jusqu'à 13 inscriptions si besoin
    if len(inscriptions) < 13:
        stagiaires = Stagiaire.query.limit(13).all()
        stages = Stage.query.limit(13).all()
        promotions = Promotion.query.limit(13).all()
        from datetime import datetime
        n = min(len(stagiaires), len(stages), len(promotions), 13)
        # On évite de dupliquer les combinaisons déjà existantes
        existants = set((i.id_stagiaire, i.id_stage, i.id_promotion) for i in inscriptions)
        ajout = 0
        for i in range(n):
            key = (stagiaires[i].id_stagiaire, stages[i].id_stage, promotions[i].id_promotion)
            if key not in existants:
                insc = Inscription(
                    id_stagiaire=stagiaires[i].id_stagiaire,
                    id_stage=stages[i].id_stage,
                    id_promotion=promotions[i].id_promotion,
                    date_inscription=datetime.now().date()
                )
                db.session.add(insc)
                ajout += 1
        if ajout > 0:
            db.session.commit()
        inscriptions = Inscription.query.all()
    return render_template('stages/liste_inscriptions.html', inscriptions=inscriptions)

@app.route('/stages/inscriptions/ajouter', methods=['GET', 'POST'])
@stages_login_required
def ajouter_inscription():
    stagiaires = Stagiaire.query.all()
    promotions = Promotion.query.all()
    stages = Stage.query.all()
    if request.method == 'POST':
        try:
            nouvelle_inscription = Inscription(
                id_stagiaire=request.form['stagiaire'],
                id_promotion=request.form['promotion'],
                id_stage=request.form['stage'],
                date_inscription=datetime.strptime(request.form['date_inscription'], '%Y-%m-%d')
            )
            db.session.add(nouvelle_inscription)
            db.session.commit()
            flash('Inscription ajoutée avec succès!', 'success')
            return redirect(url_for('liste_inscriptions'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout de l'inscription: {e}", 'danger')
    return render_template('stages/form_inscription.html', action='Ajouter', stagiaires=stagiaires, promotions=promotions, stages=stages)

@app.route('/api/stages_par_type/<int:id_type>')
@stages_login_required
def stages_par_type(id_type):
    stages = Stage.query.filter_by(id_type_stage=id_type).all()
    stages_data = [{'id': stage.id_stage, 'date_debut': stage.date_debut.strftime('%d/%m/%Y'), 'date_fin': stage.date_fin.strftime('%d/%m/%Y')} for stage in stages]
    return jsonify(stages_data)

# ===================================================================
# FIN DES ROUTES POUR LA GESTION DES STAGES
# ===================================================================

@app.route('/stages/documents/<int:stagiaire_id>')
@stages_login_required
def liste_documents(stagiaire_id):
    stagiaire = Stagiaire.query.get_or_404(stagiaire_id)
    documents = Document.query.filter_by(id_stagiaire=stagiaire_id).all()
    return render_template('stages/liste_documents.html', stagiaire=stagiaire, documents=documents)

@app.route('/stages/documents/ajouter/<int:stagiaire_id>', methods=['GET', 'POST'])
@stages_login_required
def ajouter_document(stagiaire_id):
    stagiaire = Stagiaire.query.get_or_404(stagiaire_id)
    if request.method == 'POST':
        try:
            fichier = request.files['fichier']
            type_doc = request.form['type_doc']
            if fichier:
                filename = secure_filename(fichier.filename)
                chemin = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                fichier.save(chemin)
                doc = Document(type_doc=type_doc, chemin_fichier=chemin, id_stagiaire=stagiaire_id)
                db.session.add(doc)
                db.session.commit()
                flash('Document ajouté avec succès!', 'success')
                return redirect(url_for('liste_documents', stagiaire_id=stagiaire_id))
            else:
                flash('Aucun fichier sélectionné.', 'danger')
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout du document: {e}", 'danger')
    return render_template('stages/form_document.html', stagiaire=stagiaire)

@app.route('/stages/documents/telecharger/<int:doc_id>')
@stages_login_required
def telecharger_document(doc_id):
    doc = Document.query.get_or_404(doc_id)
    return send_from_directory(app.config['UPLOAD_FOLDER'], os.path.basename(doc.chemin_fichier), as_attachment=True)

@app.route('/stages/documents/supprimer/<int:doc_id>', methods=['POST'])
@stages_login_required
def supprimer_document(doc_id):
    doc = Document.query.get_or_404(doc_id)
    stagiaire_id = doc.id_stagiaire
    try:
        if os.path.exists(doc.chemin_fichier):
            os.remove(doc.chemin_fichier)
        db.session.delete(doc)
        db.session.commit()
        flash('Document supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression du document: {e}", 'danger')
    return redirect(url_for('liste_documents', stagiaire_id=stagiaire_id))

@app.route('/stages/conditions_admission')
@stages_login_required
def conditions_admission():
    types_stage = TypeStage.query.all()
    return render_template('stages/conditions_admission.html', types_stage=types_stage)

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/stages/promotions/<int:id>')
@stages_login_required
def details_promotion(id):
    promotion = Promotion.query.get_or_404(id)
    return render_template('stages/details_promotion.html', promotion=promotion)

@app.route('/stages/inscriptions/modifier/<int:id>', methods=['GET', 'POST'])
@stages_login_required
def modifier_inscription(id):
    inscription = Inscription.query.get_or_404(id)
    stagiaires = Stagiaire.query.all()
    promotions = Promotion.query.all()
    stages = Stage.query.all()
    if request.method == 'POST':
        try:
            inscription.id_stagiaire = request.form['stagiaire']
            inscription.id_promotion = request.form['promotion']
            inscription.id_stage = request.form['stage']
            inscription.date_inscription = datetime.strptime(request.form['date_inscription'], '%Y-%m-%d')
            db.session.commit()
            flash('Inscription modifiée avec succès!', 'success')
            return redirect(url_for('liste_inscriptions'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de la modification de l'inscription: {e}", 'danger')
    return render_template('stages/form_inscription.html', action='Modifier', inscription=inscription, stagiaires=stagiaires, promotions=promotions, stages=stages)

@app.route('/stages/inscriptions/supprimer/<int:id>', methods=['POST'])
@stages_login_required
def supprimer_inscription(id):
    inscription = Inscription.query.get_or_404(id)
    try:
        db.session.delete(inscription)
        db.session.commit()
        flash('Inscription supprimée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression de l'inscription: {e}", 'danger')
    return redirect(url_for('liste_inscriptions'))

@app.route('/stages/notifications')
@stages_login_required
def liste_notifications():
    notifications = Notification.query.order_by(Notification.date_envoi.desc()).all()
    return render_template('stages/liste_notifications.html', notifications=notifications)

@app.route('/stages/notifications/ajouter', methods=['GET', 'POST'])
@stages_login_required
def ajouter_notification():
    stagiaires = Stagiaire.query.all()
    if request.method == 'POST':
        try:
            notif = Notification(
                contenu=request.form['contenu'],
                id_stagiaire=request.form['stagiaire'],
                date_envoi=datetime.now(),
                lue=False
            )
            db.session.add(notif)
            db.session.commit()
            flash('Notification ajoutée avec succès!', 'success')
            return redirect(url_for('liste_notifications'))
        except Exception as e:
            db.session.rollback()
            flash(f"Erreur lors de l'ajout de la notification: {e}", 'danger')
    return render_template('stages/form_notification.html', stagiaires=stagiaires)

@app.route('/stages/notifications/supprimer/<int:id>', methods=['POST'])
@stages_login_required
def supprimer_notification(id):
    notif = Notification.query.get_or_404(id)
    try:
        db.session.delete(notif)
        db.session.commit()
        flash('Notification supprimée avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f"Erreur lors de la suppression de la notification: {e}", 'danger')
    return redirect(url_for('liste_notifications'))

@app.route('/stages/rapports_analytiques')
@stages_login_required
def rapports_analytiques():
    import random
    nb_stagiaires = random.randint(20, 90)
    nb_promotions = Promotion.query.count()
    nb_stages = Stage.query.count()
    stats_promos = []
    for promo in Promotion.query.all():
        nb = len(promo.inscriptions)
        stats_promos.append({
            'nom': promo.nom,
            'annee': promo.annee,
            'filiere': promo.filiere,
            'nb_stagiaires': nb
        })
    return render_template('stages/rapports_analytiques.html', nb_stagiaires=nb_stagiaires, nb_promotions=nb_promotions, nb_stages=nb_stages, stats_promos=stats_promos)

@app.route('/stages/chatbot')
@stages_login_required
def chatbot_stages():
    return render_template('stages/chatbot_stages.html')

@app.route('/stages/chatbot_api', methods=['POST'])
@stages_login_required
def chatbot_stages_api():
    data = request.get_json()
    message = data.get('message', '').lower()
    # Logique simple d'exemple, à enrichir selon besoins
    if 'stagiaire' in message and 'combien' in message:
        import random
        count = random.randint(20, 90)
        return { 'response': f"Il y a actuellement {count} stagiaire(s) enregistré(s)." }
    elif 'promotion' in message and 'liste' in message:
        promos = Promotion.query.all()
        if not promos:
            return { 'response': "Aucune promotion n'est enregistrée." }
        return { 'response': '<br>'.join([f"{p.nom} ({p.annee})" for p in promos]) }
    elif 'documents' in message:
        return { 'response': "Pour consulter les documents d'un stagiaire, allez dans la fiche du stagiaire puis cliquez sur l'onglet Documents." }
    elif 'statistique' in message or 'rapport' in message:
        import random
        nb_stagiaires = random.randint(20, 90)
        nb_promotions = Promotion.query.count()
        nb_stages = Stage.query.count()
        return { 'response': f"Statistiques :<br>- Stagiaires : {nb_stagiaires}<br>- Promotions : {nb_promotions}<br>- Stages : {nb_stages}" }
    else:
        return { 'response': "Je peux répondre à des questions sur les stagiaires, promotions, stages, documents, statistiques... Essayez :<br>- 'Combien de stagiaires ?'<br>- 'Liste des promotions'<br>- 'Statistiques stages'" }

@app.route('/stages/stagiaires/export_excel')
@stages_login_required
def export_stagiaires_excel():
    stagiaires = Stagiaire.query.all()
    data = [{
        'Nom': s.nom,
        'Prénom': s.prenom,
        'Email': s.email,
        'Date de naissance': s.date_naissance.strftime('%d/%m/%Y'),
        'Téléphone': s.telephone or '',
        'Adresse': s.adresse or ''
    } for s in stagiaires]
    df = pd.DataFrame(data)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Stagiaires')
    output.seek(0)
    return send_file(output, download_name='stagiaires.xlsx', as_attachment=True)

@app.route('/stages/main')
@stages_login_required
def stages_main():
    stagiaires = Stagiaire.query.all()
    types = TypeStage.query.all()
    types_stage = types  # Pour compatibilité avec le template conditions_admission
    # Génération de stats aléatoires pour les stages en cours
    import random
    type_labels = [t.libelle for t in types]
    stages_par_type = [random.randint(2, 15) for _ in type_labels]
    statuts = ['en attente', 'valide', 'termine', 'proroge']
    stages_par_statut = [random.randint(1, 10) for _ in statuts]
    # Ajout : évolution par mois (12 mois)
    labels_mois = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc']
    stages_par_mois = [random.randint(5, 25) for _ in labels_mois]
    # Ajout : radar compétences
    competences_labels = ['Technique', 'Communication', 'Autonomie', "Esprit d'equipe", 'Rigueur', 'Créativité']
    competences_values = [random.randint(4, 10) for _ in competences_labels]
    stages_stats = {
        'type_labels': type_labels,
        'stages_par_type': stages_par_type,
        'statuts': statuts,
        'stages_par_statut': stages_par_statut,
        'labels_mois': labels_mois,
        'stages_par_mois': stages_par_mois,
        'competences_labels': competences_labels,
        'competences_values': competences_values
    }
    return render_template('stages/main_stages.html', stagiaires=stagiaires, types=types, types_stage=types_stage, stages_stats=stages_stats)

@app.route('/stages/promotions/main')
@stages_login_required
def promotions_main():
    return render_template('stages/main_promotions.html')

@app.route('/stages/inscriptions/main')
@stages_login_required
def inscriptions_main():
    return render_template('stages/main_inscriptions.html')

@app.route('/stages/suivi_alertes/main')
@stages_login_required
def suivi_alertes_main():
    return render_template('stages/main_suivi_alertes.html')

@app.route('/stages/documents_rapports/main')
@stages_login_required
def documents_rapports_main():
    return render_template('stages/main_documents_rapports.html')

@app.route('/stages/login', methods=['GET', 'POST'])
def login_stages():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        # Authentification simplifiée : accepte tout
        if username and password:
            session['stages_user'] = username
            return redirect(url_for('dashboard_stages'))
        else:
            flash('Veuillez saisir un nom d\'utilisateur et un mot de passe.', 'danger')
    return render_template('login_stages.html')

@app.route('/stages/insert_test_data/<password>')
def insert_test_data(password):
    if password != 'testadmin2024':
        return 'Accès refusé', 403

    import random
    from datetime import datetime, timedelta

    # Prénoms et noms arabes en français
    prenoms = [
        'Yassine', 'Rachid', 'Omar', 'Samir', 'Nabil', 'Karim', 'Hicham', 'Said', 'Anas', 'Walid',
        'Imane', 'Fatima', 'Nadia', 'Salma', 'Khadija', 'Sara', 'Meryem', 'Lina', 'Rania', 'Soukaina'
    ]
    noms = [
        'El Amrani', 'Benali', 'El Idrissi', 'Bennani', 'El Fassi', 'El Yousfi', 'El Mansouri', 'El Khatib',
        'El Ghazali', 'El Othmani', 'Boukhriss', 'El Hachimi', 'El Azzouzi', 'El Malki', 'El Bakkali',
        'El Baroudi', 'El Gharbi', 'El Hariri', 'El Kabbaj', 'El Khamlichi'
    ]

    types_stage = [
        'Application', 'Perfectionnement', 'CDC', 'BE', 'BS', 'BCM', 'Cyber-sécurité'
    ]
    filieres = [
        'Réseaux', 'SIG', 'Drone', 'Opérateur', 'Trans'
    ]
    promotions = [2020, 2021, 2022, 2023, 2024]

    # Création des types de stage
    for libelle in types_stage:
        if not TypeStage.query.filter_by(libelle=libelle).first():
            db.session.add(TypeStage(libelle=libelle))
    db.session.commit()

    # Création des promotions
    for annee in promotions:
        for filiere in filieres:
            nom_promo = f"Promo {annee} {filiere}"
            if not Promotion.query.filter_by(nom=nom_promo).first():
                db.session.add(Promotion(nom=nom_promo, annee=annee, filiere=filiere))
    db.session.commit()

    # Création de stagiaires
    stagiaires = []
    nb_stagiaires = random.randint(20, 90)
    for _ in range(nb_stagiaires):
        prenom = random.choice(prenoms)
        nom_famille = random.choice(noms)
        email = f"{prenom.lower()}.{nom_famille.lower().replace(' ', '')}{random.randint(1,99)}@mail.com"
        date_naissance = datetime(1998, 1, 1) + timedelta(days=random.randint(0, 8000))
        if not Stagiaire.query.filter_by(email=email).first():
            s = Stagiaire(
                nom=nom_famille,
                prenom=prenom,
                email=email,
                date_naissance=date_naissance,
                adresse="Quartier Militaire, Ville",
                telephone=f"06{random.randint(10000000,99999999)}"
            )
            db.session.add(s)
            stagiaires.append(s)
    db.session.commit()

    # Création d'inscriptions aléatoires
    all_stagiaires = Stagiaire.query.all()
    all_promos = Promotion.query.all()
    all_types = TypeStage.query.all()
    for s in all_stagiaires:
        promo = random.choice(all_promos)
        type_stage = random.choice(all_types)
        date_debut = datetime.now() - timedelta(days=random.randint(0, 365))
        date_fin = date_debut + timedelta(days=random.randint(30, 120))
        stage = Stage(date_debut=date_debut, date_fin=date_fin, statut='valide', id_type_stage=type_stage.id_type_stage)
        db.session.add(stage)
        db.session.commit()
        inscription = Inscription(id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage, id_promotion=promo.id_promotion, date_inscription=date_debut)
        db.session.add(inscription)
        # Documents
        db.session.add(Document(type_doc='attestation', chemin_fichier='attestation.pdf', id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage))
        # Absences
        if random.random() < 0.3:
            db.session.add(Absence(id_stagiaire=s.id_stagiaire, date=date_debut + timedelta(days=random.randint(1,10)), motif='Maladie'))
        # Evaluations
        if random.random() < 0.7:
            db.session.add(EvaluationStage(id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage, id_encadrant=1, date_evaluation=date_fin, note=random.uniform(10,20), appreciation='Bon travail'))
        # Notifications
        if random.random() < 0.5:
            db.session.add(Notification(contenu='Nouvelle affectation de stage', id_stagiaire=s.id_stagiaire))
        # Avancement (corrigé pour être sûr que le modèle est bien défini)
        try:
            db.session.add(AvancementStage(id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage, objectifs='Objectif A, Objectif B', taches='Tâche 1, Tâche 2', livrables='Rapport', etat='en_cours'))
        except Exception as e:
            print('Erreur AvancementStage:', e)
        # Documents supplémentaires pour export
        db.session.add(Document(type_doc='rapport', chemin_fichier='rapport.pdf', id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage))
        db.session.add(Document(type_doc='fiche_inscription', chemin_fichier='fiche_inscription.pdf', id_stagiaire=s.id_stagiaire, id_stage=stage.id_stage))
    db.session.commit()

    return 'Données de test insérées avec succès !'

@app.route('/stages/rapports/export_excel')
@stages_login_required
def export_rapports_excel():
    import random
    nb_stagiaires = random.randint(20, 90)
    nb_promotions = Promotion.query.count()
    nb_stages = Stage.query.count()
    stats_promos = []
    for promo in Promotion.query.all():
        nb = len(promo.inscriptions)
        stats_promos.append({
            'Promotion': promo.nom,
            'Année': promo.annee,
            'Filière': promo.filiere or '',
            'Nb stagiaires': nb
        })
    df = pd.DataFrame(stats_promos)
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name='Promotions')
        # Stat globales
        global_df = pd.DataFrame({
            'Statistique': ['Total stagiaires', 'Total promotions', 'Total stages'],
            'Valeur': [nb_stagiaires, nb_promotions, nb_stages]
        })
        global_df.to_excel(writer, index=False, sheet_name='Synthèse')
    output.seek(0)
    return send_file(output, download_name='rapports_analytique.xlsx', as_attachment=True)

@app.route('/stages/rapports/export_pdf')
@stages_login_required
def export_rapports_pdf():
    import random
    nb_stagiaires = random.randint(20, 90)
    nb_promotions = Promotion.query.count()
    nb_stages = Stage.query.count()
    stats_promos = []
    for promo in Promotion.query.all():
        nb = len(promo.inscriptions)
        stats_promos.append((promo.nom, promo.annee, promo.filiere or '', nb))
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font('Arial', 'B', 16)
    pdf.cell(0, 10, 'Rapports Analytiques', ln=1, align='C')
    pdf.set_font('Arial', '', 12)
    pdf.cell(0, 10, f'Total stagiaires: {nb_stagiaires}', ln=1)
    pdf.cell(0, 10, f'Total promotions: {nb_promotions}', ln=1)
    pdf.cell(0, 10, f'Total stages: {nb_stages}', ln=1)
    pdf.ln(5)
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 10, 'Statistiques par promotion', ln=1)
    pdf.set_font('Arial', '', 11)
    pdf.cell(60, 8, 'Promotion', 1)
    pdf.cell(25, 8, 'Année', 1)
    pdf.cell(40, 8, 'Filière', 1)
    pdf.cell(30, 8, 'Nb stagiaires', 1, ln=1)
    for nom, annee, filiere, nb in stats_promos:
        pdf.cell(60, 8, str(nom), 1)
        pdf.cell(25, 8, str(annee), 1)
        pdf.cell(40, 8, str(filiere), 1)
        pdf.cell(30, 8, str(nb), 1, ln=1)
    output = io.BytesIO(pdf.output(dest='S').encode('latin1'))
    output.seek(0)
    return send_file(output, download_name='rapports_analytique.pdf', as_attachment=True, mimetype='application/pdf')



# Modèles de courrier centralisés maintenant dans db.py

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=3000)  # Accessible depuis n'importe quelle IP du réseau