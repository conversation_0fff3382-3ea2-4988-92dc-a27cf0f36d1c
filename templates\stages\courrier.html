{% extends 'stages/base_stages.html' %}
{% block title %}Gestion du Courrier - Division Instruction{% endblock %}

{% block extra_css %}
<style>
/* Styles spécifiques pour l'interface courrier */
.courrier-header {
    background: #6B8E23;
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* Désactiver le pattern de camouflage pour les en-têtes des courriers */
.courrier-header::before {
    display: none !important;
}

.courrier-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.courrier-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    border-radius: 20px 20px 0 0;
}

.courrier-tabs .nav-link.active {
    background-color: #f8f9fa;
    color: #27ae60;
    font-weight: 600;
}

.courrier-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.courrier-card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.badge-urgence {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
}

.badge-action {
    background-color: #e74c3c;
    color: white;
}

.badge-info {
    background-color: #3498db;
    color: white;
}

.btn-scan {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-scan:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.document-preview {
    max-width: 100px;
    max-height: 100px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête avec statistiques -->
    <div class="courrier-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Gestion du Courrier - Division Instruction
                </h2>
                <p class="mb-0 mt-2">Interface de consultation et d'envoi des courriers</p>
            </div>
            <div class="col-md-4">
                <div class="courrier-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="count-recus">0</span>
                        <span class="stat-label">Reçus</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="count-action">0</span>
                        <span class="stat-label">Pour Action</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="count-envoyes">0</span>
                        <span class="stat-label">Envoyés</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation par onglets -->
    <ul class="nav nav-tabs courrier-tabs mb-4" id="courrierTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="recus-tab" data-bs-toggle="tab" data-bs-target="#recus" type="button" role="tab">
                <i class="fas fa-inbox me-2"></i>Courriers Reçus
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="envoyer-tab" data-bs-toggle="tab" data-bs-target="#envoyer" type="button" role="tab">
                <i class="fas fa-paper-plane me-2"></i>Envoyer un Courrier
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="historique-tab" data-bs-toggle="tab" data-bs-target="#historique" type="button" role="tab">
                <i class="fas fa-history me-2"></i>Historique
            </button>
        </li>
    </ul>

    <!-- Contenu des onglets -->
    <div class="tab-content" id="courrierTabContent">
        <!-- Onglet Courriers Reçus -->
        <div class="tab-pane fade show active" id="recus" role="tabpanel">
            <div class="row">
                <!-- Filtres -->
                <div class="col-md-3">
                    <div class="courrier-card">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Type</label>
                                <select class="form-select" id="filter-type">
                                    <option value="">Tous</option>
                                    <option value="action">Pour Action</option>
                                    <option value="info">Pour Information</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Urgence</label>
                                <select class="form-select" id="filter-urgence">
                                    <option value="">Toutes</option>
                                    <option value="extreme">Extrême</option>
                                    <option value="urgent">Urgent</option>
                                    <option value="routine">Routine</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Date</label>
                                <input type="date" class="form-control" id="filter-date">
                            </div>
                            <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Liste des courriers -->
                <div class="col-md-9">
                    <div class="courrier-card">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>Courriers Reçus</h6>
                            <span class="badge bg-light text-dark">Total: <span id="total-recus">0</span></span>
                        </div>
                        <div class="card-body">
                            <div id="courriers-list">
                                <!-- Contenu dynamique -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Envoyer un Courrier -->
        <div class="tab-pane fade" id="envoyer" role="tabpanel">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="courrier-card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Nouveau Courrier à Envoyer</h6>
                        </div>
                        <div class="card-body">
                            <form id="form-envoyer-courrier">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ID Courrier *</label>
                                            <input type="text" class="form-control" id="id-courrier-envoyer" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">N° Écrit Division *</label>
                                            <input type="text" class="form-control" id="numero-ecrit-division" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Nature du Courrier *</label>
                                            <select class="form-select" id="nature-courrier" required>
                                                <option value="">Choisir...</option>
                                                <option value="message">Message</option>
                                                <option value="nds">NDS</option>
                                                <option value="note_royale">Note Royale</option>
                                                <option value="decision">Décision</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date de Départ *</label>
                                            <input type="date" class="form-control" id="date-depart" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Objet *</label>
                                    <textarea class="form-control" id="objet-courrier" rows="3" required placeholder="Objet du courrier..."></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Organe Destinataire *</label>
                                    <input type="text" class="form-control" id="destinataire-externe" required placeholder="Ex: Commandement Régional Sud">
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Le courrier sera transmis via le module principal de gestion du courrier
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Observations</label>
                                    <textarea class="form-control" id="observations" rows="2" placeholder="Observations particulières..."></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Envoyer le Courrier
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Onglet Historique -->
        <div class="tab-pane fade" id="historique" role="tabpanel">
            <div class="courrier-card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-history me-2"></i>Historique des Courriers Envoyés</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Date</th>
                                    <th>Nature</th>
                                    <th>Objet</th>
                                    <th>Destinataire</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="historique-list">
                                <!-- Contenu dynamique -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour scanner un document -->
<div class="modal fade" id="modalScanDocument" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-scan me-2"></i>Scanner un Document
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-file-pdf fa-4x text-danger mb-3"></i>
                        <h5>Joindre un Document Scanné</h5>
                        <p class="text-muted">Sélectionnez le fichier PDF du courrier scanné</p>
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="file-scan" accept=".pdf,.jpg,.jpeg,.png">
                    </div>
                    <div id="preview-scan" class="mb-3" style="display: none;">
                        <!-- Aperçu du document -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="uploadScan()">
                    <i class="fas fa-upload me-2"></i>Joindre le Document
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Variables globales
let courriersRecus = [];
let courriersEnvoyes = [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    loadMockData();
    updateStats();
    renderCourriersRecus();
    renderHistorique();
    
    // Date par défaut
    document.getElementById('date-depart').value = new Date().toISOString().split('T')[0];
    
    // Événements
    setupEvents();
});

// Charger des données de test
function loadMockData() {
    // Courriers reçus par la Division Instruction
    courriersRecus = [
        {
            id: 'CA-001',
            type: 'action',
            urgence: 'urgent',
            date_arrivee: '2024-01-15',
            nature: 'message',
            numero_ecrit: 'MSG-2024-001',
            expediteur: 'Commandement Régional Sud',
            objet: 'Demande de rapport mensuel des activités de formation',
            annotation: 'Traitement prioritaire demandé',
            document_scanne: null,
            lu: false
        },
        {
            id: 'CA-003',
            type: 'info',
            urgence: 'routine',
            date_arrivee: '2024-01-16',
            nature: 'nds',
            numero_ecrit: 'NDS-2024-003',
            expediteur: 'État-Major Général',
            objet: 'Nouvelles directives pour la formation continue',
            annotation: 'Pour information et application',
            document_scanne: null,
            lu: true
        }
    ];

    // Courriers envoyés par la Division Instruction
    courriersEnvoyes = [
        {
            id: 'CE-DIV-001',
            numero_ecrit: 'INST-2024-001',
            date_depart: '2024-01-17',
            nature: 'message',
            objet: 'Réponse au rapport mensuel des activités',
            destinataire: 'Commandement Régional Sud',
            statut: 'envoyé'
        }
    ];
}

// Mettre à jour les statistiques
function updateStats() {
    const totalRecus = courriersRecus.length;
    const pourAction = courriersRecus.filter(c => c.type === 'action').length;
    const totalEnvoyes = courriersEnvoyes.length;

    document.getElementById('count-recus').textContent = totalRecus;
    document.getElementById('count-action').textContent = pourAction;
    document.getElementById('count-envoyes').textContent = totalEnvoyes;
    document.getElementById('total-recus').textContent = totalRecus;
}

// Configurer les événements
function setupEvents() {
    // Filtres
    document.getElementById('filter-type').addEventListener('change', renderCourriersRecus);
    document.getElementById('filter-urgence').addEventListener('change', renderCourriersRecus);
    document.getElementById('filter-date').addEventListener('change', renderCourriersRecus);

    // Formulaire d'envoi
    document.getElementById('form-envoyer-courrier').addEventListener('submit', function(e) {
        e.preventDefault();
        envoyerCourrier();
    });

    // Upload de fichier
    document.getElementById('file-scan').addEventListener('change', previewScan);
}

// Rendre la liste des courriers reçus
function renderCourriersRecus() {
    const container = document.getElementById('courriers-list');
    container.innerHTML = '';

    // Appliquer les filtres
    let courriers = [...courriersRecus];

    const filterType = document.getElementById('filter-type').value;
    const filterUrgence = document.getElementById('filter-urgence').value;
    const filterDate = document.getElementById('filter-date').value;

    if (filterType) courriers = courriers.filter(c => c.type === filterType);
    if (filterUrgence) courriers = courriers.filter(c => c.urgence === filterUrgence);
    if (filterDate) courriers = courriers.filter(c => c.date_arrivee === filterDate);

    if (courriers.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">Aucun courrier trouvé</div>';
        return;
    }

    courriers.forEach((courrier, index) => {
        const urgenceBadge = getUrgenceBadge(courrier.urgence);
        const typeBadge = courrier.type === 'action' ?
            '<span class="badge badge-action">POUR ACTION</span>' :
            '<span class="badge badge-info">POUR INFO</span>';

        const luIcon = courrier.lu ?
            '<i class="fas fa-envelope-open text-success" title="Lu"></i>' :
            '<i class="fas fa-envelope text-warning" title="Non lu"></i>';

        const scanButton = courrier.document_scanne ?
            '<button class="btn btn-sm btn-outline-success me-1" onclick="voirDocument(' + index + ')" title="Voir document"><i class="fas fa-file-pdf"></i></button>' :
            '<button class="btn btn-sm btn-scan" onclick="scannerDocument(' + index + ')" title="Scanner document"><i class="fas fa-scan"></i></button>';

        const card = `
            <div class="card mb-3 ${!courrier.lu ? 'border-warning' : ''}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-1 text-center">
                            ${luIcon}
                        </div>
                        <div class="col-md-2">
                            <strong>${courrier.id}</strong><br>
                            <small class="text-muted">${formatDate(courrier.date_arrivee)}</small>
                        </div>
                        <div class="col-md-2">
                            ${urgenceBadge}<br>
                            ${typeBadge}
                        </div>
                        <div class="col-md-4">
                            <strong>${courrier.objet}</strong><br>
                            <small class="text-muted">De: ${courrier.expediteur}</small>
                        </div>
                        <div class="col-md-3 text-end">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="voirDetails(${index})" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${scanButton}
                            ${!courrier.lu ? '<button class="btn btn-sm btn-outline-success" onclick="marquerLu(' + index + ')" title="Marquer comme lu"><i class="fas fa-check"></i></button>' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML += card;
    });
}

// Rendre l'historique
function renderHistorique() {
    const tbody = document.getElementById('historique-list');
    tbody.innerHTML = '';

    courriersEnvoyes.forEach((courrier, index) => {
        const row = `
            <tr>
                <td><strong>${courrier.id}</strong></td>
                <td>${formatDate(courrier.date_depart)}</td>
                <td>${getNatureBadge(courrier.nature)}</td>
                <td>${courrier.objet}</td>
                <td>${courrier.destinataire}</td>
                <td><span class="badge bg-success">Envoyé</span></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="voirDetailsEnvoye(${index})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Envoyer un courrier
function envoyerCourrier() {
    const id = document.getElementById('id-courrier-envoyer').value;
    const numeroEcrit = document.getElementById('numero-ecrit-division').value;
    const nature = document.getElementById('nature-courrier').value;
    const dateDepart = document.getElementById('date-depart').value;
    const objet = document.getElementById('objet-courrier').value;
    const destinataire = document.getElementById('destinataire-externe').value;
    const observations = document.getElementById('observations').value;

    if (!id || !numeroEcrit || !nature || !dateDepart || !objet || !destinataire) {
        alert('Veuillez remplir tous les champs obligatoires !');
        return;
    }

    const nouveauCourrier = {
        id: id,
        numero_ecrit: numeroEcrit,
        date_depart: dateDepart,
        nature: nature,
        objet: objet,
        destinataire: destinataire,
        observations: observations,
        statut: 'envoyé'
    };

    courriersEnvoyes.push(nouveauCourrier);

    // Simulation d'envoi au module principal
    console.log('📧 Courrier Division Instruction envoyé au module principal:', nouveauCourrier);

    // Simulation d'API call vers le module principal
    simulerEnvoiModulePrincipal(nouveauCourrier);

    // Réinitialiser le formulaire
    document.getElementById('form-envoyer-courrier').reset();
    document.getElementById('date-depart').value = new Date().toISOString().split('T')[0];

    // Mettre à jour l'affichage
    updateStats();
    renderHistorique();

    alert('✅ Courrier Division Instruction envoyé avec succès au module principal !\n\n📋 Référence: ' + nouveauCourrier.id + '\n🏢 Division: Instruction\n📧 Destinataire: ' + nouveauCourrier.destinataire);
}

// Fonctions utilitaires
function getUrgenceBadge(urgence) {
    const badges = {
        'extreme': '<span class="badge bg-danger">Extrême</span>',
        'urgent': '<span class="badge bg-warning">Urgent</span>',
        'routine': '<span class="badge bg-secondary">Routine</span>'
    };
    return badges[urgence] || '<span class="badge bg-light text-dark">-</span>';
}

function getNatureBadge(nature) {
    const badges = {
        'message': '<span class="badge bg-info">Message</span>',
        'nds': '<span class="badge bg-primary">NDS</span>',
        'note_royale': '<span class="badge bg-warning text-dark">Note Royale</span>',
        'decision': '<span class="badge bg-success">Décision</span>'
    };
    return badges[nature] || '<span class="badge bg-light text-dark">-</span>';
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('fr-FR');
}

function resetFilters() {
    document.getElementById('filter-type').value = '';
    document.getElementById('filter-urgence').value = '';
    document.getElementById('filter-date').value = '';
    renderCourriersRecus();
}

function marquerLu(index) {
    courriersRecus[index].lu = true;
    renderCourriersRecus();
    updateStats();
}

function scannerDocument(index) {
    // Stocker l'index pour l'upload
    window.currentCourrierIndex = index;
    const modal = new bootstrap.Modal(document.getElementById('modalScanDocument'));
    modal.show();
}

function previewScan() {
    const file = document.getElementById('file-scan').files[0];
    const preview = document.getElementById('preview-scan');

    if (file) {
        preview.style.display = 'block';
        if (file.type.includes('image')) {
            preview.innerHTML = `<img src="${URL.createObjectURL(file)}" class="document-preview" alt="Aperçu">`;
        } else {
            preview.innerHTML = `<i class="fas fa-file-pdf fa-3x text-danger"></i><br><small>${file.name}</small>`;
        }
    } else {
        preview.style.display = 'none';
    }
}

function uploadScan() {
    const file = document.getElementById('file-scan').files[0];
    if (!file) {
        alert('Veuillez sélectionner un fichier !');
        return;
    }

    // Simulation d'upload
    const index = window.currentCourrierIndex;
    courriersRecus[index].document_scanne = {
        nom: file.name,
        taille: file.size,
        type: file.type,
        date_upload: new Date().toISOString()
    };

    // Fermer la modale
    const modal = bootstrap.Modal.getInstance(document.getElementById('modalScanDocument'));
    modal.hide();

    // Réinitialiser le formulaire
    document.getElementById('file-scan').value = '';
    document.getElementById('preview-scan').style.display = 'none';

    // Mettre à jour l'affichage
    renderCourriersRecus();

    alert('✅ Document scanné et joint avec succès !');
}

function voirDetails(index) {
    const courrier = courriersRecus[index];
    alert(`Détails du courrier ${courrier.id}:\n\nObjet: ${courrier.objet}\nExpéditeur: ${courrier.expediteur}\nAnnotation: ${courrier.annotation}\nType: ${courrier.type.toUpperCase()}`);
}

function voirDetailsEnvoye(index) {
    const courrier = courriersEnvoyes[index];
    alert(`Détails du courrier envoyé ${courrier.id}:\n\nObjet: ${courrier.objet}\nDestinataire: ${courrier.destinataire}\nStatut: ${courrier.statut}`);
}

function voirDocument(index) {
    const courrier = courriersRecus[index];
    if (courrier.document_scanne) {
        alert(`Document scanné: ${courrier.document_scanne.nom}\nTaille: ${(courrier.document_scanne.taille / 1024).toFixed(2)} KB\nDate: ${formatDate(courrier.document_scanne.date_upload)}`);
    }
}

// Simulation d'échange avec le module principal
function simulerEnvoiModulePrincipal(courrier) {
    // Simulation d'une API REST vers le module principal
    const payload = {
        source_division: 'instruction',
        courrier: courrier,
        timestamp: new Date().toISOString(),
        statut: 'transmis'
    };

    // Log pour simulation
    console.log('🔄 Transmission vers module principal:', payload);

    // Simulation de réponse du module principal
    setTimeout(() => {
        console.log('✅ Accusé de réception du module principal:', {
            id_transmission: 'TRANS-' + Date.now(),
            courrier_id: courrier.id,
            statut: 'reçu_module_principal',
            timestamp: new Date().toISOString()
        });
    }, 1000);
}

// Simulation de réception d'un nouveau courrier du module principal
function simulerReceptionCourrier() {
    const nouveauCourrier = {
        id: 'CA-' + String(Date.now()).slice(-3),
        type: 'action',
        urgence: 'routine',
        date_arrivee: new Date().toISOString().split('T')[0],
        nature: 'message',
        numero_ecrit: 'MSG-2024-' + String(Date.now()).slice(-3),
        expediteur: 'Module Principal Courrier',
        objet: 'Nouveau courrier reçu via le système central',
        annotation: 'Courrier transmis automatiquement',
        document_scanne: null,
        lu: false
    };

    courriersRecus.unshift(nouveauCourrier);
    updateStats();
    renderCourriersRecus();

    // Notification
    if (Notification.permission === 'granted') {
        new Notification('Nouveau courrier reçu', {
            body: `Division Instruction: ${nouveauCourrier.objet}`,
            icon: '/static/images/logo.png'
        });
    }
}
</script>
{% endblock %}
