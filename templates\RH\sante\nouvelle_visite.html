{% extends "rh/base_rh.html" %}

{% block title %}Nouvelle Visite Médicale - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-heartbeat"></i>
                                Nouvelle Visite Médicale
                            </h2>
                            <small class="text-muted">Enregistrement d'une nouvelle visite médicale</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.sante_medicale') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Section 1: Informations de Base -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations de Base
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Militaire Concerné *</label>
                            <select name="militaire_id" class="form-control form-control-military" required>
                                <option value="">Sélectionner un militaire...</option>
                                {% for militaire in personnel %}
                                <option value="{{ militaire.id }}">
                                    {{ militaire.grade_actuel or '' }} {{ militaire.nom }} {{ militaire.prenom }} - {{ militaire.unite_affectation or 'N/A' }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un militaire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Type de Visite *</label>
                            <select name="type_visite" class="form-control form-control-military" required>
                                <option value="">Sélectionner un type...</option>
                                {% for type_visite in types_visites %}
                                <option value="{{ type_visite }}">{{ type_visite }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un type de visite</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Date de la Visite *</label>
                            <input type="date" name="date_visite" class="form-control form-control-military" 
                                   value="{{ date.today() }}" required>
                            <div class="invalid-feedback">La date de visite est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Médecin Traitant</label>
                            <input type="text" name="medecin_traitant" class="form-control form-control-military"
                                   placeholder="Ex: Dr. ALAMI Hassan">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Établissement Médical</label>
                            <input type="text" name="etablissement_medical" class="form-control form-control-military"
                                   placeholder="Ex: Hôpital Militaire Mohammed V">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Résultats et Aptitude -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check"></i>
                            Résultats et Aptitude
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Résultat de la Visite</label>
                            <textarea name="resultat_visite" class="form-control form-control-military" rows="3" 
                                      placeholder="Résumé des résultats de la visite médicale..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Aptitude Physique</label>
                            <select name="aptitude_physique" class="form-control form-control-military">
                                <option value="">Sélectionner une aptitude...</option>
                                {% for aptitude in aptitudes %}
                                <option value="{{ aptitude }}">{{ aptitude }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Restrictions Médicales</label>
                            <textarea name="restrictions_medicales" class="form-control form-control-military" rows="3" 
                                      placeholder="Restrictions ou limitations médicales (si applicable)..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Date Prochaine Visite</label>
                            <input type="date" name="date_prochaine_visite" class="form-control form-control-military">
                            <small class="text-muted">Date recommandée pour la prochaine visite de contrôle</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section 3: Observations Médicales -->
            <div class="col-lg-8 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-notes-medical"></i>
                            Observations Médicales
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Observations Détaillées</label>
                            <textarea name="observations_medicales" class="form-control form-control-military" rows="6" 
                                      placeholder="Observations médicales détaillées, recommandations, traitements prescrits, etc."></textarea>
                        </div>
                        
                        <div class="alert alert-military">
                            <i class="fas fa-user-md me-2"></i>
                            <strong>Confidentialité Médicale :</strong> Ces informations sont strictement confidentielles 
                            et ne doivent être accessibles qu'au personnel médical autorisé et aux responsables RH habilités.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Validation -->
            <div class="col-lg-4 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-save"></i>
                            Validation
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success-military mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Cette visite médicale sera ajoutée au dossier 
                            médical du militaire et sera accessible pour le suivi de santé.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success-military btn-lg">
                                <i class="fas fa-save"></i> Enregistrer la Visite
                            </button>
                            <a href="{{ url_for('rh.sante_medicale') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                        
                        <hr class="my-3">
                        
                        <div class="text-center">
                            <h6 class="text-warning mb-2">Actions Rapides</h6>
                            <div class="d-grid gap-1">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military btn-sm">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.sante_medicale') }}" class="btn btn-info-military btn-sm">
                                    <i class="fas fa-heartbeat"></i> Suivi Médical
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.alert-success-military {
    border-left: 4px solid var(--success-color);
    background: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
})();

// Suggestions selon le type de visite
document.querySelector('select[name="type_visite"]').addEventListener('change', function() {
    const observationsTextarea = document.querySelector('textarea[name="observations_medicales"]');
    const suggestions = {
        'Visite d\'incorporation': 'Visite médicale d\'incorporation - Examen complet initial.',
        'Visite périodique': 'Visite médicale périodique - Contrôle de routine annuel.',
        'Visite de contrôle': 'Visite de contrôle médical - Suivi d\'un traitement ou d\'une pathologie.',
        'Visite spécialisée': 'Visite médicale spécialisée - Consultation avec un spécialiste.',
        'Visite d\'aptitude': 'Visite d\'aptitude - Évaluation de l\'aptitude pour un poste spécifique.'
    };
    
    if (suggestions[this.value] && !observationsTextarea.value) {
        observationsTextarea.value = suggestions[this.value];
    }
});

// Calcul automatique de la prochaine visite selon le type
document.querySelector('select[name="type_visite"]').addEventListener('change', function() {
    const dateVisiteInput = document.querySelector('input[name="date_visite"]');
    const prochaineVisiteInput = document.querySelector('input[name="date_prochaine_visite"]');
    
    if (dateVisiteInput.value) {
        const dateVisite = new Date(dateVisiteInput.value);
        let moisAjouter = 12; // Par défaut 1 an
        
        switch(this.value) {
            case 'Visite d\'incorporation':
                moisAjouter = 12; // 1 an
                break;
            case 'Visite périodique':
                moisAjouter = 12; // 1 an
                break;
            case 'Visite de contrôle':
                moisAjouter = 6; // 6 mois
                break;
            case 'Visite spécialisée':
                moisAjouter = 6; // 6 mois
                break;
            case 'Visite d\'aptitude':
                moisAjouter = 24; // 2 ans
                break;
        }
        
        const prochaineDate = new Date(dateVisite);
        prochaineDate.setMonth(prochaineDate.getMonth() + moisAjouter);
        
        prochaineVisiteInput.value = prochaineDate.toISOString().split('T')[0];
    }
});

// Mise à jour automatique des restrictions selon l'aptitude
document.querySelector('select[name="aptitude_physique"]').addEventListener('change', function() {
    const restrictionsTextarea = document.querySelector('textarea[name="restrictions_medicales"]');
    
    if (!restrictionsTextarea.value) {
        switch(this.value) {
            case 'Apte':
                restrictionsTextarea.value = 'Aucune restriction médicale.';
                break;
            case 'Apte avec restrictions':
                restrictionsTextarea.value = 'Aptitude avec restrictions à définir selon les résultats médicaux.';
                break;
            case 'Inapte temporaire':
                restrictionsTextarea.value = 'Inaptitude temporaire - Réévaluation nécessaire.';
                break;
            case 'Inapte définitif':
                restrictionsTextarea.value = 'Inaptitude définitive - Réorientation professionnelle à envisager.';
                break;
        }
    }
});

// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const confirmation = confirm('Êtes-vous sûr de vouloir enregistrer cette visite médicale ?');
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
