{% extends "rh/base_rh.html" %}

{% block title %}Nouvelle Formation - Gestion RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-graduation-cap"></i>
                                Nouvelle Formation
                            </h2>
                            <small class="text-muted">Enregistrement d'une nouvelle formation suivie</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.liste_formations') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Liste
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire de Création -->
    <form method="POST" class="needs-validation" novalidate>
        <div class="row">
            <!-- Section 1: Informations de Base -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-user"></i>
                            Informations de Base
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Militaire Concerné *</label>
                            <select name="militaire_id" class="form-control form-control-military" required>
                                <option value="">Sélectionner un militaire...</option>
                                {% for militaire in personnel %}
                                <option value="{{ militaire.id }}">
                                    {{ militaire.grade_actuel or '' }} {{ militaire.nom }} {{ militaire.prenom }} - {{ militaire.unite_affectation or 'N/A' }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">Veuillez sélectionner un militaire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Nom de la Formation *</label>
                            <input type="text" name="nom_formation" class="form-control form-control-military" required
                                   placeholder="Ex: Formation Leadership Militaire">
                            <div class="invalid-feedback">Le nom de la formation est obligatoire</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Organisme Formateur</label>
                            <input type="text" name="organisme_formateur" class="form-control form-control-military"
                                   placeholder="Ex: École Militaire de Meknès">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Lieu de Formation</label>
                            <input type="text" name="lieu_formation" class="form-control form-control-military"
                                   placeholder="Ex: Meknès, Rabat, En ligne">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 2: Période et Durée -->
            <div class="col-lg-6 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar"></i>
                            Période et Durée
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Début</label>
                                <input type="date" name="date_debut" class="form-control form-control-military">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Date de Fin</label>
                                <input type="date" name="date_fin" class="form-control form-control-military">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Durée (en heures)</label>
                            <div class="input-group">
                                <input type="number" name="duree_heures" class="form-control form-control-military" min="1"
                                       placeholder="Ex: 40">
                                <span class="input-group-text" style="background: var(--card-bg); border-color: var(--border-color); color: var(--text-light);">heures</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Résultat</label>
                            <select name="resultat" class="form-control form-control-military">
                                {% for resultat in resultats %}
                                <option value="{{ resultat }}" {% if resultat == 'En cours' %}selected{% endif %}>
                                    {{ resultat }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Note Obtenue</label>
                                <div class="input-group">
                                    <input type="number" name="note_obtenue" class="form-control form-control-military" 
                                           min="0" max="20" step="0.1" placeholder="Ex: 16.5">
                                    <span class="input-group-text" style="background: var(--card-bg); border-color: var(--border-color); color: var(--text-light);">/20</span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label-military">Certificat Obtenu</label>
                                <div class="form-check mt-2">
                                    <input class="form-check-input" type="checkbox" name="certificat_obtenu" id="certificat">
                                    <label class="form-check-label text-light" for="certificat">
                                        Certificat ou diplôme obtenu
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Section 3: Compétences et Observations -->
            <div class="col-lg-8 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-brain"></i>
                            Compétences et Observations
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label-military">Compétences Acquises</label>
                            <textarea name="competences_acquises" class="form-control form-control-military" rows="4" 
                                      placeholder="Décrivez les compétences et connaissances acquises lors de cette formation..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label-military">Observations</label>
                            <textarea name="observations" class="form-control form-control-military" rows="3" 
                                      placeholder="Observations complémentaires, recommandations, etc."></textarea>
                        </div>
                        
                        <div class="alert alert-military">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Conseil :</strong> Détaillez les compétences acquises pour faciliter 
                            l'évaluation des besoins futurs et la planification de carrière.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 4: Validation -->
            <div class="col-lg-4 mb-4">
                <div class="card-military">
                    <div class="card-header-military">
                        <h5 class="mb-0">
                            <i class="fas fa-save"></i>
                            Validation
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success-military mb-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> Cette formation sera ajoutée au dossier 
                            du militaire et pourra être utilisée pour l'évaluation des compétences.
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success-military btn-lg">
                                <i class="fas fa-save"></i> Enregistrer la Formation
                            </button>
                            <a href="{{ url_for('rh.liste_formations') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Annuler
                            </a>
                        </div>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <h6 class="text-warning mb-3">Actions Rapides</h6>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('rh.dashboard') }}" class="btn btn-military">
                                    <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                                </a>
                                <a href="{{ url_for('rh.liste_formations') }}" class="btn btn-info-military">
                                    <i class="fas fa-graduation-cap"></i> Liste Formations
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
.form-control-military:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 213, 79, 0.25);
}

.invalid-feedback {
    color: var(--danger-color);
    font-weight: 600;
}

.was-validated .form-control:invalid {
    border-color: var(--danger-color);
}

.was-validated .form-control:valid {
    border-color: var(--success-color);
}

.input-group-text {
    background: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-light);
}

.form-check-input:checked {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.alert-success-military {
    border-left: 4px solid var(--success-color);
    background: rgba(76, 175, 80, 0.1);
    border-radius: 8px;
}

.card-military {
    transition: all 0.3s ease;
}

.card-military:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 213, 79, 0.15);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // Validation en temps réel
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
    
})();

// Calcul automatique de la durée en heures basé sur les dates
function calculerDureeHeures() {
    const dateDebut = document.querySelector('input[name="date_debut"]').value;
    const dateFin = document.querySelector('input[name="date_fin"]').value;
    const dureeField = document.querySelector('input[name="duree_heures"]');
    
    if (dateDebut && dateFin && !dureeField.value) {
        const debut = new Date(dateDebut);
        const fin = new Date(dateFin);
        
        if (fin >= debut) {
            const diffTime = Math.abs(fin - debut);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            // Estimation: 7 heures par jour de formation
            const heuresEstimees = diffDays * 7;
            dureeField.value = heuresEstimees;
        }
    }
}

// Événements pour le calcul automatique
document.querySelector('input[name="date_debut"]').addEventListener('change', calculerDureeHeures);
document.querySelector('input[name="date_fin"]').addEventListener('change', calculerDureeHeures);

// Validation des dates
document.querySelector('input[name="date_debut"]').addEventListener('change', function() {
    const dateFinInput = document.querySelector('input[name="date_fin"]');
    dateFinInput.min = this.value;
    
    if (dateFinInput.value && dateFinInput.value < this.value) {
        dateFinInput.value = this.value;
    }
});

// Suggestions de compétences selon le type de formation
document.querySelector('input[name="nom_formation"]').addEventListener('blur', function() {
    const competencesTextarea = document.querySelector('textarea[name="competences_acquises"]');
    const formation = this.value.toLowerCase();
    
    if (!competencesTextarea.value) {
        let suggestions = '';
        
        if (formation.includes('leadership')) {
            suggestions = 'Leadership, Gestion d\'équipe, Communication, Prise de décision, Motivation des troupes';
        } else if (formation.includes('technique') || formation.includes('informatique')) {
            suggestions = 'Compétences techniques, Maîtrise des outils, Résolution de problèmes, Innovation';
        } else if (formation.includes('sécurité')) {
            suggestions = 'Sécurité, Prévention des risques, Protocoles de sécurité, Gestion de crise';
        } else if (formation.includes('gestion')) {
            suggestions = 'Gestion de projet, Planification, Organisation, Contrôle budgétaire';
        } else if (formation.includes('communication')) {
            suggestions = 'Communication orale, Communication écrite, Présentation, Relations publiques';
        }
        
        if (suggestions) {
            competencesTextarea.value = suggestions;
        }
    }
});

// Mise à jour automatique du résultat selon la note
document.querySelector('input[name="note_obtenue"]').addEventListener('input', function() {
    const note = parseFloat(this.value);
    const resultatSelect = document.querySelector('select[name="resultat"]');
    const certificatCheck = document.querySelector('input[name="certificat_obtenu"]');
    
    if (!isNaN(note)) {
        if (note >= 10) {
            resultatSelect.value = 'Réussi';
            certificatCheck.checked = true;
        } else {
            resultatSelect.value = 'Échoué';
            certificatCheck.checked = false;
        }
    }
});

// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-military');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
});

// Confirmation avant soumission
document.querySelector('form').addEventListener('submit', function(e) {
    if (this.checkValidity()) {
        const confirmation = confirm('Êtes-vous sûr de vouloir enregistrer cette formation ?');
        if (!confirmation) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
