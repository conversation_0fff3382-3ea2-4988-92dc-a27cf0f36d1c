#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour ajouter des données de test pour le personnel RH
"""

from rh_models import *
from app import app
from datetime import datetime, date
import random

def add_test_personnel():
    """Ajouter des données de test pour le personnel"""
    with app.app_context():
        try:
            # Vérifier si des données existent déjà
            if Personnel.query.count() > 0:
                print(f"✅ Personnel déjà présent: {Personnel.query.count()} enregistrements")
                return
            
            print("🔄 Ajout de données de test pour le personnel...")
            
            # Récupérer les données de référence
            armes = ReferentielArme.query.all()
            unites = ReferentielUnite.query.all()
            grades = ReferentielGrade.query.all()
            categories = ReferentielCategorie.query.all()
            genres = ReferentielGenre.query.all()
            situations_fam = ReferentielSituationFamiliale.query.all()
            groupes_sanguin = ReferentielGroupeSanguin.query.all()
            
            if not all([armes, unites, grades, categories, genres, situations_fam, groupes_sanguin]):
                print("❌ Erreur: Tables de référence manquantes")
                return
            
            # Données de test
            noms_prenoms = [
                ("ALAMI", "Ahmed", "العلمي", "أحمد"),
                ("BENALI", "Mohamed", "بن علي", "محمد"),
                ("CHAKIR", "Hassan", "شاكر", "حسن"),
                ("DRISSI", "Youssef", "الإدريسي", "يوسف"),
                ("FASSI", "Omar", "الفاسي", "عمر"),
                ("GHALI", "Khalid", "الغالي", "خالد"),
                ("HAJJI", "Abdelkader", "الحاجي", "عبد القادر"),
                ("IDRISSI", "Rachid", "الإدريسي", "رشيد"),
                ("JAMAL", "Samir", "جمال", "سمير"),
                ("KABBAJ", "Noureddine", "الكباج", "نور الدين"),
                ("LAHLOU", "Mustapha", "لحلو", "مصطفى"),
                ("MANSOURI", "Abdellah", "المنصوري", "عبد الله"),
                ("NACIRI", "Driss", "الناصري", "إدريس"),
                ("OUALI", "Brahim", "الوالي", "إبراهيم"),
                ("QADIRI", "Fouad", "القادري", "فؤاد"),
                ("RAMI", "Tarik", "رامي", "طارق"),
                ("SLAOUI", "Karim", "السلاوي", "كريم"),
                ("TAZI", "Amine", "التازي", "أمين"),
                ("WAHBI", "Aziz", "الوهبي", "عزيز"),
                ("ZAKI", "Hamid", "زكي", "حميد")
            ]
            
            lieux_naissance = [
                "Rabat", "Casablanca", "Fès", "Marrakech", "Agadir", "Tanger", 
                "Meknès", "Oujda", "Kenitra", "Tétouan", "Safi", "Mohammedia",
                "Khouribga", "Beni Mellal", "El Jadida", "Nador", "Settat"
            ]
            
            lieux_residence = [
                "Hay Riad, Rabat", "Maarif, Casablanca", "Atlas, Fès", 
                "Gueliz, Marrakech", "Talborjt, Agadir", "Malabata, Tanger",
                "Hamria, Meknès", "Lazaret, Oujda", "Saknia, Kenitra",
                "Martil, Tétouan", "Sidi Bouzid, Safi", "Oulfa, Casablanca"
            ]
            
            fonctions = [
                "Chef de Section", "Adjudant Chef", "Sergent Chef", "Caporal Chef",
                "Soldat de 1ère Classe", "Officier d'État-Major", "Commandant d'Unité",
                "Instructeur", "Mécanicien", "Chauffeur", "Secrétaire", "Comptable"
            ]
            
            # Créer 50 militaires de test
            for i in range(50):
                nom, prenom, nom_arabe, prenom_arabe = random.choice(noms_prenoms)
                
                # Générer un matricule unique
                matricule = f"M{2024}{str(i+1).zfill(4)}"
                
                # Dates aléatoires
                date_naissance = date(
                    random.randint(1970, 2000),
                    random.randint(1, 12),
                    random.randint(1, 28)
                )
                
                date_engagement = date(
                    random.randint(2000, 2024),
                    random.randint(1, 12),
                    random.randint(1, 28)
                )
                
                date_prise_fonction = date(
                    random.randint(2020, 2024),
                    random.randint(1, 12),
                    random.randint(1, 28)
                )
                
                # CIN unique
                cin = f"AB{random.randint(100000, 999999)}"
                
                # Créer le personnel
                personnel = Personnel(
                    matricule=matricule,
                    nom=nom,
                    prenom=prenom,
                    nom_arabe=nom_arabe,
                    prenom_arabe=prenom_arabe,
                    date_naissance=date_naissance,
                    lieu_naissance=random.choice(lieux_naissance),
                    sexe_id=random.choice(genres).id_genre,
                    numero_cin=cin,
                    date_expiration_cin=date(2030, 12, 31),
                    categorie_id=random.choice(categories).id_categorie,
                    arme_id=random.choice(armes).id_arme,
                    specialite_id=None,  # Peut être null
                    unite_id=random.choice(unites).id_unite,
                    grade_actuel_id=random.choice(grades).id_grade,
                    fonction=random.choice(fonctions),
                    date_engagement=date_engagement,
                    date_prise_fonction=date_prise_fonction,
                    situation_fam_id=random.choice(situations_fam).id_sitfam,
                    nombre_enfants=random.randint(0, 4),
                    gsm=f"06{random.randint(10000000, 99999999)}",
                    telephone_domicile=f"05{random.randint(10000000, 99999999)}",
                    lieu_residence=random.choice(lieux_residence),
                    groupe_sanguin_id=random.choice(groupes_sanguin).id_groupe,
                    compte_bancaire=f"001{random.randint(100000000000, 999999999999)}",
                    gsm_urgence=f"06{random.randint(10000000, 99999999)}",
                    degre_parente_id=1  # Supposons que 1 = "Père"
                )
                
                db.session.add(personnel)
            
            # Commit des données
            db.session.commit()
            
            # Vérifier le résultat
            count = Personnel.query.count()
            print(f"✅ {count} militaires ajoutés avec succès")
            
            # Afficher quelques statistiques
            print("\n📊 Statistiques:")
            print(f"   - Total personnel: {count}")
            print(f"   - Officiers: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Officier').count()}")
            print(f"   - Sous-officiers: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Sous-officier').count()}")
            print(f"   - Hommes du rang: {Personnel.query.join(ReferentielCategorie).filter(ReferentielCategorie.libelle == 'Homme du rang').count()}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de l'ajout des données de test: {e}")

if __name__ == "__main__":
    add_test_personnel()
