{% extends "base.html" %}

{% block title %}Ajouter Conjoint - RH{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-heart"></i>
                        Ajouter un conjoint pour {{ personnel.prenom }} {{ personnel.nom }}
                    </h3>
                </div>
                
                <form method="POST" action="{{ url_for('rh.ajouter_conjoint', matricule=personnel.matricule) }}" class="needs-validation" novalidate>
                    <div class="card-body">
                        
                        <!-- Section Informations personnelles -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-user"></i> Informations personnelles
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom" class="required">Nom</label>
                                    <input type="text" class="form-control" id="nom" name="nom" required>
                                    <div class="invalid-feedback">Le nom est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom" class="required">Prénom</label>
                                    <input type="text" class="form-control" id="prenom" name="prenom" required>
                                    <div class="invalid-feedback">Le prénom est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_arabe" class="required">الاسم العائلي</label>
                                    <input type="text" class="form-control" id="nom_arabe" name="nom_arabe" required>
                                    <div class="invalid-feedback">الاسم العائلي مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_arabe" class="required">الاسم الشخصي</label>
                                    <input type="text" class="form-control" id="prenom_arabe" name="prenom_arabe" required>
                                    <div class="invalid-feedback">الاسم الشخصي مطلوب</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date_naissance" class="required">Date de naissance</label>
                                    <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                                    <div class="invalid-feedback">La date de naissance est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="lieu_naissance" class="required">Lieu de naissance</label>
                                    <input type="text" class="form-control" id="lieu_naissance" name="lieu_naissance" required>
                                    <div class="invalid-feedback">Le lieu de naissance est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="lieu_naissance_arabe" class="required">مكان الازدياد</label>
                                    <input type="text" class="form-control" id="lieu_naissance_arabe" name="lieu_naissance_arabe" required>
                                    <div class="invalid-feedback">مكان الازدياد مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="numero_cin" class="required">Numéro CIN</label>
                                    <input type="text" class="form-control" id="numero_cin" name="numero_cin" 
                                           pattern="[A-Z][0-9]{6}" maxlength="7" required>
                                    <div class="invalid-feedback">Format: 1 lettre + 6 chiffres (ex: A123456)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Mariage -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-rings-wedding"></i> Informations de mariage
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="date_mariage" class="required">Date de mariage</label>
                                    <input type="date" class="form-control" id="date_mariage" name="date_mariage" required>
                                    <div class="invalid-feedback">La date de mariage est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="lieu_mariage" class="required">Lieu de mariage</label>
                                    <input type="text" class="form-control" id="lieu_mariage" name="lieu_mariage" required>
                                    <div class="invalid-feedback">Le lieu de mariage est obligatoire</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Profession -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-briefcase"></i> Profession
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="profession" class="required">Profession</label>
                                    <input type="text" class="form-control" id="profession" name="profession" required>
                                    <div class="invalid-feedback">La profession est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="profession_arabe" class="required">المهنة</label>
                                    <input type="text" class="form-control" id="profession_arabe" name="profession_arabe" required>
                                    <div class="invalid-feedback">المهنة مطلوبة</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Contact -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-phone"></i> Contact et adresse
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="gsm" class="required">GSM</label>
                                    <input type="tel" class="form-control" id="gsm" name="gsm" 
                                           pattern="0[67][0-9]{8}" required>
                                    <div class="invalid-feedback">Format: 06XXXXXXXX ou 07XXXXXXXX</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adresse" class="required">Adresse</label>
                                    <textarea class="form-control" id="adresse" name="adresse" rows="2" required></textarea>
                                    <div class="invalid-feedback">L'adresse est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="adresse_arabe" class="required">العنوان</label>
                                    <textarea class="form-control" id="adresse_arabe" name="adresse_arabe" rows="2" required></textarea>
                                    <div class="invalid-feedback">العنوان مطلوب</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section Parents -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="fas fa-users"></i> Informations des parents
                                </h5>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_pere" class="required">Nom du père</label>
                                    <input type="text" class="form-control" id="nom_pere" name="nom_pere" required>
                                    <div class="invalid-feedback">Le nom du père est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_pere" class="required">Prénom du père</label>
                                    <input type="text" class="form-control" id="prenom_pere" name="prenom_pere" required>
                                    <div class="invalid-feedback">Le prénom du père est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_arabe_pere" class="required">اسم الأب</label>
                                    <input type="text" class="form-control" id="nom_arabe_pere" name="nom_arabe_pere" required>
                                    <div class="invalid-feedback">اسم الأب مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_arabe_pere" class="required">نسب الأب</label>
                                    <input type="text" class="form-control" id="prenom_arabe_pere" name="prenom_arabe_pere" required>
                                    <div class="invalid-feedback">نسب الأب مطلوب</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_mere" class="required">Nom de la mère</label>
                                    <input type="text" class="form-control" id="nom_mere" name="nom_mere" required>
                                    <div class="invalid-feedback">Le nom de la mère est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_mere" class="required">Prénom de la mère</label>
                                    <input type="text" class="form-control" id="prenom_mere" name="prenom_mere" required>
                                    <div class="invalid-feedback">Le prénom de la mère est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="nom_arabe_mere" class="required">اسم الأم</label>
                                    <input type="text" class="form-control" id="nom_arabe_mere" name="nom_arabe_mere" required>
                                    <div class="invalid-feedback">اسم الأم مطلوب</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="prenom_arabe_mere" class="required">نسب الأم</label>
                                    <input type="text" class="form-control" id="prenom_arabe_mere" name="prenom_arabe_mere" required>
                                    <div class="invalid-feedback">نسب الأم مطلوب</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="profession_pere" class="required">Profession du père</label>
                                    <input type="text" class="form-control" id="profession_pere" name="profession_pere" required>
                                    <div class="invalid-feedback">La profession du père est obligatoire</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="profession_mere" class="required">Profession de la mère</label>
                                    <input type="text" class="form-control" id="profession_mere" name="profession_mere" required>
                                    <div class="invalid-feedback">La profession de la mère est obligatoire</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('rh.fiche_personnel', matricule=personnel.matricule) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.required::after {
    content: " *";
    color: red;
}
</style>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
