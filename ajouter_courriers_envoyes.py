#!/usr/bin/env python3
"""
Script pour ajouter des courriers envoyés supplémentaires
Format similaire aux données demandées
"""

import requests
import json
from datetime import datetime, date

BASE_URL = "http://127.0.0.1:3000"

def ajouter_courriers_envoyes_supplementaires():
    """Ajouter des courriers envoyés supplémentaires"""
    print("📤 Ajout de courriers envoyés supplémentaires...")
    
    # Données supplémentaires pour courriers envoyés
    courriers = [
        {
            "id": "1467",
            "numero_ecrit": "14567",
            "division_emettrice": "informatique",
            "date_depart": "2024-01-24",
            "nature": "message",
            "objet": "Mise à jour système - Notification",
            "destinataire": "7° Bureau",
            "observations": "Mise à jour programmée pour ce weekend"
        },
        {
            "id": "1468",
            "numero_ecrit": "14568",
            "division_emettrice": "planification",
            "date_depart": "2024-01-25",
            "nature": "nds",
            "objet": "Planification des activités - Février 2024",
            "destinataire": "8° Bureau",
            "observations": "Planning détaillé en pièce jointe"
        },
        {
            "id": "1469",
            "numero_ecrit": "14569",
            "division_emettrice": "asa",
            "date_depart": "2024-01-26",
            "nature": "decision",
            "objet": "Autorisation spéciale - Projet pilote",
            "destinataire": "9° Bureau",
            "observations": "Autorisation accordée pour 6 mois"
        },
        {
            "id": "1470",
            "numero_ecrit": "14570",
            "division_emettrice": "technique",
            "date_depart": "2024-01-27",
            "nature": "message",
            "objet": "Support technique - Assistance demandée",
            "destinataire": "10° Bureau",
            "observations": "Équipe technique disponible lundi"
        },
        {
            "id": "1471",
            "numero_ecrit": "14571",
            "division_emettrice": "courrier",
            "date_depart": "2024-01-28",
            "nature": "note_royale",
            "objet": "Procédure de traitement - Nouvelle directive",
            "destinataire": "11° Bureau",
            "observations": "Application immédiate requise"
        }
    ]
    
    for courrier in courriers:
        try:
            response = requests.post(
                f"{BASE_URL}/api/courriers/envoyes",
                headers={"Content-Type": "application/json"},
                data=json.dumps(courrier)
            )
            
            if response.status_code == 201:
                print(f"✅ Ajouté: ID {courrier['id']} - N° {courrier['numero_ecrit']} - {courrier['objet']}")
            else:
                print(f"❌ Erreur pour {courrier['id']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Erreur pour {courrier['id']}: {e}")

def verifier_courriers_envoyes():
    """Vérifier tous les courriers envoyés"""
    print("\n🔍 Vérification de tous les courriers envoyés...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/courriers/envoyes")
        if response.status_code == 200:
            courriers = response.json()
            print(f"📤 Total courriers envoyés: {len(courriers)}")
            print("\nListe complète:")
            for i, courrier in enumerate(courriers, 1):
                print(f"   {i}. ID {courrier['id']} - N° {courrier['numero_ecrit']} - {courrier['division_emettrice']}")
                print(f"      Objet: {courrier['objet']}")
                print(f"      Destinataire: {courrier['destinataire']}")
                print()
        else:
            print(f"❌ Erreur API: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("➕ AJOUT DE COURRIERS ENVOYÉS SUPPLÉMENTAIRES")
    print("=" * 60)
    
    # Vérifier que l'application fonctionne
    try:
        response = requests.get(BASE_URL)
        if response.status_code != 200:
            print("❌ Application non accessible")
            return
    except:
        print("❌ Application non accessible")
        return
    
    print("✅ Application accessible")
    
    # Ajouter les courriers envoyés
    ajouter_courriers_envoyes_supplementaires()
    
    # Vérifier le résultat
    verifier_courriers_envoyes()
    
    print("=" * 60)
    print("✅ AJOUT TERMINÉ")
    print("=" * 60)
    
    print("\n🌐 Vérifiez dans l'interface web:")
    print(f"   📤 Courriers envoyés: {BASE_URL}/courrier/envoyes")
    
    print("\n📋 NOUVEAUX COURRIERS AJOUTÉS:")
    print("   • ID 1467 - N° 14567 - Informatique - Mise à jour système")
    print("   • ID 1468 - N° 14568 - Planification - Planification des activités")
    print("   • ID 1469 - N° 14569 - ASA - Autorisation spéciale")
    print("   • ID 1470 - N° 14570 - Technique - Support technique")
    print("   • ID 1471 - N° 14571 - Courrier - Procédure de traitement")

if __name__ == "__main__":
    main()
