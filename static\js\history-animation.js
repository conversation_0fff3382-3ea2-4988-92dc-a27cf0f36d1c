document.addEventListener('DOMContentLoaded', function() {
    // Ne pas toucher à l'animation CSS, elle est déjà visible et fonctionne correctement
    console.log('Animation CSS active');
    
    // Ajouter une classe pour indiquer que l'animation est chargée
    const container = document.querySelector('.text-center.py-5');
    if (container) {
        container.classList.add('animation-loaded');
    }
});
