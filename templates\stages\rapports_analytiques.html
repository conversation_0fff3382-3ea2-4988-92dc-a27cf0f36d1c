{% extends "stages/base_stages.html" %}

{% block title %}Rapports Analytiques{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Rapports Analytiques</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('export_rapports_excel') }}" class="btn btn-sm btn-outline-success me-2"><i class="fas fa-file-excel me-1"></i> Export Excel</a>
            <a href="{{ url_for('export_rapports_pdf') }}" class="btn btn-sm btn-outline-danger"><i class="fas fa-file-pdf me-1"></i> Export PDF</a>
        </div>
    </div>
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card text-bg-primary">
                <div class="card-body">
                    <h5 class="card-title">Nombre total de stagiaires</h5>
                    <p class="display-5">{{ nb_stagiaires }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-bg-info">
                <div class="card-body">
                    <h5 class="card-title">Nombre de promotions</h5>
                    <p class="display-5">{{ nb_promotions }}</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card text-bg-success">
                <div class="card-body">
                    <h5 class="card-title">Nombre de stages</h5>
                    <p class="display-5">{{ nb_stages }}</p>
                </div>
            </div>
        </div>
    </div>
    <div class="card mt-4">
        <div class="card-header">Répartition des stagiaires par promotion</div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead><tr><th>Promotion</th><th>Année</th><th>Filière</th><th>Nombre de stagiaires</th></tr></thead>
                <tbody>
                {% for promo in stats_promos %}
                    <tr>
                        <td>{{ promo.nom }}</td>
                        <td>{{ promo.annee }}</td>
                        <td>{{ promo.filiere or 'N/A' }}</td>
                        <td>{{ promo.nb_stagiaires }}</td>
                    </tr>
                {% else %}
                    <tr><td colspan="4" class="text-center">Aucune donnée</td></tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 