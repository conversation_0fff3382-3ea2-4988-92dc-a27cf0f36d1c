{% extends 'base.html' %}
{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">Gestion du Courrier</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header modal-card-header text-white" style="background: #6B8E23;">Courriers Arrivés</div>
                <div class="card-body">
                    <a href="{{ url_for('ajouter_courrier_arrive') }}" class="btn btn-success mb-2">Ajouter un courrier arrivé</a>
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date arrivée</th>
                                <th>Objet</th>
                                <th>Division(s) action</th>
                                <th>Division(s) info</th>
                                <th>Urgence</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for c in courriers_arrives %}
                            <tr>
                                <td>{{ c.id_courrier }}</td>
                                <td>{{ c.date_arrivee }}</td>
                                <td>{{ c.objet }}</td>
                                <td>{{ ', '.join(c.divisions_action) }}</td>
                                <td>{{ ', '.join(c.divisions_info) }}</td>
                                <td>{{ c.urgence }}</td>
                                <td>
                                    <a href="#" class="btn btn-info btn-sm">Voir</a>
                                    <a href="#" class="btn btn-secondary btn-sm">Scanner</a>
                                    <a href="#" class="btn btn-outline-primary btn-sm">Imprimer</a>
                                </td>
                            </tr>
                        {% else %}
                            <tr><td colspan="7" class="text-center">Aucun courrier arrivé</td></tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header modal-card-header text-white" style="background: #6B8E23;">Courriers Envoyés</div>
                <div class="card-body">
                    <a href="{{ url_for('ajouter_courrier_envoye') }}" class="btn btn-success mb-2">Ajouter un courrier envoyé</a>
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date envoi</th>
                                <th>Objet</th>
                                <th>Division émettrice</th>
                                <th>Destinataire externe</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                        {% for c in courriers_envoyes %}
                            <tr>
                                <td>{{ c.id_courrier }}</td>
                                <td>{{ c.date_envoi }}</td>
                                <td>{{ c.objet }}</td>
                                <td>{{ c.division_emettrice }}</td>
                                <td>{{ c.destinataire_externe }}</td>
                                <td>
                                    <a href="#" class="btn btn-info btn-sm">Voir</a>
                                    <a href="#" class="btn btn-secondary btn-sm">Scanner</a>
                                    <a href="#" class="btn btn-outline-primary btn-sm">Imprimer</a>
                                </td>
                            </tr>
                        {% else %}
                            <tr><td colspan="6" class="text-center">Aucun courrier envoyé</td></tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Désactiver le pattern de camouflage pour les en-têtes des modals */
.modal-card-header::before {
    display: none !important;
}
</style>
{% endblock %}
