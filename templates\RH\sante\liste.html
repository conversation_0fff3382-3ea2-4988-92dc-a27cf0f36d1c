{% extends "rh/base_rh.html" %}

{% block title %}Suivi Médical et Santé - RH{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-heartbeat"></i>
                                Suivi Médical et Santé
                            </h2>
                            <small class="text-muted">Gestion des visites médicales, aptitudes et suivi de santé</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.nouvelle_visite_medicale') }}" class="btn btn-success-military">
                                <i class="fas fa-plus"></i> Nouvelle Visite
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert<PERSON> Médic<PERSON> -->
    {% if visites_dues > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-warning-military">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Attention :</strong> {{ visites_dues }} visite(s) médicale(s) due(s) dans les 30 prochains jours.
                <a href="#" class="btn btn-warning-military btn-sm ms-2">
                    <i class="fas fa-eye"></i> Voir les détails
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filtres et Recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres et Recherche
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label-military">Recherche</label>
                            <input type="text" name="search" class="form-control form-control-military" 
                                   placeholder="Nom, médecin, établissement..." value="{{ search }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label-military">Type de Visite</label>
                            <select name="type" class="form-control form-control-military">
                                <option value="">Tous les types</option>
                                {% for type_visite in types_visites %}
                                <option value="{{ type_visite }}" {% if type_visite == type_filter %}selected{% endif %}>
                                    {{ type_visite }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label-military">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-military">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-heartbeat stat-icon"></i>
                <div class="stat-number">{{ visites.total }}</div>
                <div class="stat-label">Total Visites</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-calendar-check stat-icon"></i>
                <div class="stat-number">{{ visites_dues }}</div>
                <div class="stat-label">Visites Dues</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-check-circle stat-icon"></i>
                <div class="stat-number">{{ visites.items|selectattr('aptitude_physique', 'equalto', 'Apte')|list|length }}</div>
                <div class="stat-label">Aptes</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-exclamation-circle stat-icon"></i>
                <div class="stat-number">{{ visites.items|selectattr('aptitude_physique', 'equalto', 'Apte avec restrictions')|list|length }}</div>
                <div class="stat-label">Avec Restrictions</div>
            </div>
        </div>
    </div>

    <!-- Liste des Visites Médicales -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                Visites Médicales
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group">
                                <button class="btn btn-info-military btn-sm" onclick="exportToExcel()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button class="btn btn-info-military btn-sm" onclick="exportToPDF()">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </button>
                                <button class="btn btn-warning-military btn-sm" onclick="printList()">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if visites.items %}
                    <div class="table-responsive">
                        <table class="table table-military mb-0">
                            <thead>
                                <tr>
                                    <th>Militaire</th>
                                    <th>Type de Visite</th>
                                    <th>Date Visite</th>
                                    <th>Médecin</th>
                                    <th>Établissement</th>
                                    <th>Aptitude</th>
                                    <th>Prochaine Visite</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for visite in visites.items %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ visite.militaire.nom }} {{ visite.militaire.prenom }}</strong>
                                            <br><small class="text-muted">{{ visite.militaire.grade_actuel or 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-military">{{ visite.type_visite or 'N/A' }}</span>
                                    </td>
                                    <td>
                                        {% if visite.date_visite %}
                                        {{ visite.date_visite.strftime('%d/%m/%Y') }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ visite.medecin_traitant or 'N/A' }}</td>
                                    <td>{{ visite.etablissement_medical or 'N/A' }}</td>
                                    <td>
                                        {% if visite.aptitude_physique == 'Apte' %}
                                        <span class="badge badge-success-military">
                                            <i class="fas fa-check"></i> {{ visite.aptitude_physique }}
                                        </span>
                                        {% elif visite.aptitude_physique == 'Apte avec restrictions' %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-exclamation-triangle"></i> Avec restrictions
                                        </span>
                                        {% elif visite.aptitude_physique and 'Inapte' in visite.aptitude_physique %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-times"></i> {{ visite.aptitude_physique }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-secondary">{{ visite.aptitude_physique or 'N/A' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if visite.date_prochaine_visite %}
                                        {% set jours_restants = (visite.date_prochaine_visite - date.today()).days %}
                                        {% if jours_restants <= 0 %}
                                        <span class="badge badge-danger-military">
                                            <i class="fas fa-exclamation"></i> Échue
                                        </span>
                                        {% elif jours_restants <= 30 %}
                                        <span class="badge badge-warning-military">
                                            <i class="fas fa-clock"></i> {{ jours_restants }} jour(s)
                                        </span>
                                        {% else %}
                                        <span class="badge badge-info-military">
                                            {{ visite.date_prochaine_visite.strftime('%d/%m/%Y') }}
                                        </span>
                                        {% endif %}
                                        {% else %}
                                        <span class="text-muted">Non définie</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-info-military btn-sm" 
                                                    onclick="voirVisite({{ visite.id }})" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-warning-military btn-sm" 
                                                    onclick="modifierVisite({{ visite.id }})" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-success-military btn-sm" 
                                                    onclick="planifierSuivi({{ visite.id }})" title="Planifier suivi">
                                                <i class="fas fa-calendar-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune visite médicale trouvée</h5>
                        <p class="text-muted">Aucune visite ne correspond aux critères de recherche.</p>
                        <a href="{{ url_for('rh.nouvelle_visite_medicale') }}" class="btn btn-success-military">
                            <i class="fas fa-plus"></i> Ajouter une Visite
                        </a>
                    </div>
                    {% endif %}
                </div>
                
                <!-- Pagination -->
                {% if visites.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="Navigation des pages">
                        <ul class="pagination justify-content-center mb-0">
                            {% if visites.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.sante_medicale', page=visites.prev_num, search=search, type=type_filter) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in visites.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != visites.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('rh.sante_medicale', page=page_num, search=search, type=type_filter) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if visites.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('rh.sante_medicale', page=visites.next_num, search=search, type=type_filter) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function voirVisite(id) {
    alert('Détails de la visite médicale #' + id + ' - Fonctionnalité à développer');
}

function modifierVisite(id) {
    window.location.href = `/rh/sante/visites/${id}/modifier`;
}

function planifierSuivi(id) {
    alert('Planification du suivi médical #' + id + ' - Fonctionnalité à développer');
}

function exportToExcel() {
    alert('Export Excel - Fonctionnalité à développer');
}

function exportToPDF() {
    alert('Export PDF - Fonctionnalité à développer');
}

function printList() {
    window.print();
}

// Animation d'entrée pour les lignes du tableau
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
});
</script>
{% endblock %}
