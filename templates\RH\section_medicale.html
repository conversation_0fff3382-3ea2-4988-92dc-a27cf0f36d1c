{% extends "rh/base_rh.html" %}

{% block title %}Section Médicale - {{ militaire.nom_complet }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-0">
                                <i class="fas fa-heartbeat"></i>
                                Section Médicale
                            </h2>
                            <small style="color: var(--text-light);">
                                Militaire : {{ militaire.nom_complet }} ({{ militaire.matricule }})
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('rh.fiche_personnel', matricule=militaire.matricule) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour à la Fiche
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques Médicales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-heartbeat stat-icon text-{{ 'success' if situation_medicale and situation_medicale.aptitude == 'Apte' else 'danger' }}"></i>
                <div class="stat-number">{{ situation_medicale.aptitude if situation_medicale else 'N/A' }}</div>
                <div class="stat-label">Aptitude</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-hospital stat-icon"></i>
                <div class="stat-number">{{ hospitalisations|length }}</div>
                <div class="stat-label">Hospitalisations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-syringe stat-icon"></i>
                <div class="stat-number">{{ vaccinations|length }}</div>
                <div class="stat-label">Vaccinations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <i class="fas fa-bed stat-icon"></i>
                <div class="stat-number">{{ ptcs|length }}</div>
                <div class="stat-label">PTC</div>
            </div>
        </div>
    </div>

    <!-- Onglets Section Médicale -->
    <div class="row">
        <div class="col-12">
            <div class="card-military">
                <div class="card-header-military">
                    <ul class="nav nav-tabs card-header-tabs" id="medicalTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="situation-tab" data-bs-toggle="tab" data-bs-target="#situation" type="button" role="tab">
                                <i class="fas fa-heartbeat"></i> Situation Générale
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="hospitalisations-tab" data-bs-toggle="tab" data-bs-target="#hospitalisations" type="button" role="tab">
                                <i class="fas fa-hospital"></i> Hospitalisations
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="vaccinations-tab" data-bs-toggle="tab" data-bs-target="#vaccinations" type="button" role="tab">
                                <i class="fas fa-syringe"></i> Vaccinations
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="ptcs-tab" data-bs-toggle="tab" data-bs-target="#ptcs" type="button" role="tab">
                                <i class="fas fa-bed"></i> PTC
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="medicalTabsContent">
                        <!-- Onglet Situation Générale -->
                        <div class="tab-pane fade show active" id="situation" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-heartbeat me-2"></i>Situation Médicale Générale</h5>
                                        <a href="{{ url_for('rh.gestion_situation_medicale', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                            <i class="fas fa-{{ 'edit' if situation_medicale else 'plus' }}"></i> 
                                            {{ 'Modifier' if situation_medicale else 'Créer' }}
                                        </a>
                                    </div>
                                    
                                    {% if situation_medicale %}
                                    <div class="card border-{{ 'success' if situation_medicale.aptitude == 'Apte' else 'danger' }}">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h6><strong>Aptitude au Service :</strong></h6>
                                                    <span class="badge bg-{{ 'success' if situation_medicale.aptitude == 'Apte' else 'danger' }} fs-6">
                                                        <i class="fas fa-{{ 'check' if situation_medicale.aptitude == 'Apte' else 'times' }}"></i>
                                                        {{ situation_medicale.aptitude }}
                                                    </span>
                                                </div>
                                                {% if situation_medicale.date_derniere_visite %}
                                                <div class="col-md-6">
                                                    <h6><strong>Dernière Visite Médicale :</strong></h6>
                                                    <p>{{ situation_medicale.date_derniere_visite.strftime('%d/%m/%Y') }}</p>
                                                </div>
                                                {% endif %}
                                            </div>
                                            {% if situation_medicale.observations_generales %}
                                            <div class="mt-3">
                                                <h6><strong>Observations Générales :</strong></h6>
                                                <p class="text-muted">{{ situation_medicale.observations_generales }}</p>
                                            </div>
                                            {% endif %}
                                            <div class="mt-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-clock"></i> 
                                                    Dernière mise à jour : {{ situation_medicale.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <div class="text-center py-5">
                                        <i class="fas fa-heartbeat fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Aucune situation médicale enregistrée</h5>
                                        <p class="text-muted">Cliquez sur "Créer" pour ajouter les informations médicales.</p>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-lg-4">
                                    <h6><i class="fas fa-info-circle me-2"></i>Informations Personnelles</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <table class="table table-sm table-borderless">
                                                <tr><td><strong>Âge :</strong></td><td>{{ militaire.age }} ans</td></tr>
                                                <tr><td><strong>Groupe sanguin :</strong></td><td><span class="badge bg-danger">{{ militaire.groupe_sanguin.libelle if militaire.groupe_sanguin else 'N/A' }}</span></td></tr>
                                                <tr><td><strong>Taille :</strong></td><td>{{ militaire.taille_cm }} cm</td></tr>
                                                <tr><td><strong>Genre :</strong></td><td>{{ militaire.genre.libelle if militaire.genre else 'N/A' }}</td></tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Onglet Hospitalisations -->
                        <div class="tab-pane fade" id="hospitalisations" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-hospital me-2"></i>Hospitalisations ({{ hospitalisations|length }})</h5>
                                <a href="{{ url_for('rh.nouvelle_hospitalisation', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-plus"></i> Nouvelle Hospitalisation
                                </a>
                            </div>
                            
                            {% if hospitalisations %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Établissement</th>
                                            <th>Date Entrée</th>
                                            <th>Date Sortie</th>
                                            <th>Durée</th>
                                            <th>Motif</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for hosp in hospitalisations %}
                                        <tr>
                                            <td><strong>{{ hosp.etablissement }}</strong></td>
                                            <td>{{ hosp.date_entree.strftime('%d/%m/%Y') }}</td>
                                            <td>
                                                {% if hosp.date_sortie %}
                                                {{ hosp.date_sortie.strftime('%d/%m/%Y') }}
                                                {% else %}
                                                <span class="text-muted">En cours</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ hosp.duree_jours }} jour(s)</span>
                                            </td>
                                            <td>{{ hosp.motif[:50] }}{% if hosp.motif|length > 50 %}...{% endif %}</td>
                                            <td>
                                                {% if hosp.en_cours %}
                                                <span class="badge bg-warning">En cours</span>
                                                {% else %}
                                                <span class="badge bg-success">Terminée</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-info" onclick="voirDetails({{ hosp.id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-hospital fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucune hospitalisation enregistrée</h5>
                                <p class="text-muted">Cliquez sur "Nouvelle Hospitalisation" pour ajouter un séjour hospitalier.</p>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Onglet Vaccinations -->
                        <div class="tab-pane fade" id="vaccinations" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-syringe me-2"></i>Vaccinations ({{ vaccinations|length }})</h5>
                                <a href="{{ url_for('rh.nouvelle_vaccination', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-plus"></i> Nouvelle Vaccination
                                </a>
                            </div>
                            
                            {% if vaccinations %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Vaccin</th>
                                            <th>Date Vaccination</th>
                                            <th>Lieu</th>
                                            <th>Médecin</th>
                                            <th>Rappel Prévu</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for vacc in vaccinations %}
                                        <tr>
                                            <td><strong>{{ vacc.nom_vaccin }}</strong></td>
                                            <td>{{ vacc.date_vaccination.strftime('%d/%m/%Y') }}</td>
                                            <td>{{ vacc.lieu_vaccination or 'N/A' }}</td>
                                            <td>{{ vacc.medecin_vaccinateur or 'N/A' }}</td>
                                            <td>
                                                {% if vacc.rappel_prevu %}
                                                {% set jours_rappel = (vacc.rappel_prevu - date.today()).days %}
                                                {{ vacc.rappel_prevu.strftime('%d/%m/%Y') }}
                                                {% if jours_rappel <= 30 and jours_rappel >= 0 %}
                                                <br><small class="text-warning">Dans {{ jours_rappel }} jours</small>
                                                {% elif jours_rappel < 0 %}
                                                <br><small class="text-danger">En retard</small>
                                                {% endif %}
                                                {% else %}
                                                <span class="text-muted">Aucun</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if vacc.rappel_prevu %}
                                                {% set jours_rappel = (vacc.rappel_prevu - date.today()).days %}
                                                {% if jours_rappel < 0 %}
                                                <span class="badge bg-danger">Rappel en retard</span>
                                                {% elif jours_rappel <= 30 %}
                                                <span class="badge bg-warning">Rappel proche</span>
                                                {% else %}
                                                <span class="badge bg-success">À jour</span>
                                                {% endif %}
                                                {% else %}
                                                <span class="badge bg-info">Unique</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-syringe fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucune vaccination enregistrée</h5>
                                <p class="text-muted">Cliquez sur "Nouvelle Vaccination" pour ajouter un vaccin.</p>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Onglet PTC -->
                        <div class="tab-pane fade" id="ptcs" role="tabpanel">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5><i class="fas fa-bed me-2"></i>Permissions Temporaires de Congé ({{ ptcs|length }})</h5>
                                <a href="{{ url_for('rh.nouveau_ptc', matricule=militaire.matricule) }}" class="btn btn-success-military">
                                    <i class="fas fa-plus"></i> Nouveau PTC
                                </a>
                            </div>
                            
                            {% if ptcs %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Objet</th>
                                            <th>Date Début</th>
                                            <th>Date Fin</th>
                                            <th>Durée</th>
                                            <th>Lieu</th>
                                            <th>N° Décision</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for ptc in ptcs %}
                                        <tr>
                                            <td><strong>{{ ptc.objet[:50] }}{% if ptc.objet|length > 50 %}...{% endif %}</strong></td>
                                            <td>{{ ptc.date_debut.strftime('%d/%m/%Y') }}</td>
                                            <td>{{ ptc.date_fin.strftime('%d/%m/%Y') }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ ptc.duree_jours }} jour(s)</span>
                                            </td>
                                            <td>{{ ptc.lieu_ptc or 'N/A' }}</td>
                                            <td>{{ ptc.numero_decision or 'N/A' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-bed fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucun PTC enregistré</h5>
                                <p class="text-muted">Cliquez sur "Nouveau PTC" pour ajouter une permission temporaire de congé.</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.nav-tabs .nav-link {
    color: var(--text-light);
    border: none;
    background: transparent;
    margin-right: 10px;
    border-radius: 25px;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(212, 175, 55, 0.2);
    color: var(--accent-color);
}

.nav-tabs .nav-link.active {
    background: var(--accent-color);
    color: var(--primary-color);
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(212, 175, 55, 0.1);
}

.badge {
    font-size: 0.75em;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function voirDetails(hospitalisationId) {
    alert('Voir détails hospitalisation ID: ' + hospitalisationId + ' - Fonctionnalité en développement');
}

// Animation des onglets
document.addEventListener('DOMContentLoaded', function() {
    // Animation des statistiques
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        if (!isNaN(stat.textContent)) {
            const finalValue = parseInt(stat.textContent);
            let currentValue = 0;
            const increment = finalValue / 20;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentValue);
                }
            }, 50);
        }
    });
    
    // Animation des onglets
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const targetPane = document.querySelector(e.target.getAttribute('data-bs-target'));
            targetPane.style.opacity = '0';
            targetPane.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                targetPane.style.transition = 'all 0.3s ease';
                targetPane.style.opacity = '1';
                targetPane.style.transform = 'translateY(0)';
            }, 50);
        });
    });
});
</script>
{% endblock %}
