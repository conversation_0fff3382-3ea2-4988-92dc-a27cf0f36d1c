#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Routes de gestion familiale pour le module RH
Implémentation des routes pour la gestion des conjoints et enfants
"""

from flask import request, redirect, url_for, flash, jsonify
from datetime import datetime
from rh_models import *
from rh_blueprint import rh_bp

# ============================================================================
# ROUTES DE GESTION FAMILIALE
# ============================================================================

@rh_bp.route('/api/modifier_conjoint/<matricule>', methods=['PUT'])
def api_modifier_conjoint(matricule):
    """API pour modifier les informations du conjoint"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        if not personnel.conjoint:
            return jsonify({
                'success': False,
                'error': 'Aucun conjoint trouvé pour ce personnel'
            }), 404
        
        data = request.get_json()
        conjoint = personnel.conjoint
        
        # Mise à jour des champs modifiables
        if 'nom' in data:
            conjoint.nom = data['nom']
        if 'prenom' in data:
            conjoint.prenom = data['prenom']
        if 'nom_arabe' in data:
            conjoint.nom_arabe = data['nom_arabe']
        if 'prenom_arabe' in data:
            conjoint.prenom_arabe = data['prenom_arabe']
        if 'adresse' in data:
            conjoint.adresse = data['adresse']
        if 'adresse_arabe' in data:
            conjoint.adresse_arabe = data['adresse_arabe']
        if 'profession' in data:
            conjoint.profession = data['profession']
        if 'profession_arabe' in data:
            conjoint.profession_arabe = data['profession_arabe']
        if 'numero_cin' in data:
            conjoint.numero_cin = data['numero_cin']
        if 'gsm' in data:
            conjoint.gsm = data['gsm']
        
        # Mise à jour des dates si fournies
        if 'date_naissance' in data:
            conjoint.date_naissance = datetime.strptime(data['date_naissance'], '%Y-%m-%d').date()
        if 'date_mariage' in data:
            conjoint.date_mariage = datetime.strptime(data['date_mariage'], '%Y-%m-%d').date()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Conjoint modifié avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_conjoint/<matricule>', methods=['DELETE'])
def api_supprimer_conjoint(matricule):
    """API pour supprimer le conjoint"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        if not personnel.conjoint:
            return jsonify({
                'success': False,
                'error': 'Aucun conjoint trouvé pour ce personnel'
            }), 404
        
        db.session.delete(personnel.conjoint)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Conjoint supprimé avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/modifier_enfant/<int:id_enfant>', methods=['PUT'])
def api_modifier_enfant(id_enfant):
    """API pour modifier les informations d'un enfant"""
    try:
        enfant = Enfant.query.get_or_404(id_enfant)
        data = request.get_json()
        
        # Mise à jour des champs modifiables
        if 'nom' in data:
            enfant.nom = data['nom']
        if 'prenom' in data:
            enfant.prenom = data['prenom']
        if 'sexe_id' in data:
            enfant.sexe_id = data['sexe_id']
        if 'lieu_naissance' in data:
            enfant.lieu_naissance = data['lieu_naissance']
        
        # Mise à jour des dates si fournies
        if 'date_naissance' in data:
            enfant.date_naissance = datetime.strptime(data['date_naissance'], '%Y-%m-%d').date()
        if 'date_deces' in data:
            if data['date_deces']:
                enfant.date_deces = datetime.strptime(data['date_deces'], '%Y-%m-%d').date()
            else:
                enfant.date_deces = None
        
        db.session.commit()
        
        # Mettre à jour le nombre d'enfants du personnel
        personnel = Personnel.query.get(enfant.matricule)
        personnel.nombre_enfants = len([e for e in personnel.enfants if e.est_vivant])
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Enfant modifié avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/supprimer_enfant/<int:id_enfant>', methods=['DELETE'])
def api_supprimer_enfant(id_enfant):
    """API pour supprimer un enfant"""
    try:
        enfant = Enfant.query.get_or_404(id_enfant)
        matricule = enfant.matricule
        
        db.session.delete(enfant)
        db.session.commit()
        
        # Mettre à jour le nombre d'enfants du personnel
        personnel = Personnel.query.get(matricule)
        personnel.nombre_enfants = len([e for e in personnel.enfants if e.est_vivant])
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Enfant supprimé avec succès'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/personnel/<matricule>/famille', methods=['GET'])
def api_get_famille_complete(matricule):
    """API pour récupérer toutes les informations familiales d'un personnel"""
    try:
        personnel = Personnel.query.get_or_404(matricule)
        
        # Informations du conjoint
        conjoint_data = None
        if personnel.conjoint:
            c = personnel.conjoint
            conjoint_data = {
                'id_conjoint': c.id_conjoint,
                'nom': c.nom,
                'prenom': c.prenom,
                'nom_arabe': c.nom_arabe,
                'prenom_arabe': c.prenom_arabe,
                'nom_complet': c.nom_complet,
                'nom_complet_arabe': c.nom_complet_arabe,
                'date_naissance': c.date_naissance.strftime('%Y-%m-%d'),
                'lieu_naissance': c.lieu_naissance,
                'lieu_naissance_arabe': c.lieu_naissance_arabe,
                'adresse': c.adresse,
                'adresse_arabe': c.adresse_arabe,
                'date_mariage': c.date_mariage.strftime('%Y-%m-%d'),
                'lieu_mariage': c.lieu_mariage,
                'profession': c.profession,
                'profession_arabe': c.profession_arabe,
                'numero_cin': c.numero_cin,
                'gsm': c.gsm,
                'nom_pere': c.nom_pere,
                'prenom_pere': c.prenom_pere,
                'nom_arabe_pere': c.nom_arabe_pere,
                'prenom_arabe_pere': c.prenom_arabe_pere,
                'nom_mere': c.nom_mere,
                'prenom_mere': c.prenom_mere,
                'nom_arabe_mere': c.nom_arabe_mere,
                'prenom_arabe_mere': c.prenom_arabe_mere,
                'profession_pere': c.profession_pere,
                'profession_mere': c.profession_mere
            }
        
        # Informations des enfants
        enfants_data = []
        for e in personnel.enfants:
            enfants_data.append({
                'id_enfant': e.id_enfant,
                'nom': e.nom,
                'prenom': e.prenom,
                'nom_complet': e.nom_complet,
                'sexe': e.genre.libelle,
                'sexe_id': e.sexe_id,
                'date_naissance': e.date_naissance.strftime('%Y-%m-%d'),
                'lieu_naissance': e.lieu_naissance,
                'date_deces': e.date_deces.strftime('%Y-%m-%d') if e.date_deces else None,
                'est_vivant': e.est_vivant,
                'age': (datetime.now().date() - e.date_naissance).days // 365 if e.est_vivant else None
            })
        
        # Statistiques familiales
        nb_enfants_vivants = len([e for e in personnel.enfants if e.est_vivant])
        nb_enfants_total = len(personnel.enfants)
        
        return jsonify({
            'success': True,
            'famille': {
                'conjoint': conjoint_data,
                'enfants': enfants_data,
                'statistiques': {
                    'a_conjoint': conjoint_data is not None,
                    'nb_enfants_vivants': nb_enfants_vivants,
                    'nb_enfants_total': nb_enfants_total,
                    'situation_familiale': personnel.situation_familiale.libelle
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/enfants_par_age', methods=['POST'])
def api_enfants_par_age():
    """API pour récupérer les enfants par tranche d'âge"""
    try:
        data = request.get_json()
        age_min = data.get('age_min', 0)
        age_max = data.get('age_max', 100)
        
        # Calculer les dates correspondantes
        today = datetime.now().date()
        date_max = today.replace(year=today.year - age_min)
        date_min = today.replace(year=today.year - age_max)
        
        # Requête pour les enfants vivants dans la tranche d'âge
        enfants = Enfant.query.filter(
            Enfant.date_deces.is_(None),  # Enfants vivants
            Enfant.date_naissance >= date_min,
            Enfant.date_naissance <= date_max
        ).all()
        
        enfants_data = []
        for e in enfants:
            age = (today - e.date_naissance).days // 365
            enfants_data.append({
                'id_enfant': e.id_enfant,
                'nom_complet': e.nom_complet,
                'sexe': e.genre.libelle,
                'age': age,
                'date_naissance': e.date_naissance.strftime('%d/%m/%Y'),
                'lieu_naissance': e.lieu_naissance,
                'personnel': {
                    'matricule': e.matricule,
                    'nom_complet': e.personnel.nom_complet,
                    'unite': e.personnel.unite.libelle,
                    'grade': e.personnel.grade_actuel.libelle
                }
            })
        
        return jsonify({
            'success': True,
            'enfants': enfants_data,
            'total': len(enfants_data),
            'criteres': {
                'age_min': age_min,
                'age_max': age_max
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@rh_bp.route('/api/statistiques_familiales', methods=['GET'])
def api_statistiques_familiales():
    """API pour récupérer les statistiques familiales globales"""
    try:
        # Statistiques générales
        total_personnel = Personnel.query.count()
        personnel_maries = Personnel.query.join(ReferentielSituationFamiliale).filter(
            ReferentielSituationFamiliale.libelle == 'Marié(e)'
        ).count()
        personnel_avec_conjoint = Personnel.query.join(Conjoint).count()
        
        # Statistiques des enfants
        total_enfants = Enfant.query.count()
        enfants_vivants = Enfant.query.filter(Enfant.date_deces.is_(None)).count()
        
        # Répartition par situation familiale
        stats_situation = db.session.query(
            ReferentielSituationFamiliale.libelle,
            db.func.count(Personnel.matricule).label('effectif')
        ).join(Personnel).group_by(ReferentielSituationFamiliale.libelle).all()
        
        # Répartition des enfants par sexe
        stats_enfants_sexe = db.session.query(
            ReferentielGenre.libelle,
            db.func.count(Enfant.id_enfant).label('effectif')
        ).join(Enfant).filter(Enfant.date_deces.is_(None)).group_by(ReferentielGenre.libelle).all()
        
        # Personnel avec le plus d'enfants
        personnel_max_enfants = db.session.query(
            Personnel.matricule,
            Personnel.nom,
            Personnel.prenom,
            db.func.count(Enfant.id_enfant).label('nb_enfants')
        ).join(Enfant).filter(Enfant.date_deces.is_(None)).group_by(
            Personnel.matricule, Personnel.nom, Personnel.prenom
        ).order_by(db.func.count(Enfant.id_enfant).desc()).limit(10).all()
        
        return jsonify({
            'success': True,
            'statistiques': {
                'personnel': {
                    'total': total_personnel,
                    'maries': personnel_maries,
                    'avec_conjoint': personnel_avec_conjoint,
                    'pourcentage_maries': round((personnel_maries / total_personnel) * 100, 2) if total_personnel > 0 else 0
                },
                'enfants': {
                    'total': total_enfants,
                    'vivants': enfants_vivants,
                    'deces': total_enfants - enfants_vivants,
                    'moyenne_par_personnel': round(enfants_vivants / total_personnel, 2) if total_personnel > 0 else 0
                },
                'repartition_situation': [{'situation': s[0], 'effectif': s[1]} for s in stats_situation],
                'repartition_enfants_sexe': [{'sexe': s[0], 'effectif': s[1]} for s in stats_enfants_sexe],
                'personnel_max_enfants': [
                    {
                        'matricule': p[0],
                        'nom_complet': f"{p[2]} {p[1]}",
                        'nb_enfants': p[3]
                    } for p in personnel_max_enfants
                ]
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
