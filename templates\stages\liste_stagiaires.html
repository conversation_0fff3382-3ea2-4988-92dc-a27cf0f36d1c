{% extends "stages/base_stages.html" %}

{% block title %}Liste des Stagiaires{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Gestion des Stagiaires</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="{{ url_for('ajouter_stagiaire') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-plus-circle me-1"></i>
                Ajouter un Stagiaire
            </a>
            <a href="{{ url_for('export_stagiaires_excel') }}" class="btn btn-sm btn-outline-success ms-2">
                <i class="fas fa-file-excel me-1"></i>
                Export Excel
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        {% for category, message in messages %}
          <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>
        {% endfor %}
      {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th scope="col">Photo</th>
                            <th scope="col">Nom</th>
                            <th scope="col">Prénom</th>
                            <th scope="col">Email</th>
                            <th scope="col">Date de naissance</th>
                            <th scope="col">Téléphone</th>
                            <th scope="col" class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stagiaire in stagiaires %}
                        <tr>
                            <td>
                                {% if stagiaire.photo %}
                                    <img src="{{ url_for('uploaded_file', filename=stagiaire.photo) }}" alt="Photo de {{ stagiaire.nom }}" class="img-thumbnail" style="width: 50px; height: 50px;">
                                {% else %}
                                    <img src="{{ url_for('static', filename='images/default_avatar.png') }}" alt="Avatar par défaut" class="img-thumbnail" style="width: 50px; height: 50px;">
                                {% endif %}
                            </td>
                            <td>{{ stagiaire.nom }}</td>
                            <td>{{ stagiaire.prenom }}</td>
                            <td>{{ stagiaire.email }}</td>
                            <td>{{ stagiaire.date_naissance.strftime('%d/%m/%Y') if stagiaire.date_naissance else '' }}</td>
                            <td>{{ stagiaire.telephone or '' }}</td>
                            <td class="text-center">
                                <a href="{{ url_for('modifier_stagiaire', id=stagiaire.id_stagiaire) }}" class="btn btn-warning btn-sm">Modifier</a>
                                <form action="{{ url_for('supprimer_stagiaire', id=stagiaire.id_stagiaire) }}" method="post" style="display:inline;">
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce stagiaire ?');">Supprimer</button>
                                </form>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center">Aucun stagiaire trouvé.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 