<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Courriers</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 Test Simple - Courriers Arrivés</h1>
    
    <div id="status" class="status info">
        🔄 Chargement en cours...
    </div>
    
    <div>
        <strong>Nombre de courriers:</strong> <span id="count">0</span>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Date Arrivée</th>
                <th>Urgence</th>
                <th>Nature</th>
                <th>Expéditeur</th>
                <th>Objet</th>
            </tr>
        </thead>
        <tbody id="courriers-tbody">
            <tr>
                <td colspan="6">Chargement...</td>
            </tr>
        </tbody>
    </table>
    
    <h2>🔍 Logs de Débogage</h2>
    <div id="logs" style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto;">
    </div>

    <script>
        // Variables globales
        let courriers = [];
        
        // Fonction de log
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logsDiv = document.getElementById('logs');
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logsDiv.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        // Fonction pour mettre à jour le statut
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        // Fonction pour charger les courriers
        async function loadCourriers() {
            log('🚀 Début du chargement des courriers');
            updateStatus('🔄 Chargement des courriers...', 'info');
            
            try {
                log('📡 Appel API: /api/courriers/arrives');
                const response = await fetch('/api/courriers/arrives');
                
                log(`📊 Réponse API: Status ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                courriers = await response.json();
                log(`✅ ${courriers.length} courriers récupérés`, 'success');
                
                // Mettre à jour l'affichage
                updateCount();
                renderTable();
                updateStatus(`✅ ${courriers.length} courriers chargés`, 'success');
                
            } catch (error) {
                log(`❌ Erreur: ${error.message}`, 'error');
                updateStatus(`❌ Erreur: ${error.message}`, 'error');
                
                // Afficher une ligne d'erreur dans le tableau
                const tbody = document.getElementById('courriers-tbody');
                tbody.innerHTML = `<tr><td colspan="6" style="color: red;">Erreur: ${error.message}</td></tr>`;
            }
        }
        
        // Fonction pour mettre à jour le compteur
        function updateCount() {
            document.getElementById('count').textContent = courriers.length;
            log(`📊 Compteur mis à jour: ${courriers.length}`);
        }
        
        // Fonction pour rendre le tableau
        function renderTable() {
            log('🎨 Rendu du tableau');
            const tbody = document.getElementById('courriers-tbody');
            
            if (courriers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6">Aucun courrier trouvé</td></tr>';
                return;
            }
            
            let html = '';
            courriers.forEach((courrier, index) => {
                html += `
                    <tr>
                        <td><strong>${courrier.id}</strong></td>
                        <td>${courrier.date_arrivee}</td>
                        <td>
                            <span style="background: ${getUrgenceColor(courrier.urgence)}; color: white; padding: 2px 6px; border-radius: 3px;">
                                ${courrier.urgence}
                            </span>
                        </td>
                        <td>${courrier.nature}</td>
                        <td>${courrier.expediteur}</td>
                        <td>${courrier.objet}</td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
            log(`✅ Tableau rendu avec ${courriers.length} lignes`, 'success');
        }
        
        // Fonction pour obtenir la couleur de l'urgence
        function getUrgenceColor(urgence) {
            const colors = {
                'extreme': '#dc3545',
                'extreme_urgent': '#fd7e14', 
                'urgent': '#ffc107',
                'routine': '#28a745'
            };
            return colors[urgence] || '#6c757d';
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page chargée, début de l\'initialisation');
            
            // Test des fonctionnalités de base
            log('🔧 Test des fonctionnalités JavaScript');
            
            if (typeof fetch === 'undefined') {
                log('❌ fetch() non disponible', 'error');
                updateStatus('❌ fetch() non disponible', 'error');
                return;
            } else {
                log('✅ fetch() disponible', 'success');
            }
            
            if (typeof Promise === 'undefined') {
                log('❌ Promise non disponible', 'error');
                updateStatus('❌ Promise non disponible', 'error');
                return;
            } else {
                log('✅ Promise disponible', 'success');
            }
            
            // Charger les courriers
            loadCourriers();
        });
        
        // Gestion des erreurs globales
        window.addEventListener('error', function(event) {
            log(`❌ Erreur JavaScript: ${event.error.message}`, 'error');
            updateStatus(`❌ Erreur JavaScript: ${event.error.message}`, 'error');
        });
        
        // Test périodique (toutes les 10 secondes)
        setInterval(function() {
            log('🔄 Test périodique - rechargement des données');
            loadCourriers();
        }, 10000);
    </script>
</body>
</html>
